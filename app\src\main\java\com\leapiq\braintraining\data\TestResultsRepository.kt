package com.leapiq.braintraining.data

import android.content.Context
import com.leapiq.braintraining.data.model.TestResult
import com.leapiq.braintraining.data.model.TestType
import java.util.*
import kotlin.math.abs

/**
 * Repository for advanced test results operations and analytics
 */
class TestResultsRepository private constructor(context: Context) {
    
    private val progressManager = TestProgressManager.getInstance(context)
    
    companion object {
        @Volatile
        private var INSTANCE: TestResultsRepository? = null
        
        fun getInstance(context: Context): TestResultsRepository {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TestResultsRepository(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        // Test ID mappings
        const val MEMORY_ASSESSMENT = "memory_assessment"
        const val ATTENTION_TEST = "attention_test"
        const val PROCESSING_SPEED = "processing_speed"
        const val LEARNING_STYLE = "learning_style"
        const val STRESS_RESPONSE = "stress_response"
        const val PROBLEM_SOLVING = "problem_solving"
    }
    
    /**
     * Get test results for a specific test
     */
    fun getTestResults(testId: String): List<TestResult> {
        return progressManager.getTestResults(testId)
    }

    /**
     * Get comprehensive dashboard data
     */
    fun getDashboardData(): TestDashboardData {
        val allResults = progressManager.getAllTestResults()
        val cognitiveTests = listOf(MEMORY_ASSESSMENT, ATTENTION_TEST, PROCESSING_SPEED)
        val personalityTests = listOf(LEARNING_STYLE, STRESS_RESPONSE, PROBLEM_SOLVING)
        
        val cognitiveResults = cognitiveTests.flatMap { allResults[it] ?: emptyList() }
        val personalityResults = personalityTests.flatMap { allResults[it] ?: emptyList() }
        
        return TestDashboardData(
            totalTestsCompleted = allResults.values.sumOf { it.size },
            cognitiveTestsCompleted = cognitiveResults.size,
            personalityTestsCompleted = personalityResults.size,
            averageCognitiveScore = if (cognitiveResults.isNotEmpty()) cognitiveResults.map { it.score }.average() else 0.0,
            averagePersonalityScore = if (personalityResults.isNotEmpty()) personalityResults.map { it.score }.average() else 0.0,
            recentActivity = getRecentActivity(),
            strongestAreas = getStrongestAreas(),
            improvementAreas = getImprovementAreas(),
            overallProgress = calculateOverallProgress()
        )
    }
    
    /**
     * Get recent activity across all tests
     */
    fun getRecentActivity(days: Int = 7): List<ActivityItem> {
        val cutoffDate = Calendar.getInstance().apply {
            add(Calendar.DAY_OF_YEAR, -days)
        }.time
        
        val allResults = progressManager.getAllTestResults()
        val recentResults = mutableListOf<Pair<String, TestResult>>()
        
        allResults.forEach { (testId, results) ->
            results.filter { it.completedAt.after(cutoffDate) }
                .forEach { result -> recentResults.add(testId to result) }
        }
        
        return recentResults
            .sortedByDescending { it.second.completedAt }
            .take(10)
            .map { (testId, result) ->
                ActivityItem(
                    testId = testId,
                    testName = getTestName(testId),
                    score = result.score,
                    completedAt = result.completedAt,
                    activityType = if (result.score > getPreviousScore(testId, result.completedAt)) 
                        ActivityType.IMPROVEMENT else ActivityType.COMPLETION
                )
            }
    }
    
    /**
     * Get strongest performance areas
     */
    fun getStrongestAreas(): List<StrengthArea> {
        val allProgresses = progressManager.getAllTestProgresses()
        
        return allProgresses.map { (testId, progress) ->
            StrengthArea(
                testId = testId,
                testName = getTestName(testId),
                averageScore = progress.averageScore,
                bestScore = progress.bestScore,
                consistency = calculateConsistency(testId)
            )
        }.sortedByDescending { it.averageScore }
    }
    
    /**
     * Get areas needing improvement
     */
    fun getImprovementAreas(): List<ImprovementArea> {
        val allProgresses = progressManager.getAllTestProgresses()
        
        return allProgresses.map { (testId, progress) ->
            val trend = progressManager.getPerformanceTrends(testId)
            ImprovementArea(
                testId = testId,
                testName = getTestName(testId),
                currentScore = progress.averageScore,
                trendDirection = trend.trendDirection,
                recommendedActions = getRecommendedActions(testId, progress.averageScore)
            )
        }.filter { it.currentScore < 70 || it.trendDirection == TrendDirection.DECLINING }
         .sortedBy { it.currentScore }
    }
    
    /**
     * Calculate overall progress across all tests
     */
    fun calculateOverallProgress(): OverallProgress {
        val allProgresses = progressManager.getAllTestProgresses()
        val completedTests = allProgresses.filter { it.value.timesCompleted > 0 }
        
        if (completedTests.isEmpty()) {
            return OverallProgress(
                completionPercentage = 0.0,
                averageScore = 0.0,
                totalTestsTaken = 0,
                improvingTests = 0,
                stableTests = 0,
                decliningTests = 0
            )
        }
        
        val trends = completedTests.map { (testId, _) ->
            progressManager.getPerformanceTrends(testId)
        }
        
        return OverallProgress(
            completionPercentage = (completedTests.size.toDouble() / 6) * 100,
            averageScore = completedTests.values.map { it.averageScore }.average(),
            totalTestsTaken = completedTests.values.sumOf { it.timesCompleted },
            improvingTests = trends.count { it.trendDirection == TrendDirection.IMPROVING },
            stableTests = trends.count { it.trendDirection == TrendDirection.STABLE },
            decliningTests = trends.count { it.trendDirection == TrendDirection.DECLINING }
        )
    }
    
    /**
     * Get comparative analysis between tests
     */
    fun getComparativeAnalysis(): ComparativeAnalysis {
        val cognitiveTests = listOf(MEMORY_ASSESSMENT, ATTENTION_TEST, PROCESSING_SPEED)
        val personalityTests = listOf(LEARNING_STYLE, STRESS_RESPONSE, PROBLEM_SOLVING)
        
        val cognitiveScores = cognitiveTests.mapNotNull { testId ->
            val progress = progressManager.getTestProgress(testId)
            if (progress.timesCompleted > 0) progress.averageScore else null
        }
        
        val personalityScores = personalityTests.mapNotNull { testId ->
            val progress = progressManager.getTestProgress(testId)
            if (progress.timesCompleted > 0) progress.averageScore else null
        }
        
        return ComparativeAnalysis(
            cognitiveVsPersonality = CognitivePersonalityComparison(
                cognitiveAverage = if (cognitiveScores.isNotEmpty()) cognitiveScores.average() else 0.0,
                personalityAverage = if (personalityScores.isNotEmpty()) personalityScores.average() else 0.0,
                preferredTestType = if (cognitiveScores.average() > personalityScores.average()) 
                    TestType.COGNITIVE else TestType.PERSONALITY
            ),
            testRankings = getTestRankings(),
            correlations = calculateTestCorrelations()
        )
    }
    
    /**
     * Get personalized insights based on all test data
     */
    fun getPersonalizedInsights(): List<PersonalizedInsight> {
        val insights = mutableListOf<PersonalizedInsight>()
        val allProgresses = progressManager.getAllTestProgresses()
        
        // Performance insights
        val bestTest = allProgresses.maxByOrNull { it.value.averageScore }
        val worstTest = allProgresses.minByOrNull { it.value.averageScore }
        
        bestTest?.let { (testId, progress) ->
            if (progress.timesCompleted > 0) {
                insights.add(PersonalizedInsight(
                    type = InsightType.STRENGTH,
                    title = "Your Strongest Area",
                    description = "You excel at ${getTestName(testId)} with an average score of ${progress.averageScore.toInt()}%",
                    actionable = "Continue practicing this area to maintain your strength",
                    priority = InsightPriority.LOW
                ))
            }
        }
        
        worstTest?.let { (testId, progress) ->
            if (progress.timesCompleted > 0 && progress.averageScore < 60) {
                insights.add(PersonalizedInsight(
                    type = InsightType.IMPROVEMENT_NEEDED,
                    title = "Focus Area Identified",
                    description = "${getTestName(testId)} shows room for improvement with an average score of ${progress.averageScore.toInt()}%",
                    actionable = "Dedicate extra practice time to this area",
                    priority = InsightPriority.HIGH
                ))
            }
        }
        
        // Trend insights
        allProgresses.forEach { (testId, progress) ->
            if (progress.timesCompleted >= 3) {
                val trend = progressManager.getPerformanceTrends(testId)
                when (trend.trendDirection) {
                    TrendDirection.IMPROVING -> {
                        insights.add(PersonalizedInsight(
                            type = InsightType.POSITIVE_TREND,
                            title = "Great Progress!",
                            description = "Your ${getTestName(testId)} scores are improving consistently",
                            actionable = "Keep up the excellent work",
                            priority = InsightPriority.MEDIUM
                        ))
                    }
                    TrendDirection.DECLINING -> {
                        insights.add(PersonalizedInsight(
                            type = InsightType.NEGATIVE_TREND,
                            title = "Performance Decline",
                            description = "Your ${getTestName(testId)} scores have been declining recently",
                            actionable = "Consider reviewing your approach or taking a break",
                            priority = InsightPriority.HIGH
                        ))
                    }
                    TrendDirection.STABLE -> {
                        if (progress.averageScore > 80) {
                            insights.add(PersonalizedInsight(
                                type = InsightType.MASTERY,
                                title = "Consistent Excellence",
                                description = "You maintain consistently high performance in ${getTestName(testId)}",
                                actionable = "Consider challenging yourself with advanced variations",
                                priority = InsightPriority.LOW
                            ))
                        }
                    }
                }
            }
        }
        
        return insights.sortedByDescending { it.priority.ordinal }
    }
    
    // Helper methods
    private fun getTestName(testId: String): String {
        return when (testId) {
            MEMORY_ASSESSMENT -> "Memory Assessment"
            ATTENTION_TEST -> "Attention Test"
            PROCESSING_SPEED -> "Processing Speed"
            LEARNING_STYLE -> "Learning Style"
            STRESS_RESPONSE -> "Stress Response"
            PROBLEM_SOLVING -> "Problem Solving Style"
            else -> "Unknown Test"
        }
    }
    
    private fun getPreviousScore(testId: String, currentDate: Date): Int {
        val results = progressManager.getTestResults(testId)
            .filter { it.completedAt.before(currentDate) }
            .sortedByDescending { it.completedAt }
        
        return results.firstOrNull()?.score ?: 0
    }
    
    private fun calculateConsistency(testId: String): Double {
        val results = progressManager.getTestResults(testId)
        if (results.size < 2) return 100.0
        
        val scores = results.map { it.score.toDouble() }
        val mean = scores.average()
        val variance = scores.map { (it - mean) * (it - mean) }.average()
        val standardDeviation = kotlin.math.sqrt(variance)
        
        return maxOf(0.0, 100.0 - (standardDeviation * 2))
    }
    
    private fun getRecommendedActions(testId: String, currentScore: Double): List<String> {
        val actions = mutableListOf<String>()

        // Add test-specific recommendations
        val testSpecificActions = when (testId) {
            "attention_test" -> listOf("Practice focus exercises", "Reduce distractions during practice")
            "memory_assessment" -> listOf("Use memory techniques like visualization", "Practice recall exercises")
            "learning_style" -> listOf("Apply your learning style to study methods", "Experiment with different approaches")
            "problem_solving_style" -> listOf("Practice with different problem types", "Develop systematic approaches")
            "stress_response" -> listOf("Practice stress management techniques", "Learn relaxation methods")
            else -> emptyList()
        }

        when {
            currentScore < 50 -> {
                actions.add("Review test instructions and practice regularly")
                actions.add("Focus on understanding the fundamentals")
                actions.addAll(testSpecificActions)
            }
            currentScore < 70 -> {
                actions.add("Practice specific weak areas identified in results")
                actions.add("Try different strategies and approaches")
            }
            else -> {
                actions.add("Maintain current performance level")
                actions.add("Challenge yourself with advanced techniques")
            }
        }
        
        return actions
    }
    
    private fun getTestRankings(): List<TestRanking> {
        val allProgresses = progressManager.getAllTestProgresses()
        
        return allProgresses.map { (testId, progress) ->
            TestRanking(
                testId = testId,
                testName = getTestName(testId),
                averageScore = progress.averageScore,
                rank = 0 // Will be set after sorting
            )
        }.sortedByDescending { it.averageScore }
         .mapIndexed { index, ranking -> ranking.copy(rank = index + 1) }
    }
    
    private fun calculateTestCorrelations(): List<TestCorrelation> {
        // Simplified correlation calculation
        // In a real implementation, you'd calculate Pearson correlation coefficients
        return emptyList() // Placeholder for now
    }
}

/**
 * Data classes for enhanced test analytics
 */
data class TestDashboardData(
    val totalTestsCompleted: Int,
    val cognitiveTestsCompleted: Int,
    val personalityTestsCompleted: Int,
    val averageCognitiveScore: Double,
    val averagePersonalityScore: Double,
    val recentActivity: List<ActivityItem>,
    val strongestAreas: List<StrengthArea>,
    val improvementAreas: List<ImprovementArea>,
    val overallProgress: OverallProgress
)

data class ActivityItem(
    val testId: String,
    val testName: String,
    val score: Int,
    val completedAt: Date,
    val activityType: ActivityType
)

enum class ActivityType {
    COMPLETION,
    IMPROVEMENT,
    MILESTONE
}

data class StrengthArea(
    val testId: String,
    val testName: String,
    val averageScore: Double,
    val bestScore: Int,
    val consistency: Double
)

data class ImprovementArea(
    val testId: String,
    val testName: String,
    val currentScore: Double,
    val trendDirection: TrendDirection,
    val recommendedActions: List<String>
)

data class OverallProgress(
    val completionPercentage: Double,
    val averageScore: Double,
    val totalTestsTaken: Int,
    val improvingTests: Int,
    val stableTests: Int,
    val decliningTests: Int
)

data class ComparativeAnalysis(
    val cognitiveVsPersonality: CognitivePersonalityComparison,
    val testRankings: List<TestRanking>,
    val correlations: List<TestCorrelation>
)

data class CognitivePersonalityComparison(
    val cognitiveAverage: Double,
    val personalityAverage: Double,
    val preferredTestType: TestType
)

data class TestRanking(
    val testId: String,
    val testName: String,
    val averageScore: Double,
    val rank: Int
)

data class TestCorrelation(
    val test1Id: String,
    val test2Id: String,
    val correlationCoefficient: Double,
    val significance: CorrelationSignificance
)

enum class CorrelationSignificance {
    STRONG,
    MODERATE,
    WEAK,
    NONE
}

data class PersonalizedInsight(
    val type: InsightType,
    val title: String,
    val description: String,
    val actionable: String,
    val priority: InsightPriority
)

enum class InsightType {
    STRENGTH,
    IMPROVEMENT_NEEDED,
    POSITIVE_TREND,
    NEGATIVE_TREND,
    MASTERY,
    MILESTONE
}

enum class InsightPriority {
    HIGH,
    MEDIUM,
    LOW
}


