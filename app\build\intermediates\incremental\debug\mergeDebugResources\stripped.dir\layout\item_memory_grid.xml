<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:layout_margin="2dp">

    <View
        android:id="@+id/grid_item"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/memory_grid_default"
        android:clickable="true"
        android:focusable="true" />

    <TextView
        android:id="@+id/grid_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:text="1"
        android:textColor="@color/text_primary"
        android:textSize="12sp"
        android:visibility="gone" />

</FrameLayout>
