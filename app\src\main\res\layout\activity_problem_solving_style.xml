<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/surface_white"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="Problem Solving Style Test"
        app:titleTextColor="@color/text_primary" />

    <!-- Progress Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/surface_white">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/progress_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Question 1 of 12"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Timer Container -->
            <LinearLayout
                android:id="@+id/timer_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/rounded_background"
                android:padding="8dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="⏱️"
                    android:textSize="16sp"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/timer_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="60s"
                    android:textColor="@android:color/holo_blue_dark"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

        <ProgressBar
            android:id="@+id/progress_bar"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginTop="8dp"
            android:progressTint="@color/primary_light_blue" />

    </LinearLayout>

    <!-- Instructions -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:text="You'll face various problem-solving scenarios and puzzles. Choose the approach that best represents how you would naturally handle each situation. Some questions are timed to assess your problem-solving under pressure."
        android:textColor="@color/text_primary"
        android:textSize="14sp"
        android:gravity="center"
        android:background="@drawable/rounded_background"
        android:padding="16dp" />

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Question Header -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:id="@+id/question_number_text"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Question 1"
                            android:textColor="@color/primary_light_blue"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/question_type_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Logic Puzzle"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp"
                            android:background="@drawable/rounded_background"
                            android:padding="6dp" />

                    </LinearLayout>

                    <!-- Problem Statement -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Problem:"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="4dp" />

                    <TextView
                        android:id="@+id/problem_statement"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Your team needs to solve a complex technical problem with a tight deadline."
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:lineSpacingExtra="4dp"
                        android:layout_marginBottom="16dp"
                        android:background="@color/background_light_gray"
                        android:padding="12dp" />

                    <!-- Question -->
                    <TextView
                        android:id="@+id/question_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="What's your preferred approach to tackle this challenge?"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Options -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Select your preferred approach:"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:layout_marginBottom="12dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/options_recycler"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Problem Solving Styles Info -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/primary_light_blue_light">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Problem Solving Styles"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="4">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📊"
                                android:textSize="20sp"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Analytical"
                                android:textColor="@color/text_primary"
                                android:textSize="11sp"
                                android:textStyle="bold"
                                android:gravity="center" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="💡"
                                android:textSize="20sp"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Intuitive"
                                android:textColor="@color/text_primary"
                                android:textSize="11sp"
                                android:textStyle="bold"
                                android:gravity="center" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="📋"
                                android:textSize="20sp"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Systematic"
                                android:textColor="@color/text_primary"
                                android:textSize="11sp"
                                android:textStyle="bold"
                                android:gravity="center" />

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🎨"
                                android:textSize="20sp"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Creative"
                                android:textColor="@color/text_primary"
                                android:textSize="11sp"
                                android:textStyle="bold"
                                android:gravity="center" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

    <!-- Navigation Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/surface_white"
        android:elevation="4dp">

        <Button
            android:id="@+id/btn_next"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="Next"
            android:textColor="@color/surface_white"
            app:backgroundTint="@color/primary_light_blue"
            android:visibility="gone" />

        <Button
            android:id="@+id/btn_submit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="Submit"
            android:textColor="@color/surface_white"
            app:backgroundTint="@color/success_green"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
