<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Test Type Icon -->
        <TextView
            android:id="@+id/test_type_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="🧠"
            android:textSize="24sp"
            android:layout_marginEnd="12dp" />

        <!-- Test Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/test_name_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Memory Assessment"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="2dp" />

            <TextView
                android:id="@+id/date_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Dec 15, 2023 at 14:30"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/accuracy_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Accuracy: 85%"
                android:textColor="@color/text_secondary"
                android:textSize="11sp" />

            <TextView
                android:id="@+id/time_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Time: 5m 30s"
                android:textColor="@color/text_secondary"
                android:textSize="11sp" />

        </LinearLayout>

        <!-- Score -->
        <TextView
            android:id="@+id/score_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="85%"
            android:textColor="@color/success_green"
            android:textSize="20sp"
            android:textStyle="bold"
            android:layout_marginStart="12dp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
