package com.leapiq.braintraining.ui.games.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.ui.games.model.WordSearchGrid

/**
 * Adapter for the word search grid
 * Displays letters in a grid layout
 */
class WordSearchGridAdapter(
    private var grid: WordSearchGrid,
    private val onCellClicked: (Int, Int) -> Unit
) : RecyclerView.Adapter<WordSearchGridAdapter.GridCellViewHolder>() {

    class GridCellViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val letterText: TextView = itemView.findViewById(R.id.letter_text)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GridCellViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_word_search_cell, parent, false)
        return GridCellViewHolder(view)
    }

    override fun onBindViewHolder(holder: GridCellViewHolder, position: Int) {
        val row = position / grid.size
        val col = position % grid.size
        val letter = grid.getCell(row, col)
        
        holder.letterText.text = letter.toString()
        
        // Set click listener
        holder.itemView.setOnClickListener {
            onCellClicked(row, col)
        }
    }

    override fun getItemCount() = grid.size * grid.size

    fun updateGrid(newGrid: WordSearchGrid) {
        grid = newGrid
        notifyDataSetChanged()
    }
}
