// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityVocabularyBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnMenu;

  @NonNull
  public final Button btnOption1;

  @NonNull
  public final Button btnOption2;

  @NonNull
  public final Button btnOption3;

  @NonNull
  public final Button btnOption4;

  @NonNull
  public final ImageButton btnQuit;

  @NonNull
  public final TextView gameTitle;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final TextView levelText;

  @NonNull
  public final TextView modeText;

  @NonNull
  public final TextView promptDisplay;

  @NonNull
  public final TextView questionText;

  @NonNull
  public final TextView roundText;

  @NonNull
  public final TextView trialText;

  private ActivityVocabularyBinding(@NonNull LinearLayout rootView, @NonNull ImageButton btnMenu,
      @NonNull Button btnOption1, @NonNull Button btnOption2, @NonNull Button btnOption3,
      @NonNull Button btnOption4, @NonNull ImageButton btnQuit, @NonNull TextView gameTitle,
      @NonNull TextView instructionText, @NonNull TextView levelText, @NonNull TextView modeText,
      @NonNull TextView promptDisplay, @NonNull TextView questionText, @NonNull TextView roundText,
      @NonNull TextView trialText) {
    this.rootView = rootView;
    this.btnMenu = btnMenu;
    this.btnOption1 = btnOption1;
    this.btnOption2 = btnOption2;
    this.btnOption3 = btnOption3;
    this.btnOption4 = btnOption4;
    this.btnQuit = btnQuit;
    this.gameTitle = gameTitle;
    this.instructionText = instructionText;
    this.levelText = levelText;
    this.modeText = modeText;
    this.promptDisplay = promptDisplay;
    this.questionText = questionText;
    this.roundText = roundText;
    this.trialText = trialText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityVocabularyBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityVocabularyBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_vocabulary, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityVocabularyBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_menu;
      ImageButton btnMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnMenu == null) {
        break missingId;
      }

      id = R.id.btn_option1;
      Button btnOption1 = ViewBindings.findChildViewById(rootView, id);
      if (btnOption1 == null) {
        break missingId;
      }

      id = R.id.btn_option2;
      Button btnOption2 = ViewBindings.findChildViewById(rootView, id);
      if (btnOption2 == null) {
        break missingId;
      }

      id = R.id.btn_option3;
      Button btnOption3 = ViewBindings.findChildViewById(rootView, id);
      if (btnOption3 == null) {
        break missingId;
      }

      id = R.id.btn_option4;
      Button btnOption4 = ViewBindings.findChildViewById(rootView, id);
      if (btnOption4 == null) {
        break missingId;
      }

      id = R.id.btn_quit;
      ImageButton btnQuit = ViewBindings.findChildViewById(rootView, id);
      if (btnQuit == null) {
        break missingId;
      }

      id = R.id.game_title;
      TextView gameTitle = ViewBindings.findChildViewById(rootView, id);
      if (gameTitle == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.level_text;
      TextView levelText = ViewBindings.findChildViewById(rootView, id);
      if (levelText == null) {
        break missingId;
      }

      id = R.id.mode_text;
      TextView modeText = ViewBindings.findChildViewById(rootView, id);
      if (modeText == null) {
        break missingId;
      }

      id = R.id.prompt_display;
      TextView promptDisplay = ViewBindings.findChildViewById(rootView, id);
      if (promptDisplay == null) {
        break missingId;
      }

      id = R.id.question_text;
      TextView questionText = ViewBindings.findChildViewById(rootView, id);
      if (questionText == null) {
        break missingId;
      }

      id = R.id.round_text;
      TextView roundText = ViewBindings.findChildViewById(rootView, id);
      if (roundText == null) {
        break missingId;
      }

      id = R.id.trial_text;
      TextView trialText = ViewBindings.findChildViewById(rootView, id);
      if (trialText == null) {
        break missingId;
      }

      return new ActivityVocabularyBinding((LinearLayout) rootView, btnMenu, btnOption1, btnOption2,
          btnOption3, btnOption4, btnQuit, gameTitle, instructionText, levelText, modeText,
          promptDisplay, questionText, roundText, trialText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
