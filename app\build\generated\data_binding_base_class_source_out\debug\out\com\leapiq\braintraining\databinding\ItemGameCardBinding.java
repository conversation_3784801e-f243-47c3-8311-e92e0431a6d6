// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemGameCardBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView gameCategory;

  @NonNull
  public final ImageView gameImage;

  @NonNull
  public final TextView gameName;

  @NonNull
  public final ProgressBar gameProgress;

  @NonNull
  public final ImageView lockIcon;

  @NonNull
  public final TextView premiumBadge;

  @NonNull
  public final TextView progressText;

  private ItemGameCardBinding(@NonNull CardView rootView, @NonNull TextView gameCategory,
      @NonNull ImageView gameImage, @NonNull TextView gameName, @NonNull ProgressBar gameProgress,
      @NonNull ImageView lockIcon, @NonNull TextView premiumBadge, @NonNull TextView progressText) {
    this.rootView = rootView;
    this.gameCategory = gameCategory;
    this.gameImage = gameImage;
    this.gameName = gameName;
    this.gameProgress = gameProgress;
    this.lockIcon = lockIcon;
    this.premiumBadge = premiumBadge;
    this.progressText = progressText;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemGameCardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemGameCardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_game_card, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemGameCardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.game_category;
      TextView gameCategory = ViewBindings.findChildViewById(rootView, id);
      if (gameCategory == null) {
        break missingId;
      }

      id = R.id.game_image;
      ImageView gameImage = ViewBindings.findChildViewById(rootView, id);
      if (gameImage == null) {
        break missingId;
      }

      id = R.id.game_name;
      TextView gameName = ViewBindings.findChildViewById(rootView, id);
      if (gameName == null) {
        break missingId;
      }

      id = R.id.game_progress;
      ProgressBar gameProgress = ViewBindings.findChildViewById(rootView, id);
      if (gameProgress == null) {
        break missingId;
      }

      id = R.id.lock_icon;
      ImageView lockIcon = ViewBindings.findChildViewById(rootView, id);
      if (lockIcon == null) {
        break missingId;
      }

      id = R.id.premium_badge;
      TextView premiumBadge = ViewBindings.findChildViewById(rootView, id);
      if (premiumBadge == null) {
        break missingId;
      }

      id = R.id.progress_text;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      return new ItemGameCardBinding((CardView) rootView, gameCategory, gameImage, gameName,
          gameProgress, lockIcon, premiumBadge, progressText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
