<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_white"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="40dp"
        android:paddingTop="60dp"
        android:paddingBottom="80dp">

        <!-- Welcome Icon with background circle -->
        <FrameLayout
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:layout_marginBottom="40dp">

            <!-- Background circle -->
            <View
                android:layout_width="140dp"
                android:layout_height="140dp"
                android:background="@drawable/icon_background_circle" />

            <!-- Icon -->
            <ImageView
                android:id="@+id/welcomeIcon"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_home"
                android:tint="@color/primary_light_blue"
                android:contentDescription="Welcome icon" />
        </FrameLayout>

        <!-- Welcome Title -->
        <TextView
            android:id="@+id/welcomeTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:text="Welcome to LeapIQ"
            android:textColor="@color/text_primary"
            android:textSize="32sp"
            android:textStyle="bold"
            android:gravity="center"
            android:fontFamily="sans-serif-medium" />

        <!-- Welcome Description -->
        <TextView
            android:id="@+id/welcomeDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:text="Train your brain with scientifically-designed games and assessments."
            android:textColor="@color/text_secondary"
            android:textSize="18sp"
            android:lineSpacingExtra="6dp"
            android:gravity="center"
            android:fontFamily="sans-serif" />

    </LinearLayout>

</ScrollView>
