// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTestProgressBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView averageScoreText;

  @NonNull
  public final TextView averageTimeText;

  @NonNull
  public final TextView bestScoreText;

  @NonNull
  public final TextView consistencyText;

  @NonNull
  public final ScrollView contentContainer;

  @NonNull
  public final LinearLayout emptyStateContainer;

  @NonNull
  public final TextView fastestTimeText;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView recommendationDescription;

  @NonNull
  public final TextView recommendationTitle;

  @NonNull
  public final MaterialCardView recommendationsContainer;

  @NonNull
  public final RecyclerView resultsRecycler;

  @NonNull
  public final TextView streakText;

  @NonNull
  public final TextView timesCompletedText;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView trendDescription;

  @NonNull
  public final TextView trendIndicator;

  private ActivityTestProgressBinding(@NonNull LinearLayout rootView,
      @NonNull TextView averageScoreText, @NonNull TextView averageTimeText,
      @NonNull TextView bestScoreText, @NonNull TextView consistencyText,
      @NonNull ScrollView contentContainer, @NonNull LinearLayout emptyStateContainer,
      @NonNull TextView fastestTimeText, @NonNull ProgressBar progressBar,
      @NonNull TextView recommendationDescription, @NonNull TextView recommendationTitle,
      @NonNull MaterialCardView recommendationsContainer, @NonNull RecyclerView resultsRecycler,
      @NonNull TextView streakText, @NonNull TextView timesCompletedText, @NonNull Toolbar toolbar,
      @NonNull TextView trendDescription, @NonNull TextView trendIndicator) {
    this.rootView = rootView;
    this.averageScoreText = averageScoreText;
    this.averageTimeText = averageTimeText;
    this.bestScoreText = bestScoreText;
    this.consistencyText = consistencyText;
    this.contentContainer = contentContainer;
    this.emptyStateContainer = emptyStateContainer;
    this.fastestTimeText = fastestTimeText;
    this.progressBar = progressBar;
    this.recommendationDescription = recommendationDescription;
    this.recommendationTitle = recommendationTitle;
    this.recommendationsContainer = recommendationsContainer;
    this.resultsRecycler = resultsRecycler;
    this.streakText = streakText;
    this.timesCompletedText = timesCompletedText;
    this.toolbar = toolbar;
    this.trendDescription = trendDescription;
    this.trendIndicator = trendIndicator;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTestProgressBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTestProgressBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_test_progress, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTestProgressBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.average_score_text;
      TextView averageScoreText = ViewBindings.findChildViewById(rootView, id);
      if (averageScoreText == null) {
        break missingId;
      }

      id = R.id.average_time_text;
      TextView averageTimeText = ViewBindings.findChildViewById(rootView, id);
      if (averageTimeText == null) {
        break missingId;
      }

      id = R.id.best_score_text;
      TextView bestScoreText = ViewBindings.findChildViewById(rootView, id);
      if (bestScoreText == null) {
        break missingId;
      }

      id = R.id.consistency_text;
      TextView consistencyText = ViewBindings.findChildViewById(rootView, id);
      if (consistencyText == null) {
        break missingId;
      }

      id = R.id.content_container;
      ScrollView contentContainer = ViewBindings.findChildViewById(rootView, id);
      if (contentContainer == null) {
        break missingId;
      }

      id = R.id.empty_state_container;
      LinearLayout emptyStateContainer = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateContainer == null) {
        break missingId;
      }

      id = R.id.fastest_time_text;
      TextView fastestTimeText = ViewBindings.findChildViewById(rootView, id);
      if (fastestTimeText == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.recommendation_description;
      TextView recommendationDescription = ViewBindings.findChildViewById(rootView, id);
      if (recommendationDescription == null) {
        break missingId;
      }

      id = R.id.recommendation_title;
      TextView recommendationTitle = ViewBindings.findChildViewById(rootView, id);
      if (recommendationTitle == null) {
        break missingId;
      }

      id = R.id.recommendations_container;
      MaterialCardView recommendationsContainer = ViewBindings.findChildViewById(rootView, id);
      if (recommendationsContainer == null) {
        break missingId;
      }

      id = R.id.results_recycler;
      RecyclerView resultsRecycler = ViewBindings.findChildViewById(rootView, id);
      if (resultsRecycler == null) {
        break missingId;
      }

      id = R.id.streak_text;
      TextView streakText = ViewBindings.findChildViewById(rootView, id);
      if (streakText == null) {
        break missingId;
      }

      id = R.id.times_completed_text;
      TextView timesCompletedText = ViewBindings.findChildViewById(rootView, id);
      if (timesCompletedText == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.trend_description;
      TextView trendDescription = ViewBindings.findChildViewById(rootView, id);
      if (trendDescription == null) {
        break missingId;
      }

      id = R.id.trend_indicator;
      TextView trendIndicator = ViewBindings.findChildViewById(rootView, id);
      if (trendIndicator == null) {
        break missingId;
      }

      return new ActivityTestProgressBinding((LinearLayout) rootView, averageScoreText,
          averageTimeText, bestScoreText, consistencyText, contentContainer, emptyStateContainer,
          fastestTimeText, progressBar, recommendationDescription, recommendationTitle,
          recommendationsContainer, resultsRecycler, streakText, timesCompletedText, toolbar,
          trendDescription, trendIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
