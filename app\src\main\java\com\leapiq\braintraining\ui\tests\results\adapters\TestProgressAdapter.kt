package com.leapiq.braintraining.ui.tests.results.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ItemTestProgressBinding
import com.leapiq.braintraining.ui.tests.results.TestProgressItem
import com.leapiq.braintraining.data.TrendDirection

/**
 * Adapter for displaying test progress items
 */
class TestProgressAdapter(
    private val onItemClick: (String) -> Unit
) : ListAdapter<TestProgressItem, TestProgressAdapter.TestProgressViewHolder>(DiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TestProgressViewHolder {
        val binding = ItemTestProgressBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return TestProgressViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: TestProgressViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class TestProgressViewHolder(
        private val binding: ItemTestProgressBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(item: TestProgressItem) {
            binding.apply {
                testNameText.text = item.testName
                timesCompletedText.text = "${item.progress.timesCompleted} times"
                bestScoreText.text = "${item.progress.bestScore}%"
                averageScoreText.text = "${item.progress.averageScore.toInt()}%"
                
                // Set trend indicator
                val trendIcon = when (item.trend.trendDirection) {
                    TrendDirection.IMPROVING -> "📈"
                    TrendDirection.DECLINING -> "📉"
                    TrendDirection.STABLE -> "➡️"
                }
                
                val trendColor = when (item.trend.trendDirection) {
                    TrendDirection.IMPROVING -> R.color.success_green
                    TrendDirection.DECLINING -> R.color.error_red
                    TrendDirection.STABLE -> R.color.text_secondary
                }
                
                trendIndicator.text = trendIcon
                trendIndicator.setTextColor(ContextCompat.getColor(root.context, trendColor))
                
                // Set progress bar
                progressBar.progress = item.progress.averageScore.toInt()
                
                // Set score color based on performance
                val scoreColor = when {
                    item.progress.averageScore >= 80 -> R.color.success_green
                    item.progress.averageScore >= 60 -> R.color.warning_orange
                    else -> R.color.error_red
                }
                
                averageScoreText.setTextColor(ContextCompat.getColor(root.context, scoreColor))
                bestScoreText.setTextColor(ContextCompat.getColor(root.context, scoreColor))
                
                // Set click listener
                root.setOnClickListener {
                    onItemClick(item.testId)
                }
            }
        }
    }
    
    private class DiffCallback : DiffUtil.ItemCallback<TestProgressItem>() {
        override fun areItemsTheSame(oldItem: TestProgressItem, newItem: TestProgressItem): Boolean {
            return oldItem.testId == newItem.testId
        }
        
        override fun areContentsTheSame(oldItem: TestProgressItem, newItem: TestProgressItem): Boolean {
            return oldItem == newItem
        }
    }
}
