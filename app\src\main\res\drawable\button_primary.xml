<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#0056CC" />
            <corners android:radius="24dp" />
        </shape>
    </item>

    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/primary_light_blue" />
            <corners android:radius="24dp" />
        </shape>
    </item>
</selector>
