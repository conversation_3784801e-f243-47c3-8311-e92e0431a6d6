package com.leapiq.braintraining.ui.games.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.ui.games.model.Ball
import com.leapiq.braintraining.ui.games.model.Tube

/**
 * Adapter for the tube sort game grid
 * Displays tubes with colored balls
 */
class TubeAdapter(
    private val tubes: List<Tube>,
    private val onTubeClicked: (Int) -> Unit
) : RecyclerView.Adapter<TubeAdapter.TubeViewHolder>() {

    private var selectedTubeIndex = -1

    class TubeViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tubeContainer: LinearLayout = itemView.findViewById(R.id.tube_container)
        val ballSlot1: View = itemView.findViewById(R.id.ball_slot_1)
        val ballSlot2: View = itemView.findViewById(R.id.ball_slot_2)
        val ballSlot3: View = itemView.findViewById(R.id.ball_slot_3)
        val ballSlot4: View = itemView.findViewById(R.id.ball_slot_4)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TubeViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_tube, parent, false)
        return TubeViewHolder(view)
    }

    override fun onBindViewHolder(holder: TubeViewHolder, position: Int) {
        val tube = tubes[position]
        val ballSlots = listOf(holder.ballSlot1, holder.ballSlot2, holder.ballSlot3, holder.ballSlot4)
        
        // Clear all slots first
        ballSlots.forEach { slot ->
            slot.setBackgroundResource(R.drawable.tube_empty_slot)
        }
        
        // Fill slots with balls (bottom to top)
        tube.balls.forEachIndexed { index, ball ->
            if (index < ballSlots.size) {
                val slot = ballSlots[index]
                val colorRes = getBallColorResource(ball.color)
                slot.setBackgroundResource(colorRes)
            }
        }
        
        // Set selection state
        if (position == selectedTubeIndex) {
            holder.tubeContainer.setBackgroundResource(R.drawable.tube_selected_background)
        } else {
            holder.tubeContainer.setBackgroundResource(R.drawable.tube_normal_background)
        }
        
        // Set click listener
        holder.itemView.setOnClickListener {
            onTubeClicked(position)
        }
    }

    override fun getItemCount() = tubes.size

    fun setSelectedTube(index: Int) {
        val previousSelected = selectedTubeIndex
        selectedTubeIndex = index
        
        if (previousSelected != -1) {
            notifyItemChanged(previousSelected)
        }
        if (index != -1) {
            notifyItemChanged(index)
        }
    }

    private fun getBallColorResource(color: Ball.Color): Int {
        return when (color) {
            Ball.Color.RED -> R.drawable.tube_ball_red
            Ball.Color.BLUE -> R.drawable.tube_ball_blue
            Ball.Color.GREEN -> R.drawable.tube_ball_green
            Ball.Color.YELLOW -> R.drawable.tube_ball_yellow
            Ball.Color.PURPLE -> R.drawable.tube_ball_purple
            Ball.Color.ORANGE -> R.drawable.tube_ball_orange
            Ball.Color.PINK -> R.drawable.tube_ball_pink
            Ball.Color.CYAN -> R.drawable.tube_ball_cyan
        }
    }
}
