package com.leapiq.braintraining.ui.games.model

/**
 * Data models for Logic Puzzles game
 */

/**
 * Represents a complete logic puzzle with categories, clues, and solution
 */
data class LogicPuzzle(
    val categories: List<Category>,
    val clues: List<Clue>,
    val solution: Map<String, Map<String, String>>
)

/**
 * Represents a category in the puzzle (e.g., People, Pets, Colors)
 */
data class Category(
    val name: String,
    val items: List<String>
)

/**
 * Represents a clue in the puzzle
 */
data class Clue(
    val text: String,
    val type: ClueType,
    val isUsed: Boolean = false
)

/**
 * Types of clues in logic puzzles
 */
enum class ClueType {
    DIRECT,         // "Alice has a cat"
    NOT_EQUAL,      // "<PERSON> doesn't have a dog"
    ADJACENT,       // "The cat owner lives next to the dog owner"
    RELATIVE,       // "The red house is left of the blue house"
    CONDITIONAL     // "If <PERSON> has a cat, then <PERSON> has a dog"
}

/**
 * State of a cell in the logic grid
 */
enum class CellState {
    EMPTY,          // No mark
    MARKED_X,       // Impossible (marked with X)
    MARKED_O        // Confirmed (marked with O)
}

/**
 * Represents a position in the logic grid
 */
data class GridPosition(
    val row: Int,
    val col: Int,
    val rowCategory: String,
    val rowItem: String,
    val colCategory: String,
    val colItem: String
)

/**
 * Represents the state of the entire grid
 */
data class GridState(
    val cells: Map<Pair<Int, Int>, CellState> = emptyMap(),
    val isComplete: Boolean = false,
    val isValid: Boolean = true
)

/**
 * Represents a constraint in the puzzle
 */
data class Constraint(
    val type: ConstraintType,
    val entities: List<String>,
    val relationship: String
)

/**
 * Types of constraints
 */
enum class ConstraintType {
    EQUALS,         // A = B
    NOT_EQUALS,     // A ≠ B
    ADJACENT,       // A is next to B
    LEFT_OF,        // A is left of B
    RIGHT_OF,       // A is right of B
    BETWEEN         // A is between B and C
}

/**
 * Represents a hint for the player
 */
data class Hint(
    val text: String,
    val type: HintType,
    val relatedClue: Int? = null,
    val suggestedAction: String? = null
)

/**
 * Types of hints
 */
enum class HintType {
    DIRECT_CLUE,        // Points to a specific clue
    ELIMINATION,        // Suggests elimination strategy
    DEDUCTION,          // Suggests logical deduction
    STRATEGY,           // General strategy advice
    VALIDATION          // Points out errors
}
