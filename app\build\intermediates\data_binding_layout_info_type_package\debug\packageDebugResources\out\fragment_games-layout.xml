<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_games" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\fragment_games.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_games_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="133" endOffset="14"/></Target><Target tag="layout/fragment_games_0" include="layout_header"><Expressions/><location startLine="11" startOffset="4" endLine="11" endOffset="45"/></Target><Target id="@+id/category_scroll_view" view="HorizontalScrollView"><Expressions/><location startLine="14" startOffset="4" endLine="120" endOffset="26"/></Target><Target id="@+id/tab_all" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="31" startOffset="12" endLine="42" endOffset="41"/></Target><Target id="@+id/tab_memory" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="44" startOffset="12" endLine="57" endOffset="41"/></Target><Target id="@+id/tab_attention" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="59" startOffset="12" endLine="72" endOffset="41"/></Target><Target id="@+id/tab_math" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="74" startOffset="12" endLine="87" endOffset="41"/></Target><Target id="@+id/tab_logic" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="89" startOffset="12" endLine="102" endOffset="41"/></Target><Target id="@+id/tab_language" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="104" startOffset="12" endLine="116" endOffset="41"/></Target><Target id="@+id/games_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="123" startOffset="4" endLine="131" endOffset="49"/></Target></Targets></Layout>