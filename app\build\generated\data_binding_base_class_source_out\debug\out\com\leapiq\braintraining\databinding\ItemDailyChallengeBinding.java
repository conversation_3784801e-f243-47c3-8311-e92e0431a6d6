// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDailyChallengeBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final TextView challengeCategory;

  @NonNull
  public final ImageView challengeIcon;

  @NonNull
  public final TextView challengeName;

  private ItemDailyChallengeBinding(@NonNull CardView rootView, @NonNull TextView challengeCategory,
      @NonNull ImageView challengeIcon, @NonNull TextView challengeName) {
    this.rootView = rootView;
    this.challengeCategory = challengeCategory;
    this.challengeIcon = challengeIcon;
    this.challengeName = challengeName;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDailyChallengeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDailyChallengeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_daily_challenge, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDailyChallengeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.challenge_category;
      TextView challengeCategory = ViewBindings.findChildViewById(rootView, id);
      if (challengeCategory == null) {
        break missingId;
      }

      id = R.id.challenge_icon;
      ImageView challengeIcon = ViewBindings.findChildViewById(rootView, id);
      if (challengeIcon == null) {
        break missingId;
      }

      id = R.id.challenge_name;
      TextView challengeName = ViewBindings.findChildViewById(rootView, id);
      if (challengeName == null) {
        break missingId;
      }

      return new ItemDailyChallengeBinding((CardView) rootView, challengeCategory, challengeIcon,
          challengeName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
