<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Test Info -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/test_name_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Memory Assessment"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/times_completed_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="3 times"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                android:layout_marginBottom="8dp" />

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progress_bar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="6dp"
                android:layout_marginBottom="4dp"
                android:progressTint="@color/primary_light_blue"
                android:max="100" />

        </LinearLayout>

        <!-- Scores and Trend -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="end"
            android:layout_marginStart="16dp">

            <!-- Trend Indicator -->
            <TextView
                android:id="@+id/trend_indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="📈"
                android:textSize="16sp"
                android:layout_marginBottom="4dp"
                android:gravity="center" />

            <!-- Best Score -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="2dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Best: "
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp" />

                <TextView
                    android:id="@+id/best_score_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="85%"
                    android:textColor="@color/success_green"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Average Score -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Avg: "
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp" />

                <TextView
                    android:id="@+id/average_score_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="78%"
                    android:textColor="@color/success_green"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
