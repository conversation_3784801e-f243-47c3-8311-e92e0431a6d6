// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemRecentActivityBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView activityIcon;

  @NonNull
  public final TextView dateText;

  @NonNull
  public final TextView scoreText;

  @NonNull
  public final TextView testNameText;

  private ItemRecentActivityBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView activityIcon, @NonNull TextView dateText, @NonNull TextView scoreText,
      @NonNull TextView testNameText) {
    this.rootView = rootView;
    this.activityIcon = activityIcon;
    this.dateText = dateText;
    this.scoreText = scoreText;
    this.testNameText = testNameText;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemRecentActivityBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemRecentActivityBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_recent_activity, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemRecentActivityBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.activity_icon;
      TextView activityIcon = ViewBindings.findChildViewById(rootView, id);
      if (activityIcon == null) {
        break missingId;
      }

      id = R.id.date_text;
      TextView dateText = ViewBindings.findChildViewById(rootView, id);
      if (dateText == null) {
        break missingId;
      }

      id = R.id.score_text;
      TextView scoreText = ViewBindings.findChildViewById(rootView, id);
      if (scoreText == null) {
        break missingId;
      }

      id = R.id.test_name_text;
      TextView testNameText = ViewBindings.findChildViewById(rootView, id);
      if (testNameText == null) {
        break missingId;
      }

      return new ItemRecentActivityBinding((MaterialCardView) rootView, activityIcon, dateText,
          scoreText, testNameText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
