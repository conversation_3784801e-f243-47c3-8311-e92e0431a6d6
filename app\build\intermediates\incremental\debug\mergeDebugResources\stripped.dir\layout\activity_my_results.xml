<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/surface_white"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="My Results"
        app:titleTextColor="@color/text_primary" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Empty State -->
            <LinearLayout
                android:id="@+id/empty_state_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="32dp"
                android:gravity="center"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="📊"
                    android:textSize="64sp"
                    android:layout_marginBottom="16dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="No Test Results Yet"
                    android:textColor="@color/text_primary"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Complete your first test to see your results and progress here"
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp"
                    android:gravity="center"
                    android:layout_marginBottom="24dp" />

                <com.google.android.material.button.MaterialButton
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Take Your First Test"
                    android:textColor="@color/surface_white"
                    app:backgroundTint="@color/primary_light_blue"
                    app:cornerRadius="8dp" />

            </LinearLayout>

            <!-- Content Container -->
            <LinearLayout
                android:id="@+id/content_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- Overview Statistics -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Overview"
                            android:textColor="@color/text_primary"
                            android:textSize="20sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="16dp" />

                        <!-- Statistics Grid -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:weightSum="3">

                            <!-- Total Tests -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="8dp">

                                <TextView
                                    android:id="@+id/total_tests_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textColor="@color/primary_light_blue"
                                    android:textSize="32sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Total Tests"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp"
                                    android:gravity="center" />

                            </LinearLayout>

                            <!-- Cognitive Tests -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="8dp">

                                <TextView
                                    android:id="@+id/cognitive_tests_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textColor="@color/success_green"
                                    android:textSize="32sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Cognitive"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp"
                                    android:gravity="center" />

                            </LinearLayout>

                            <!-- Personality Tests -->
                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="8dp">

                                <TextView
                                    android:id="@+id/personality_tests_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textColor="@color/warning_orange"
                                    android:textSize="32sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Personality"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp"
                                    android:gravity="center" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- Average Scores -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="16dp"
                            android:orientation="horizontal"
                            android:weightSum="2">

                            <!-- Cognitive Average -->
                            <LinearLayout
                                android:id="@+id/cognitive_score_container"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="8dp"
                                android:background="@drawable/rounded_background"
                                android:layout_marginEnd="8dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Cognitive Avg"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/avg_cognitive_score_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0%"
                                    android:textColor="@color/success_green"
                                    android:textSize="18sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <!-- Personality Average -->
                            <LinearLayout
                                android:id="@+id/personality_score_container"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center"
                                android:padding="8dp"
                                android:background="@drawable/rounded_background"
                                android:layout_marginStart="8dp">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Personality Avg"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/avg_personality_score_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0%"
                                    android:textColor="@color/warning_orange"
                                    android:textSize="18sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Overall Progress -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="20dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Overall Progress"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="12dp" />

                        <ProgressBar
                            android:id="@+id/overall_progress_bar"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="match_parent"
                            android:layout_height="12dp"
                            android:layout_marginBottom="8dp"
                            android:progressTint="@color/primary_light_blue"
                            android:max="100" />

                        <TextView
                            android:id="@+id/overall_progress_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="0% Complete"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp"
                            android:layout_marginBottom="16dp" />

                        <!-- Progress Details -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:weightSum="3">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/improving_tests_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textColor="@color/success_green"
                                    android:textSize="20sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Improving"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/stable_tests_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textColor="@color/primary_light_blue"
                                    android:textSize="20sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Stable"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/declining_tests_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textColor="@color/error_red"
                                    android:textSize="20sp"
                                    android:textStyle="bold" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Declining"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Insights -->
                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/insights_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:visibility="gone"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:padding="16dp">

                        <View
                            android:id="@+id/insight_priority_indicator"
                            android:layout_width="4dp"
                            android:layout_height="match_parent"
                            android:layout_marginEnd="12dp"
                            android:background="@color/primary_light_blue" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/insight_title"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Insight Title"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:id="@+id/insight_description"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Insight description"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:id="@+id/insight_action"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Recommended action"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:textStyle="italic" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Test Progress -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Test Progress"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="12dp" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/test_progress_recycler"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Recent Activity -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Recent Activity"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="12dp" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recent_activity_recycler"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Action Buttons -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_view_all_results"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="8dp"
                        android:text="View All Results"
                        android:textColor="@color/surface_white"
                        app:backgroundTint="@color/primary_light_blue"
                        app:cornerRadius="8dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="2">

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_analytics"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="4dp"
                            android:text="Analytics"
                            android:textColor="@color/primary_light_blue"
                            app:cornerRadius="8dp"
                            app:strokeColor="@color/primary_light_blue" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_export_data"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="4dp"
                            android:text="Export Data"
                            android:textColor="@color/primary_light_blue"
                            app:cornerRadius="8dp"
                            app:strokeColor="@color/primary_light_blue" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
