package com.leapiq.braintraining.ui.games.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.ui.games.model.HanoiDisk
import com.leapiq.braintraining.ui.games.model.HanoiTower

/**
 * Adapter for the Tower of Hanoi game
 * Displays towers with stacked disks
 */
class HanoiTowerAdapter(
    private val towers: List<HanoiTower>,
    private val onTowerClicked: (Int) -> Unit
) : RecyclerView.Adapter<HanoiTowerAdapter.TowerViewHolder>() {

    private var selectedTowerIndex = -1

    class TowerViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val towerContainer: LinearLayout = itemView.findViewById(R.id.tower_container)
        val towerName: TextView = itemView.findViewById(R.id.tower_name)
        val towerPeg: View = itemView.findViewById(R.id.tower_peg)
        val disksContainer: LinearLayout = itemView.findViewById(R.id.disks_container)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TowerViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_hanoi_tower, parent, false)
        return TowerViewHolder(view)
    }

    override fun onBindViewHolder(holder: TowerViewHolder, position: Int) {
        val tower = towers[position]
        
        // Set tower name
        holder.towerName.text = "Tower ${tower.name}"
        
        // Clear previous disks
        holder.disksContainer.removeAllViews()
        
        // Add disks from bottom to top
        tower.disks.forEach { disk ->
            val diskView = createDiskView(holder.itemView.context, disk)
            holder.disksContainer.addView(diskView, 0) // Add to top of container
        }
        
        // Set selection state
        if (position == selectedTowerIndex) {
            holder.towerContainer.setBackgroundResource(R.drawable.hanoi_tower_selected_background)
        } else {
            holder.towerContainer.setBackgroundResource(R.drawable.hanoi_tower_normal_background)
        }
        
        // Set click listener
        holder.itemView.setOnClickListener {
            onTowerClicked(position)
        }
    }

    override fun getItemCount() = towers.size

    fun setSelectedTower(index: Int) {
        val previousSelected = selectedTowerIndex
        selectedTowerIndex = index
        
        if (previousSelected != -1) {
            notifyItemChanged(previousSelected)
        }
        if (index != -1) {
            notifyItemChanged(index)
        }
    }

    private fun createDiskView(context: android.content.Context, disk: HanoiDisk): View {
        val diskView = TextView(context)
        
        // Set disk text and appearance
        diskView.text = disk.size.toString()
        diskView.gravity = android.view.Gravity.CENTER
        diskView.textSize = 14f
        diskView.setTextColor(ContextCompat.getColor(context, R.color.text_white))
        diskView.typeface = android.graphics.Typeface.DEFAULT_BOLD
        
        // Set disk size and color based on disk size
        val diskWidth = 40 + (disk.size * 20) // Larger disks are wider
        val diskHeight = 30
        
        val layoutParams = LinearLayout.LayoutParams(
            diskWidth,
            diskHeight
        )
        layoutParams.setMargins(4, 2, 4, 2)
        diskView.layoutParams = layoutParams
        
        // Set background color based on disk color
        val colorRes = getDiskColorResource(disk.getColor())
        diskView.setBackgroundResource(colorRes)
        
        return diskView
    }

    private fun getDiskColorResource(color: HanoiDisk.DiskColor): Int {
        return when (color) {
            HanoiDisk.DiskColor.RED -> R.drawable.hanoi_disk_red
            HanoiDisk.DiskColor.BLUE -> R.drawable.hanoi_disk_blue
            HanoiDisk.DiskColor.GREEN -> R.drawable.hanoi_disk_green
            HanoiDisk.DiskColor.YELLOW -> R.drawable.hanoi_disk_yellow
            HanoiDisk.DiskColor.PURPLE -> R.drawable.hanoi_disk_purple
            HanoiDisk.DiskColor.ORANGE -> R.drawable.hanoi_disk_orange
            HanoiDisk.DiskColor.PINK -> R.drawable.hanoi_disk_pink
            HanoiDisk.DiskColor.CYAN -> R.drawable.hanoi_disk_cyan
        }
    }
}
