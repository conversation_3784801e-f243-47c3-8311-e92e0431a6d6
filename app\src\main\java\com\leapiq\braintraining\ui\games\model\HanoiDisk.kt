package com.leapiq.braintraining.ui.games.model

/**
 * Represents a disk in the Tower of Hanoi game
 */
data class HanoiDisk(
    val size: Int // Size 1 = smallest, higher numbers = larger disks
) {
    fun getColor(): DiskColor {
        return when (size) {
            1 -> DiskColor.RED
            2 -> DiskColor.BLUE
            3 -> DiskColor.GREEN
            4 -> DiskColor.YELLOW
            5 -> DiskColor.PURPLE
            6 -> DiskColor.ORANGE
            7 -> DiskColor.PINK
            8 -> DiskColor.CYAN
            else -> DiskColor.RED
        }
    }
    
    enum class DiskColor {
        RED, BLUE, GRE<PERSON>, <PERSON><PERSON>LOW, PURPLE, ORANGE, PINK, CYAN
    }
}
