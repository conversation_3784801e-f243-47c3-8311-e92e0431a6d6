<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#F0F0F0" />
            <corners android:radius="24dp" />
            <stroke android:width="2dp" android:color="#0056CC" />
        </shape>
    </item>

    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/background_white" />
            <corners android:radius="24dp" />
            <stroke android:width="2dp" android:color="@color/primary_light_blue" />
        </shape>
    </item>
</selector>
