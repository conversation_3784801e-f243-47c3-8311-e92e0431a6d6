<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_card_matching" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_card_matching.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_card_matching_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="134" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="26" startOffset="12" endLine="33" endOffset="49"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="45" endOffset="44"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="48" startOffset="12" endLine="55" endOffset="51"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="67" startOffset="12" endLine="76" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="78" startOffset="12" endLine="85" endOffset="40"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="90" startOffset="8" endLine="99" endOffset="90"/></Target><Target id="@+id/card_grid" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="121" startOffset="16" endLine="126" endOffset="63"/></Target></Targets></Layout>