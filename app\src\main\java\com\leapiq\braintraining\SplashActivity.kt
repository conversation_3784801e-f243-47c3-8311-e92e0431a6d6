package com.leapiq.braintraining

import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.appcompat.app.AppCompatActivity

class SplashActivity : AppCompatActivity() {

    private companion object {
        const val SPLASH_DELAY = 2500L // 2.5 seconds
    }

    private lateinit var sharedPreferences: SharedPreferences

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_splash)

        sharedPreferences = getSharedPreferences("LeapIQ_Prefs", MODE_PRIVATE)

        // Navigate after delay
        Handler(Looper.getMainLooper()).postDelayed({
            navigateToNextScreen()
        }, SPLASH_DELAY)
    }

    private fun navigateToNextScreen() {
        val isOnboardingComplete = sharedPreferences.getBoolean("onboarding_complete", false)

        val intent = if (isOnboardingComplete) {
            Intent(this, MainActivity::class.java)
        } else {
            Intent(this, WelcomeActivity::class.java)
        }

        startActivity(intent)
        finish()
    }

    override fun onBackPressed() {
        // Disable back button during splash
        // Do nothing
    }
}
