// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemHanoiTowerBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout disksContainer;

  @NonNull
  public final LinearLayout towerContainer;

  @NonNull
  public final TextView towerName;

  @NonNull
  public final View towerPeg;

  private ItemHanoiTowerBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout disksContainer, @NonNull LinearLayout towerContainer,
      @NonNull TextView towerName, @NonNull View towerPeg) {
    this.rootView = rootView;
    this.disksContainer = disksContainer;
    this.towerContainer = towerContainer;
    this.towerName = towerName;
    this.towerPeg = towerPeg;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemHanoiTowerBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemHanoiTowerBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_hanoi_tower, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemHanoiTowerBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.disks_container;
      LinearLayout disksContainer = ViewBindings.findChildViewById(rootView, id);
      if (disksContainer == null) {
        break missingId;
      }

      LinearLayout towerContainer = (LinearLayout) rootView;

      id = R.id.tower_name;
      TextView towerName = ViewBindings.findChildViewById(rootView, id);
      if (towerName == null) {
        break missingId;
      }

      id = R.id.tower_peg;
      View towerPeg = ViewBindings.findChildViewById(rootView, id);
      if (towerPeg == null) {
        break missingId;
      }

      return new ItemHanoiTowerBinding((LinearLayout) rootView, disksContainer, towerContainer,
          towerName, towerPeg);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
