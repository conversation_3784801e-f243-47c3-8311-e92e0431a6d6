// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLearningStyleOptionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final CheckBox optionCheckbox;

  @NonNull
  public final TextView optionText;

  @NonNull
  public final TextView preferenceIcon;

  private ItemLearningStyleOptionBinding(@NonNull LinearLayout rootView,
      @NonNull CheckBox optionCheckbox, @NonNull TextView optionText,
      @NonNull TextView preferenceIcon) {
    this.rootView = rootView;
    this.optionCheckbox = optionCheckbox;
    this.optionText = optionText;
    this.preferenceIcon = preferenceIcon;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLearningStyleOptionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLearningStyleOptionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_learning_style_option, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLearningStyleOptionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.option_checkbox;
      CheckBox optionCheckbox = ViewBindings.findChildViewById(rootView, id);
      if (optionCheckbox == null) {
        break missingId;
      }

      id = R.id.option_text;
      TextView optionText = ViewBindings.findChildViewById(rootView, id);
      if (optionText == null) {
        break missingId;
      }

      id = R.id.preference_icon;
      TextView preferenceIcon = ViewBindings.findChildViewById(rootView, id);
      if (preferenceIcon == null) {
        break missingId;
      }

      return new ItemLearningStyleOptionBinding((LinearLayout) rootView, optionCheckbox, optionText,
          preferenceIcon);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
