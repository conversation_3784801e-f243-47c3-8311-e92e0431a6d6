<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_focus_challenge" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_focus_challenge.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_focus_challenge_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="189" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/progress_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/target_display" view="TextView"><Expressions/><location startLine="111" startOffset="8" endLine="118" endOffset="38"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="123" startOffset="4" endLine="132" endOffset="33"/></Target><Target id="@+id/response_area" view="FrameLayout"><Expressions/><location startLine="135" startOffset="4" endLine="187" endOffset="17"/></Target><Target id="@+id/stimulus_display" view="TextView"><Expressions/><location startLine="146" startOffset="8" endLine="161" endOffset="40"/></Target><Target id="@+id/feedback_text" view="TextView"><Expressions/><location startLine="164" startOffset="8" endLine="174" endOffset="39"/></Target></Targets></Layout>