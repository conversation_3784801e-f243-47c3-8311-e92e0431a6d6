<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_margin="1dp"
    android:background="@drawable/word_search_cell_background"
    android:clickable="true"
    android:focusable="true">

    <TextView
        android:id="@+id/letter_text"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:gravity="center"
        android:text="A"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold"
        android:fontFamily="monospace"
        tools:text="A" />

</FrameLayout>
