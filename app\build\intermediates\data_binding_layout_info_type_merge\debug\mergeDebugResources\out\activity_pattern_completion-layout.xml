<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_pattern_completion" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_pattern_completion.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_pattern_completion_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="261" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/trial_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/pattern_type_text" view="TextView"><Expressions/><location startLine="103" startOffset="4" endLine="113" endOffset="34"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="116" startOffset="4" endLine="125" endOffset="33"/></Target><Target id="@+id/pattern_display" view="TextView"><Expressions/><location startLine="141" startOffset="12" endLine="154" endOffset="44"/></Target><Target id="@+id/question_text" view="TextView"><Expressions/><location startLine="157" startOffset="12" endLine="168" endOffset="63"/></Target><Target id="@+id/btn_option1" view="Button"><Expressions/><location startLine="189" startOffset="12" endLine="202" endOffset="32"/></Target><Target id="@+id/btn_option2" view="Button"><Expressions/><location startLine="204" startOffset="12" endLine="217" endOffset="32"/></Target><Target id="@+id/btn_option3" view="Button"><Expressions/><location startLine="227" startOffset="12" endLine="240" endOffset="32"/></Target><Target id="@+id/btn_option4" view="Button"><Expressions/><location startLine="242" startOffset="12" endLine="255" endOffset="32"/></Target></Targets></Layout>