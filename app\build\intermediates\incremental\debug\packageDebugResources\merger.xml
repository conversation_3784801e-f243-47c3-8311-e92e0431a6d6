<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Projects\LeapIQ\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Projects\LeapIQ\app\src\main\res"><file name="fade_in" path="C:\Projects\LeapIQ\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="fade_out" path="C:\Projects\LeapIQ\app\src\main\res\anim\fade_out.xml" qualifiers="" type="anim"/><file name="anagram_input_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\anagram_input_background.xml" qualifiers="" type="drawable"/><file name="anagram_scrambled_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\anagram_scrambled_background.xml" qualifiers="" type="drawable"/><file name="button_danger" path="C:\Projects\LeapIQ\app\src\main\res\drawable\button_danger.xml" qualifiers="" type="drawable"/><file name="button_primary" path="C:\Projects\LeapIQ\app\src\main\res\drawable\button_primary.xml" qualifiers="" type="drawable"/><file name="button_secondary" path="C:\Projects\LeapIQ\app\src\main\res\drawable\button_secondary.xml" qualifiers="" type="drawable"/><file name="card_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\card_background.xml" qualifiers="" type="drawable"/><file name="dialog_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\dialog_background.xml" qualifiers="" type="drawable"/><file name="estimation_answer_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\estimation_answer_background.xml" qualifiers="" type="drawable"/><file name="estimation_display_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\estimation_display_background.xml" qualifiers="" type="drawable"/><file name="focus_stimulus_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\focus_stimulus_background.xml" qualifiers="" type="drawable"/><file name="game_card_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\game_card_background.xml" qualifiers="" type="drawable"/><file name="hanoi_disk_blue" path="C:\Projects\LeapIQ\app\src\main\res\drawable\hanoi_disk_blue.xml" qualifiers="" type="drawable"/><file name="hanoi_disk_cyan" path="C:\Projects\LeapIQ\app\src\main\res\drawable\hanoi_disk_cyan.xml" qualifiers="" type="drawable"/><file name="hanoi_disk_green" path="C:\Projects\LeapIQ\app\src\main\res\drawable\hanoi_disk_green.xml" qualifiers="" type="drawable"/><file name="hanoi_disk_orange" path="C:\Projects\LeapIQ\app\src\main\res\drawable\hanoi_disk_orange.xml" qualifiers="" type="drawable"/><file name="hanoi_disk_pink" path="C:\Projects\LeapIQ\app\src\main\res\drawable\hanoi_disk_pink.xml" qualifiers="" type="drawable"/><file name="hanoi_disk_purple" path="C:\Projects\LeapIQ\app\src\main\res\drawable\hanoi_disk_purple.xml" qualifiers="" type="drawable"/><file name="hanoi_disk_red" path="C:\Projects\LeapIQ\app\src\main\res\drawable\hanoi_disk_red.xml" qualifiers="" type="drawable"/><file name="hanoi_disk_yellow" path="C:\Projects\LeapIQ\app\src\main\res\drawable\hanoi_disk_yellow.xml" qualifiers="" type="drawable"/><file name="hanoi_tower_base" path="C:\Projects\LeapIQ\app\src\main\res\drawable\hanoi_tower_base.xml" qualifiers="" type="drawable"/><file name="hanoi_tower_normal_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\hanoi_tower_normal_background.xml" qualifiers="" type="drawable"/><file name="hanoi_tower_peg" path="C:\Projects\LeapIQ\app\src\main\res\drawable\hanoi_tower_peg.xml" qualifiers="" type="drawable"/><file name="hanoi_tower_selected_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\hanoi_tower_selected_background.xml" qualifiers="" type="drawable"/><file name="header_icon_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\header_icon_background.xml" qualifiers="" type="drawable"/><file name="ic_analytics" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_analytics.xml" qualifiers="" type="drawable"/><file name="ic_apple" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_apple.xml" qualifiers="" type="drawable"/><file name="ic_arrow_back" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_arrow_back.xml" qualifiers="" type="drawable"/><file name="ic_car" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_car.xml" qualifiers="" type="drawable"/><file name="ic_card_back" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_card_back.xml" qualifiers="" type="drawable"/><file name="ic_check" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_check.xml" qualifiers="" type="drawable"/><file name="ic_circle" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_circle.xml" qualifiers="" type="drawable"/><file name="ic_close" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_close.xml" qualifiers="" type="drawable"/><file name="ic_diamond" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_diamond.xml" qualifiers="" type="drawable"/><file name="ic_favorite" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_favorite.xml" qualifiers="" type="drawable"/><file name="ic_games" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_games.xml" qualifiers="" type="drawable"/><file name="ic_heart" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_heart.xml" qualifiers="" type="drawable"/><file name="ic_home" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_home.xml" qualifiers="" type="drawable"/><file name="ic_lock" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_lock.xml" qualifiers="" type="drawable"/><file name="ic_menu" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_menu.xml" qualifiers="" type="drawable"/><file name="ic_music" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_music.xml" qualifiers="" type="drawable"/><file name="ic_person" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_person.xml" qualifiers="" type="drawable"/><file name="ic_pets" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_pets.xml" qualifiers="" type="drawable"/><file name="ic_progress" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_progress.xml" qualifiers="" type="drawable"/><file name="ic_school" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_school.xml" qualifiers="" type="drawable"/><file name="ic_settings" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_settings.xml" qualifiers="" type="drawable"/><file name="ic_sports" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_sports.xml" qualifiers="" type="drawable"/><file name="ic_square" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_square.xml" qualifiers="" type="drawable"/><file name="ic_star" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_star.xml" qualifiers="" type="drawable"/><file name="ic_streak" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_streak.xml" qualifiers="" type="drawable"/><file name="ic_tests" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_tests.xml" qualifiers="" type="drawable"/><file name="ic_today" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_today.xml" qualifiers="" type="drawable"/><file name="ic_triangle" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_triangle.xml" qualifiers="" type="drawable"/><file name="ic_trophy" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_trophy.xml" qualifiers="" type="drawable"/><file name="logic_premises_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\logic_premises_background.xml" qualifiers="" type="drawable"/><file name="logic_question_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\logic_question_background.xml" qualifiers="" type="drawable"/><file name="math_answer_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\math_answer_background.xml" qualifiers="" type="drawable"/><file name="math_problem_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\math_problem_background.xml" qualifiers="" type="drawable"/><file name="memory_grid_default" path="C:\Projects\LeapIQ\app\src\main\res\drawable\memory_grid_default.xml" qualifiers="" type="drawable"/><file name="memory_grid_highlighted" path="C:\Projects\LeapIQ\app\src\main\res\drawable\memory_grid_highlighted.xml" qualifiers="" type="drawable"/><file name="memory_grid_pattern" path="C:\Projects\LeapIQ\app\src\main\res\drawable\memory_grid_pattern.xml" qualifiers="" type="drawable"/><file name="memory_grid_selected" path="C:\Projects\LeapIQ\app\src\main\res\drawable\memory_grid_selected.xml" qualifiers="" type="drawable"/><file name="number_display_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\number_display_background.xml" qualifiers="" type="drawable"/><file name="number_input_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\number_input_background.xml" qualifiers="" type="drawable"/><file name="pattern_cell_empty" path="C:\Projects\LeapIQ\app\src\main\res\drawable\pattern_cell_empty.xml" qualifiers="" type="drawable"/><file name="pattern_cell_filled" path="C:\Projects\LeapIQ\app\src\main\res\drawable\pattern_cell_filled.xml" qualifiers="" type="drawable"/><file name="pattern_display_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\pattern_display_background.xml" qualifiers="" type="drawable"/><file name="pattern_question_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\pattern_question_background.xml" qualifiers="" type="drawable"/><file name="premium_button_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\premium_button_background.xml" qualifiers="" type="drawable"/><file name="reaction_stimulus_circle" path="C:\Projects\LeapIQ\app\src\main\res\drawable\reaction_stimulus_circle.xml" qualifiers="" type="drawable"/><file name="rounded_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\rounded_background.xml" qualifiers="" type="drawable"/><file name="search_shape_circle" path="C:\Projects\LeapIQ\app\src\main\res\drawable\search_shape_circle.xml" qualifiers="" type="drawable"/><file name="search_shape_diamond" path="C:\Projects\LeapIQ\app\src\main\res\drawable\search_shape_diamond.xml" qualifiers="" type="drawable"/><file name="search_shape_square" path="C:\Projects\LeapIQ\app\src\main\res\drawable\search_shape_square.xml" qualifiers="" type="drawable"/><file name="search_shape_triangle" path="C:\Projects\LeapIQ\app\src\main\res\drawable\search_shape_triangle.xml" qualifiers="" type="drawable"/><file name="sequence_answer_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\sequence_answer_background.xml" qualifiers="" type="drawable"/><file name="sequence_button_blue" path="C:\Projects\LeapIQ\app\src\main\res\drawable\sequence_button_blue.xml" qualifiers="" type="drawable"/><file name="sequence_button_green" path="C:\Projects\LeapIQ\app\src\main\res\drawable\sequence_button_green.xml" qualifiers="" type="drawable"/><file name="sequence_button_red" path="C:\Projects\LeapIQ\app\src\main\res\drawable\sequence_button_red.xml" qualifiers="" type="drawable"/><file name="sequence_button_yellow" path="C:\Projects\LeapIQ\app\src\main\res\drawable\sequence_button_yellow.xml" qualifiers="" type="drawable"/><file name="sequence_display_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\sequence_display_background.xml" qualifiers="" type="drawable"/><file name="spatial_shape_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\spatial_shape_background.xml" qualifiers="" type="drawable"/><file name="speed_math_answer_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\speed_math_answer_background.xml" qualifiers="" type="drawable"/><file name="speed_math_problem_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\speed_math_problem_background.xml" qualifiers="" type="drawable"/><file name="stroop_word_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\stroop_word_background.xml" qualifiers="" type="drawable"/><file name="tube_ball_blue" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tube_ball_blue.xml" qualifiers="" type="drawable"/><file name="tube_ball_cyan" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tube_ball_cyan.xml" qualifiers="" type="drawable"/><file name="tube_ball_green" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tube_ball_green.xml" qualifiers="" type="drawable"/><file name="tube_ball_orange" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tube_ball_orange.xml" qualifiers="" type="drawable"/><file name="tube_ball_pink" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tube_ball_pink.xml" qualifiers="" type="drawable"/><file name="tube_ball_purple" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tube_ball_purple.xml" qualifiers="" type="drawable"/><file name="tube_ball_red" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tube_ball_red.xml" qualifiers="" type="drawable"/><file name="tube_ball_yellow" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tube_ball_yellow.xml" qualifiers="" type="drawable"/><file name="tube_empty_slot" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tube_empty_slot.xml" qualifiers="" type="drawable"/><file name="tube_normal_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tube_normal_background.xml" qualifiers="" type="drawable"/><file name="tube_selected_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tube_selected_background.xml" qualifiers="" type="drawable"/><file name="vocabulary_prompt_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\vocabulary_prompt_background.xml" qualifiers="" type="drawable"/><file name="word_search_cell_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\word_search_cell_background.xml" qualifiers="" type="drawable"/><file name="word_search_grid_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\word_search_grid_background.xml" qualifiers="" type="drawable"/><file name="word_search_list_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\word_search_list_background.xml" qualifiers="" type="drawable"/><file name="word_target_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\word_target_background.xml" qualifiers="" type="drawable"/><file name="activity_all_results" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_all_results.xml" qualifiers="" type="layout"/><file name="activity_anagrams" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_anagrams.xml" qualifiers="" type="layout"/><file name="activity_analytics" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_analytics.xml" qualifiers="" type="layout"/><file name="activity_card_matching" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_card_matching.xml" qualifiers="" type="layout"/><file name="activity_estimation" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_estimation.xml" qualifiers="" type="layout"/><file name="activity_focus_challenge" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_focus_challenge.xml" qualifiers="" type="layout"/><file name="activity_game_result" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_game_result.xml" qualifiers="" type="layout"/><file name="activity_learning_style" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_learning_style.xml" qualifiers="" type="layout"/><file name="activity_logical_reasoning" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_logical_reasoning.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="activity_mental_arithmetic" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_mental_arithmetic.xml" qualifiers="" type="layout"/><file name="activity_my_results" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_my_results.xml" qualifiers="" type="layout"/><file name="activity_number_memory" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_number_memory.xml" qualifiers="" type="layout"/><file name="activity_number_sequences" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_number_sequences.xml" qualifiers="" type="layout"/><file name="activity_pattern_completion" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_pattern_completion.xml" qualifiers="" type="layout"/><file name="activity_pattern_memory" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_pattern_memory.xml" qualifiers="" type="layout"/><file name="activity_problem_solving_style" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_problem_solving_style.xml" qualifiers="" type="layout"/><file name="activity_reaction_time" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_reaction_time.xml" qualifiers="" type="layout"/><file name="activity_sequence_recall" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_sequence_recall.xml" qualifiers="" type="layout"/><file name="activity_spatial_rotation" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_spatial_rotation.xml" qualifiers="" type="layout"/><file name="activity_speed_math" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_speed_math.xml" qualifiers="" type="layout"/><file name="activity_stress_response" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_stress_response.xml" qualifiers="" type="layout"/><file name="activity_stroop_test" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_stroop_test.xml" qualifiers="" type="layout"/><file name="activity_test_progress" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_test_progress.xml" qualifiers="" type="layout"/><file name="activity_test_results" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_test_results.xml" qualifiers="" type="layout"/><file name="activity_tower_of_hanoi" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_tower_of_hanoi.xml" qualifiers="" type="layout"/><file name="activity_tube_sort" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_tube_sort.xml" qualifiers="" type="layout"/><file name="activity_visual_search" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_visual_search.xml" qualifiers="" type="layout"/><file name="activity_vocabulary" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_vocabulary.xml" qualifiers="" type="layout"/><file name="activity_word_association" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_word_association.xml" qualifiers="" type="layout"/><file name="activity_word_search" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_word_search.xml" qualifiers="" type="layout"/><file name="dialog_game_menu" path="C:\Projects\LeapIQ\app\src\main\res\layout\dialog_game_menu.xml" qualifiers="" type="layout"/><file name="fragment_games" path="C:\Projects\LeapIQ\app\src\main\res\layout\fragment_games.xml" qualifiers="" type="layout"/><file name="fragment_progress" path="C:\Projects\LeapIQ\app\src\main\res\layout\fragment_progress.xml" qualifiers="" type="layout"/><file name="fragment_tests" path="C:\Projects\LeapIQ\app\src\main\res\layout\fragment_tests.xml" qualifiers="" type="layout"/><file name="fragment_today" path="C:\Projects\LeapIQ\app\src\main\res\layout\fragment_today.xml" qualifiers="" type="layout"/><file name="item_achievement" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_achievement.xml" qualifiers="" type="layout"/><file name="item_all_results" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_all_results.xml" qualifiers="" type="layout"/><file name="item_category_accuracy" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_category_accuracy.xml" qualifiers="" type="layout"/><file name="item_daily_challenge" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_daily_challenge.xml" qualifiers="" type="layout"/><file name="item_game_card" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_game_card.xml" qualifiers="" type="layout"/><file name="item_hanoi_tower" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_hanoi_tower.xml" qualifiers="" type="layout"/><file name="item_learning_style_option" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_learning_style_option.xml" qualifiers="" type="layout"/><file name="item_memory_card" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_memory_card.xml" qualifiers="" type="layout"/><file name="item_memory_grid" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_memory_grid.xml" qualifiers="" type="layout"/><file name="item_pattern_cell" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_pattern_cell.xml" qualifiers="" type="layout"/><file name="item_problem_solving_option" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_problem_solving_option.xml" qualifiers="" type="layout"/><file name="item_recent_activity" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_recent_activity.xml" qualifiers="" type="layout"/><file name="item_search_shape" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_search_shape.xml" qualifiers="" type="layout"/><file name="item_stress_response_option" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_stress_response_option.xml" qualifiers="" type="layout"/><file name="item_test_card" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_test_card.xml" qualifiers="" type="layout"/><file name="item_test_progress" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_test_progress.xml" qualifiers="" type="layout"/><file name="item_test_result" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_test_result.xml" qualifiers="" type="layout"/><file name="item_tube" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_tube.xml" qualifiers="" type="layout"/><file name="item_word_search_cell" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_word_search_cell.xml" qualifiers="" type="layout"/><file name="item_word_search_word" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_word_search_word.xml" qualifiers="" type="layout"/><file name="layout_header" path="C:\Projects\LeapIQ\app\src\main\res\layout\layout_header.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="C:\Projects\LeapIQ\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="nav_graph" path="C:\Projects\LeapIQ\app\src\main\res\navigation\nav_graph.xml" qualifiers="" type="navigation"/><file path="C:\Projects\LeapIQ\app\src\main\res\values\colors.xml" qualifiers=""><color name="primary_color">#2196F3</color><color name="primary_light_blue">#2196F3</color><color name="primary_light_blue_dark">#1976D2</color><color name="primary_light_blue_light">#BBDEFB</color><color name="secondary_blue">#03DAC5</color><color name="secondary_blue_variant">#018786</color><color name="background_white">#FFFFFF</color><color name="background_light_gray">#F5F5F5</color><color name="surface_white">#FFFFFF</color><color name="text_primary">#212121</color><color name="text_secondary">#757575</color><color name="text_white">#FFFFFF</color><color name="stroop_red">#F44336</color><color name="stroop_blue">#2196F3</color><color name="stroop_green">#4CAF50</color><color name="stroop_yellow">#FFEB3B</color><color name="search_purple">#9C27B0</color><color name="search_orange">#FF9800</color><color name="tube_pink">#E91E63</color><color name="tube_cyan">#00BCD4</color><color name="stroop_red_light">#FFEBEE</color><color name="stroop_green_light">#E8F5E8</color><color name="memory_color">#E3F2FD</color><color name="attention_color">#E8F5E8</color><color name="math_color">#FFF3E0</color><color name="logic_color">#F3E5F5</color><color name="language_color">#FFEBEE</color><color name="success_green">#4CAF50</color><color name="warning_orange">#FF9800</color><color name="error_red">#F44336</color><color name="premium_gold">#FFD700</color><color name="premium_gold_dark">#FFC107</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Projects\LeapIQ\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">LeapIQ</string><string name="nav_today">Today</string><string name="nav_games">Games</string><string name="nav_tests">Tests</string><string name="nav_progress">Progress</string><string name="welcome_user">Welcome User</string><string name="premium">Premium</string><string name="streak">Streak</string><string name="settings">Settings</string><string name="daily_challenges">Daily Challenges</string><string name="start_challenge">Start Challenge</string><string name="all_games">All</string><string name="memory_games">Memory</string><string name="attention_games">Attention</string><string name="math_games">Math</string><string name="logic_games">Logic</string><string name="language_games">Language</string><string name="card_matching">Card Matching</string><string name="sequence_recall">Sequence Recall</string><string name="pattern_memory">Pattern Memory</string><string name="number_memory">Number Memory</string><string name="stroop_test">Stroop Test</string><string name="reaction_time">Reaction Time</string><string name="visual_search">Visual Search</string><string name="focus_challenge">Focus Challenge</string><string name="mental_arithmetic">Mental Arithmetic</string><string name="number_sequences">Number Sequences</string><string name="speed_math">Speed Math</string><string name="estimation">Estimation</string><string name="pattern_completion">Pattern Completion</string><string name="logic_puzzles">Logic Puzzles</string><string name="shape_matching">Shape Matching</string><string name="rule_discovery">Rule Discovery</string><string name="tube_sort">Tube Sort</string><string name="word_association">Word Association</string><string name="anagrams">Anagrams</string><string name="vocabulary">Vocabulary</string><string name="word_search">Word Search</string><string name="level_completed">Level %d Completed</string><string name="accuracy_result">%d%%</string><string name="time_result">%s</string><string name="score_result">%d</string><string formatted="false" name="rounds_result">%d/%d</string><string name="next_level">Next Level (%d)</string><string name="retry_level">Retry Level</string><string name="level_locked">Level Locked</string><string name="level_format">Level %d</string><string formatted="false" name="round_format">Round %d/%d</string><string name="all_tests">All</string><string name="cognitive_tests">Cognitive</string><string name="personality_tests">Personality</string><string name="learning_style">Learning Style</string><string name="stress_response">Stress Response</string><string name="problem_solving_style">Problem Solving Style</string><string name="total_score">Total Score</string><string name="current_level">Level %d</string><string name="max_streak">Max Streak: %d days</string><string name="current_streak">Current: %d days</string><string name="games_played_week">Games Played: %d this week</string><string name="achievements">Achievements</string><string name="category_progress">Category Progress</string><string name="play">Play</string><string name="start">Start</string><string name="continue_game">Continue</string><string name="locked">Locked</string><string name="free">Free</string><string name="premium_only">Premium</string><string name="price_placeholder">$0.00</string><string name="tower_of_hanoi">Tower of Hanoi</string><string name="logical_reasoning">Logical Reasoning</string><string name="spatial_rotation">Spatial Rotation</string></file><file path="C:\Projects\LeapIQ\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.LeapIQ" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_light_blue</item>
        <item name="colorPrimaryVariant">@color/primary_light_blue_dark</item>
        <item name="colorOnPrimary">@color/text_white</item>
        
        
        <item name="colorSecondary">@color/secondary_blue</item>
        <item name="colorSecondaryVariant">@color/secondary_blue_variant</item>
        <item name="colorOnSecondary">@color/text_primary</item>
        
        
        <item name="android:colorBackground">@color/background_white</item>
        <item name="colorSurface">@color/surface_white</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        
        <item name="android:statusBarColor">@color/primary_light_blue_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        
        
        <item name="android:navigationBarColor">@color/background_white</item>
        <item name="android:windowLightNavigationBar">true</item>
    </style><style name="LeapIQ">
        
    </style><style name="LeapIQ.Header">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:background">@color/primary_light_blue</item>
        <item name="android:elevation">4dp</item>
    </style><style name="LeapIQ.HeaderText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_white</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
    </style><style name="LeapIQ.GameCard">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:background">@drawable/game_card_background</item>
        <item name="android:elevation">2dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style><style name="LeapIQ.GameTitle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">8dp</item>
    </style><style name="Theme.LeapIQ.Splash" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:statusBarColor">@color/background_white</item>
        <item name="android:navigationBarColor">@color/background_white</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
    </style></file><file name="backup_rules" path="C:\Projects\LeapIQ\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Projects\LeapIQ\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="logical_reasoning" path="C:\Projects\LeapIQ\app\src\main\res\drawable\logical_reasoning.xml" qualifiers="" type="drawable"/><file name="spatial_rotation" path="C:\Projects\LeapIQ\app\src\main\res\drawable\spatial_rotation.xml" qualifiers="" type="drawable"/><file name="tower_of_hanoi" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tower_of_hanoi.xml" qualifiers="" type="drawable"/><file name="fragment_profile" path="C:\Projects\LeapIQ\app\src\main\res\layout\fragment_profile.xml" qualifiers="" type="layout"/><file name="item_detailed_score" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_detailed_score.xml" qualifiers="" type="layout"/><file name="activity_logic_puzzles" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_logic_puzzles.xml" qualifiers="" type="layout"/><file name="item_logic_clue" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_logic_clue.xml" qualifiers="" type="layout"/><file name="item_logic_grid_cell" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_logic_grid_cell.xml" qualifiers="" type="layout"/><file name="item_logic_grid_header" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_logic_grid_header.xml" qualifiers="" type="layout"/><file name="item_logic_grid_row" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_logic_grid_row.xml" qualifiers="" type="layout"/><file name="button_primary_rounded" path="C:\Projects\LeapIQ\app\src\main\res\drawable\button_primary_rounded.xml" qualifiers="" type="drawable"/><file name="gradient_learning_style" path="C:\Projects\LeapIQ\app\src\main\res\drawable\gradient_learning_style.xml" qualifiers="" type="drawable"/><file name="gradient_problem_solving" path="C:\Projects\LeapIQ\app\src\main\res\drawable\gradient_problem_solving.xml" qualifiers="" type="drawable"/><file name="gradient_stress_response" path="C:\Projects\LeapIQ\app\src\main\res\drawable\gradient_stress_response.xml" qualifiers="" type="drawable"/><file name="ic_test_placeholder" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_test_placeholder.xml" qualifiers="" type="drawable"/><file name="ic_timer" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_timer.xml" qualifiers="" type="drawable"/><file name="activity_test_info" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_test_info.xml" qualifiers="" type="layout"/><file name="activity_test_question_base" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_test_question_base.xml" qualifiers="" type="layout"/><file name="item_test_instruction" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_test_instruction.xml" qualifiers="" type="layout"/><file name="ic_learning" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_learning.xml" qualifiers="" type="drawable"/><file name="ic_problem_solving" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_problem_solving.xml" qualifiers="" type="drawable"/><file name="ic_stress" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_stress.xml" qualifiers="" type="drawable"/><file name="circle_primary" path="C:\Projects\LeapIQ\app\src\main\res\drawable\circle_primary.xml" qualifiers="" type="drawable"/><file name="rounded_background_light" path="C:\Projects\LeapIQ\app\src\main\res\drawable\rounded_background_light.xml" qualifiers="" type="drawable"/><file name="item_key_point" path="C:\Projects\LeapIQ\app\src\main\res\layout\item_key_point.xml" qualifiers="" type="layout"/><file name="ic_launcher_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="reaction_area_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\reaction_area_background.xml" qualifiers="" type="drawable"/><file name="reaction_pulse_effect" path="C:\Projects\LeapIQ\app\src\main\res\drawable\reaction_pulse_effect.xml" qualifiers="" type="drawable"/><file name="reaction_stimulus_dynamic" path="C:\Projects\LeapIQ\app\src\main\res\drawable\reaction_stimulus_dynamic.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Projects\LeapIQ\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Projects\LeapIQ\app\src\main\res\values\ic_launcher_background.xml" qualifiers=""><color name="ic_launcher_background">#FFFFFF</color></file><file name="slide_in_left" path="C:\Projects\LeapIQ\app\src\main\res\anim\slide_in_left.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="C:\Projects\LeapIQ\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_out_left" path="C:\Projects\LeapIQ\app\src\main\res\anim\slide_out_left.xml" qualifiers="" type="anim"/><file name="slide_out_right" path="C:\Projects\LeapIQ\app\src\main\res\anim\slide_out_right.xml" qualifiers="" type="anim"/><file name="ic_notifications" path="C:\Projects\LeapIQ\app\src\main\res\drawable\ic_notifications.xml" qualifiers="" type="drawable"/><file name="splash_background" path="C:\Projects\LeapIQ\app\src\main\res\drawable\splash_background.xml" qualifiers="" type="drawable"/><file name="tab_selector" path="C:\Projects\LeapIQ\app\src\main\res\drawable\tab_selector.xml" qualifiers="" type="drawable"/><file name="activity_name_input" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_name_input.xml" qualifiers="" type="layout"/><file name="activity_notification_permission" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_notification_permission.xml" qualifiers="" type="layout"/><file name="activity_splash" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_splash.xml" qualifiers="" type="layout"/><file name="activity_welcome" path="C:\Projects\LeapIQ\app\src\main\res\layout\activity_welcome.xml" qualifiers="" type="layout"/><file name="fragment_welcome_page" path="C:\Projects\LeapIQ\app\src\main\res\layout\fragment_welcome_page.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Projects\LeapIQ\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Projects\LeapIQ\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Projects\LeapIQ\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Projects\LeapIQ\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>