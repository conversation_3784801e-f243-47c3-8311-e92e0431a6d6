package com.leapiq.braintraining.ui.tests.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.databinding.ItemTestCardBinding
import com.leapiq.braintraining.data.model.Test

class TestAdapter(
    private val onTestClick: (Test) -> Unit
) : ListAdapter<Test, TestAdapter.TestViewHolder>(DiffCallback) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TestViewHolder {
        val binding = ItemTestCardBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return TestViewHolder(binding)
    }

    override fun onBindViewHolder(holder: TestViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class TestViewHolder(
        private val binding: ItemTestCardBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(test: Test) {
            binding.apply {
                testName.text = test.name
                testCategory.text = test.category.displayName
                durationBadge.text = "5-10 min" // Default duration
                
                // Set test image based on category
                // TODO: Set appropriate image based on test.imageResource
                
                // Show completion status
                if (test.isCompleted) {
                    completionIcon.visibility = View.VISIBLE
                    if (test.lastScore != null) {
                        lastScore.visibility = View.VISIBLE
                        lastScore.text = "Last Score: ${test.lastScore}%"
                    } else {
                        lastScore.visibility = View.GONE
                    }
                } else {
                    completionIcon.visibility = View.GONE
                    lastScore.visibility = View.GONE
                }
                
                root.setOnClickListener {
                    onTestClick(test)
                }
            }
        }
    }

    companion object {
        private val DiffCallback = object : DiffUtil.ItemCallback<Test>() {
            override fun areItemsTheSame(oldItem: Test, newItem: Test): Boolean {
                return oldItem.id == newItem.id
            }

            override fun areContentsTheSame(oldItem: Test, newItem: Test): Boolean {
                return oldItem == newItem
            }
        }
    }
}
