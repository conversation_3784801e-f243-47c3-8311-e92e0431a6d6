package com.leapiq.braintraining

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.leapiq.braintraining.databinding.ActivityWelcomeBinding
import com.leapiq.braintraining.ui.welcome.WelcomePageFragment

class WelcomeActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityWelcomeBinding
    private lateinit var viewPager: ViewPager2
    private lateinit var tabLayout: TabLayout
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityWelcomeBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupViewPager()
        setupButtons()
    }
    
    private fun setupViewPager() {
        viewPager = binding.viewPager
        tabLayout = binding.tabLayout
        
        val adapter = WelcomePagerAdapter(this)
        viewPager.adapter = adapter
        
        // Connect tab layout with view pager
        TabLayoutMediator(tabLayout, viewPager) { _, _ ->
            // Tab layout will show dots automatically
        }.attach()
        
        // Listen for page changes to update button visibility
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                updateButtons(position)
            }
        })
    }
    
    private fun setupButtons() {
        binding.btnSkip.setOnClickListener {
            // Skip to name input
            startNameInputActivity()
        }
        
        binding.btnNext.setOnClickListener {
            if (viewPager.currentItem < 3) {
                viewPager.currentItem += 1
            } else {
                // Last page - go to name input
                startNameInputActivity()
            }
        }
        
        binding.btnPrevious.setOnClickListener {
            if (viewPager.currentItem > 0) {
                viewPager.currentItem -= 1
            }
        }
    }
    
    private fun updateButtons(position: Int) {
        binding.btnPrevious.visibility = if (position == 0) {
            android.view.View.INVISIBLE
        } else {
            android.view.View.VISIBLE
        }
        
        binding.btnNext.text = if (position == 3) {
            "Get Started"
        } else {
            "Next"
        }
    }
    
    private fun startNameInputActivity() {
        startActivity(Intent(this, NameInputActivity::class.java))
        finish()
    }
    
    private class WelcomePagerAdapter(activity: WelcomeActivity) : FragmentStateAdapter(activity) {
        override fun getItemCount(): Int = 4
        
        override fun createFragment(position: Int): Fragment {
            return WelcomePageFragment.newInstance(position)
        }
    }
}
