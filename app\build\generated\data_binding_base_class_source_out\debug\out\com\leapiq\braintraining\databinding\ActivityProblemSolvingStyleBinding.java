// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityProblemSolvingStyleBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnNext;

  @NonNull
  public final Button btnSubmit;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final RecyclerView optionsRecycler;

  @NonNull
  public final TextView problemStatement;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView progressText;

  @NonNull
  public final TextView questionNumberText;

  @NonNull
  public final TextView questionText;

  @NonNull
  public final TextView questionTypeText;

  @NonNull
  public final LinearLayout timerContainer;

  @NonNull
  public final TextView timerText;

  @NonNull
  public final Toolbar toolbar;

  private ActivityProblemSolvingStyleBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnNext, @NonNull Button btnSubmit, @NonNull TextView instructionText,
      @NonNull RecyclerView optionsRecycler, @NonNull TextView problemStatement,
      @NonNull ProgressBar progressBar, @NonNull TextView progressText,
      @NonNull TextView questionNumberText, @NonNull TextView questionText,
      @NonNull TextView questionTypeText, @NonNull LinearLayout timerContainer,
      @NonNull TextView timerText, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.btnNext = btnNext;
    this.btnSubmit = btnSubmit;
    this.instructionText = instructionText;
    this.optionsRecycler = optionsRecycler;
    this.problemStatement = problemStatement;
    this.progressBar = progressBar;
    this.progressText = progressText;
    this.questionNumberText = questionNumberText;
    this.questionText = questionText;
    this.questionTypeText = questionTypeText;
    this.timerContainer = timerContainer;
    this.timerText = timerText;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityProblemSolvingStyleBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityProblemSolvingStyleBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_problem_solving_style, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityProblemSolvingStyleBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_next;
      Button btnNext = ViewBindings.findChildViewById(rootView, id);
      if (btnNext == null) {
        break missingId;
      }

      id = R.id.btn_submit;
      Button btnSubmit = ViewBindings.findChildViewById(rootView, id);
      if (btnSubmit == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.options_recycler;
      RecyclerView optionsRecycler = ViewBindings.findChildViewById(rootView, id);
      if (optionsRecycler == null) {
        break missingId;
      }

      id = R.id.problem_statement;
      TextView problemStatement = ViewBindings.findChildViewById(rootView, id);
      if (problemStatement == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.progress_text;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      id = R.id.question_number_text;
      TextView questionNumberText = ViewBindings.findChildViewById(rootView, id);
      if (questionNumberText == null) {
        break missingId;
      }

      id = R.id.question_text;
      TextView questionText = ViewBindings.findChildViewById(rootView, id);
      if (questionText == null) {
        break missingId;
      }

      id = R.id.question_type_text;
      TextView questionTypeText = ViewBindings.findChildViewById(rootView, id);
      if (questionTypeText == null) {
        break missingId;
      }

      id = R.id.timer_container;
      LinearLayout timerContainer = ViewBindings.findChildViewById(rootView, id);
      if (timerContainer == null) {
        break missingId;
      }

      id = R.id.timer_text;
      TextView timerText = ViewBindings.findChildViewById(rootView, id);
      if (timerText == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityProblemSolvingStyleBinding((LinearLayout) rootView, btnNext, btnSubmit,
          instructionText, optionsRecycler, problemStatement, progressBar, progressText,
          questionNumberText, questionText, questionTypeText, timerContainer, timerText, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
