package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityMentalArithmeticBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Mental Arithmetic Game
 * Players solve math problems mentally without calculator
 * Progressive difficulty with larger numbers and mixed operations
 */
class MentalArithmeticActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMentalArithmeticBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "math_1"

    // Mental arithmetic specific
    private var currentProblem = ""
    private var correctAnswer = 0
    private var problemStartTime = 0L
    private var currentTrial = 0
    private var trialsPerRound = 10
    private var reactionTimes = mutableListOf<Long>()

    // Math operations
    private enum class Operation(val symbol: String) {
        ADD("+"), SUBTRACT("-"), MULTIPLY("×"), DIVIDE("÷")
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMentalArithmeticBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.mental_arithmetic)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Solve the math problems mentally!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup number pad
            setupNumberPad()

            // Setup control buttons
            btnClear.setOnClickListener {
                clearInput()
            }

            btnSubmit.setOnClickListener {
                submitAnswer()
            }

            btnNegative.setOnClickListener {
                toggleNegative()
            }
        }
    }

    private fun setupNumberPad() {
        binding.apply {
            btn0.setOnClickListener { addDigit("0") }
            btn1.setOnClickListener { addDigit("1") }
            btn2.setOnClickListener { addDigit("2") }
            btn3.setOnClickListener { addDigit("3") }
            btn4.setOnClickListener { addDigit("4") }
            btn5.setOnClickListener { addDigit("5") }
            btn6.setOnClickListener { addDigit("6") }
            btn7.setOnClickListener { addDigit("7") }
            btn8.setOnClickListener { addDigit("8") }
            btn9.setOnClickListener { addDigit("9") }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentTrial = 0
        reactionTimes.clear()
        trialsPerRound = getTrialsPerRound(currentLevel)
        
        binding.trialText.text = "Problem 1/$trialsPerRound"
        
        generateNextProblem()
    }

    private fun getTrialsPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 10      // 10 problems per round
            in 6..10 -> 12     // 12 problems per round
            in 11..15 -> 15    // 15 problems per round
            in 16..20 -> 18    // 18 problems per round
            else -> 20         // 20 problems per round
        }
    }

    private fun generateNextProblem() {
        if (currentTrial >= trialsPerRound) {
            roundComplete()
            return
        }

        currentTrial++
        binding.trialText.text = "Problem $currentTrial/$trialsPerRound"
        
        val difficulty = getDifficulty(currentLevel)
        val operations = getAvailableOperations(currentLevel)
        val operation = operations.random()
        
        generateProblem(operation, difficulty)
        displayProblem()
        clearInput()
        problemStartTime = System.currentTimeMillis()
    }

    private fun getDifficulty(level: Int): Int {
        return when (level) {
            in 1..3 -> 1       // Single digit numbers
            in 4..6 -> 2       // Two digit numbers
            in 7..10 -> 3      // Larger numbers
            in 11..15 -> 4     // Mixed complexity
            in 16..20 -> 5     // High complexity
            else -> 6          // Expert level
        }
    }

    private fun getAvailableOperations(level: Int): List<Operation> {
        return when (level) {
            in 1..3 -> listOf(Operation.ADD, Operation.SUBTRACT)
            in 4..6 -> listOf(Operation.ADD, Operation.SUBTRACT, Operation.MULTIPLY)
            in 7..10 -> listOf(Operation.ADD, Operation.SUBTRACT, Operation.MULTIPLY, Operation.DIVIDE)
            else -> listOf(Operation.ADD, Operation.SUBTRACT, Operation.MULTIPLY, Operation.DIVIDE)
        }
    }

    private fun generateProblem(operation: Operation, difficulty: Int) {
        when (operation) {
            Operation.ADD -> generateAddition(difficulty)
            Operation.SUBTRACT -> generateSubtraction(difficulty)
            Operation.MULTIPLY -> generateMultiplication(difficulty)
            Operation.DIVIDE -> generateDivision(difficulty)
        }
    }

    private fun generateAddition(difficulty: Int) {
        val (min, max) = getNumberRange(difficulty)
        val a = Random.nextInt(min, max + 1)
        val b = Random.nextInt(min, max + 1)
        
        currentProblem = "$a + $b"
        correctAnswer = a + b
    }

    private fun generateSubtraction(difficulty: Int) {
        val (min, max) = getNumberRange(difficulty)
        val a = Random.nextInt(min, max + 1)
        val b = Random.nextInt(min, a + 1) // Ensure positive result
        
        currentProblem = "$a - $b"
        correctAnswer = a - b
    }

    private fun generateMultiplication(difficulty: Int) {
        val (min, max) = when (difficulty) {
            1 -> Pair(2, 9)    // Single digit
            2 -> Pair(2, 12)   // Times tables
            3 -> Pair(10, 25)  // Larger numbers
            4 -> Pair(10, 50)  // Mixed
            5 -> Pair(20, 99)  // High complexity
            else -> Pair(10, 99)
        }
        
        val a = Random.nextInt(min, max + 1)
        val b = Random.nextInt(2, if (difficulty <= 2) 10 else 15)
        
        currentProblem = "$a × $b"
        correctAnswer = a * b
    }

    private fun generateDivision(difficulty: Int) {
        // Generate division that results in whole numbers
        val (min, max) = getNumberRange(difficulty)
        val divisor = Random.nextInt(2, if (difficulty <= 2) 10 else 15)
        val quotient = Random.nextInt(min / divisor + 1, max / divisor + 1)
        val dividend = divisor * quotient
        
        currentProblem = "$dividend ÷ $divisor"
        correctAnswer = quotient
    }

    private fun getNumberRange(difficulty: Int): Pair<Int, Int> {
        return when (difficulty) {
            1 -> Pair(1, 9)        // 1-9
            2 -> Pair(10, 99)      // 10-99
            3 -> Pair(50, 200)     // 50-200
            4 -> Pair(100, 500)    // 100-500
            5 -> Pair(200, 999)    // 200-999
            else -> Pair(500, 9999) // 500-9999
        }
    }

    private fun displayProblem() {
        binding.problemDisplay.text = "$currentProblem = ?"
        binding.instructionText.text = "Calculate mentally and enter your answer:"
    }

    private fun addDigit(digit: String) {
        val currentInput = binding.answerInput.text.toString()
        if (currentInput.length < 8) { // Limit input length
            binding.answerInput.text = currentInput + digit
        }
    }

    private fun clearInput() {
        binding.answerInput.text = ""
    }

    private fun toggleNegative() {
        val currentInput = binding.answerInput.text.toString()
        if (currentInput.isNotEmpty()) {
            if (currentInput.startsWith("-")) {
                binding.answerInput.text = currentInput.substring(1)
            } else {
                binding.answerInput.text = "-$currentInput"
            }
        }
    }

    private fun submitAnswer() {
        val userInput = binding.answerInput.text.toString()
        if (userInput.isEmpty()) return
        
        val userAnswer = userInput.toIntOrNull() ?: return
        val reactionTime = System.currentTimeMillis() - problemStartTime
        
        totalAttempts++
        val isCorrect = userAnswer == correctAnswer
        
        if (isCorrect) {
            totalCorrect++
            reactionTimes.add(reactionTime)
            showFeedback("Correct! (${reactionTime}ms)", true)
        } else {
            showFeedback("Wrong! Answer was $correctAnswer", false)
        }
        
        // Continue to next problem after delay
        Handler(Looper.getMainLooper()).postDelayed({
            generateNextProblem()
        }, 1500)
    }

    private fun showFeedback(message: String, isCorrect: Boolean) {
        binding.instructionText.text = message
        binding.instructionText.setTextColor(
            resources.getColor(
                if (isCorrect) R.color.stroop_green else R.color.stroop_red,
                null
            )
        )
        
        // Reset color after delay
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.setTextColor(
                resources.getColor(R.color.text_secondary, null)
            )
        }, 1500)
    }

    private fun roundComplete() {
        val avgReactionTime = if (reactionTimes.isNotEmpty()) {
            reactionTimes.average().toLong()
        } else 0L
        
        val roundAccuracy = if (trialsPerRound > 0) {
            (totalCorrect.toDouble() / trialsPerRound)
        } else 1.0
        
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Accuracy: ${(roundAccuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentTrial = 0
            reactionTimes.clear()
            
            Handler(Looper.getMainLooper()).postDelayed({
                generateNextProblem()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0
        val avgReactionTime = if (reactionTimes.isNotEmpty()) reactionTimes.average().toLong() else 0L

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7, // 70% threshold
                timeSpentMs = totalTime / maxRounds,
                attempts = trialsPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = if (avgReactionTime > 0) (accuracy * 1000 / avgReactionTime * 100).toInt() else (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
                Problems Solved: $totalAttempts

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Best Avg Time: ${avgReactionTime}ms

                Math Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Solve math problems using mental calculation

                📋 RULES:
                • Calculate the answer in your head (no calculator!)
                • Enter your answer using the number pad
                • Work as quickly and accurately as possible
                • Complete problems to advance levels

                💡 TIPS:
                • Use mental math strategies
                • Break down complex problems
                • Practice estimation for quick checks
                • Speed improves with practice

                🏆 SCORING:
                • Accuracy = correct answers / total problems
                • Speed = average calculation time
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
