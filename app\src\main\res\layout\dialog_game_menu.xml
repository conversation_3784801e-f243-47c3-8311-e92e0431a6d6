<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp"
    android:background="@drawable/dialog_background">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Game Menu"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <Button
        android:id="@+id/btn_continue"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Continue"
        android:textColor="@android:color/white"
        android:background="@drawable/button_primary"
        android:layout_marginBottom="12dp"
        android:padding="16dp" />

    <Button
        android:id="@+id/btn_restart"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Restart Level"
        android:textColor="@color/text_primary"
        android:background="@drawable/button_secondary"
        android:layout_marginBottom="12dp"
        android:padding="16dp" />

    <Button
        android:id="@+id/btn_how_to_play"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="How to Play"
        android:textColor="@color/text_primary"
        android:background="@drawable/button_secondary"
        android:layout_marginBottom="12dp"
        android:padding="16dp" />

    <Button
        android:id="@+id/btn_quit_menu"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Quit to Games"
        android:textColor="@android:color/white"
        android:background="@drawable/button_danger"
        android:padding="16dp" />

</LinearLayout>
