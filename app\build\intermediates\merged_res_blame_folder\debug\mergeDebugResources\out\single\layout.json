[{"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_achievement.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_achievement.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/fragment_progress.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/fragment_progress.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_test_progress.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_test_progress.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_stroop_test.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_stroop_test.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_problem_solving_style.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_problem_solving_style.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_anagrams.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_anagrams.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_vocabulary.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_vocabulary.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_card_matching.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_card_matching.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_learning_style.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_learning_style.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_visual_search.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_visual_search.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_hanoi_tower.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_hanoi_tower.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_test_instruction.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_test_instruction.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_key_point.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_key_point.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/fragment_today.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/fragment_today.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_word_search_cell.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_word_search_cell.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_estimation.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_estimation.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_search_shape.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_search_shape.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_game_card.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_game_card.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_word_association.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_word_association.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_recent_activity.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_recent_activity.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_logic_clue.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_logic_clue.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_sequence_recall.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_sequence_recall.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_word_search_word.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_word_search_word.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/fragment_profile.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/fragment_profile.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_mental_arithmetic.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_mental_arithmetic.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_memory_grid.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_memory_grid.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_logic_grid_cell.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_logic_grid_cell.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_tube_sort.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_tube_sort.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_all_results.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_all_results.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_speed_math.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_speed_math.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_focus_challenge.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_focus_challenge.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_memory_card.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_memory_card.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_reaction_time.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_reaction_time.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_my_results.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_my_results.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_tower_of_hanoi.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_tower_of_hanoi.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_number_sequences.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_number_sequences.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/fragment_welcome_page.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/fragment_welcome_page.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_test_question_base.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_test_question_base.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_test_info.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_test_info.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_pattern_memory.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_pattern_memory.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_test_result.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_test_result.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_number_memory.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_number_memory.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_name_input.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_name_input.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_word_search.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_word_search.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/fragment_games.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/fragment_games.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_logic_grid_header.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_logic_grid_header.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_game_result.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_game_result.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_main.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_main.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_logical_reasoning.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_logical_reasoning.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_analytics.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_analytics.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/fragment_tests.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/fragment_tests.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_test_card.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_test_card.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_stress_response_option.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_stress_response_option.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_daily_challenge.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_daily_challenge.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/dialog_game_menu.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/dialog_game_menu.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_detailed_score.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_detailed_score.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_logic_grid_row.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_logic_grid_row.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_tube.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_tube.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_learning_style_option.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_learning_style_option.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_test_progress.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_test_progress.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_spatial_rotation.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_spatial_rotation.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_all_results.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_all_results.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_pattern_completion.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_pattern_completion.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_stress_response.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_stress_response.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_problem_solving_option.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_problem_solving_option.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_category_accuracy.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_category_accuracy.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_test_results.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_test_results.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_notification_permission.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_notification_permission.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/layout_header.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/layout_header.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_logic_puzzles.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_logic_puzzles.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/activity_welcome.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/activity_welcome.xml"}, {"merged": "com.leapiq.braintraining.app-mergeDebugResources-3:/layout/item_pattern_cell.xml", "source": "com.leapiq.braintraining.app-main-6:/layout/item_pattern_cell.xml"}]