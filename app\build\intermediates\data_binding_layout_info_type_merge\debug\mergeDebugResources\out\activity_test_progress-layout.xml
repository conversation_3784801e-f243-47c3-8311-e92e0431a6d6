<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_test_progress" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_test_progress.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_test_progress_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="489" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="50"/></Target><Target id="@+id/empty_state_container" view="LinearLayout"><Expressions/><location startLine="21" startOffset="4" endLine="54" endOffset="18"/></Target><Target id="@+id/content_container" view="ScrollView"><Expressions/><location startLine="57" startOffset="4" endLine="487" endOffset="16"/></Target><Target id="@+id/times_completed_text" view="TextView"><Expressions/><location startLine="107" startOffset="28" endLine="114" endOffset="58"/></Target><Target id="@+id/best_score_text" view="TextView"><Expressions/><location startLine="135" startOffset="28" endLine="142" endOffset="58"/></Target><Target id="@+id/average_score_text" view="TextView"><Expressions/><location startLine="163" startOffset="28" endLine="170" endOffset="58"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="185" startOffset="20" endLine="192" endOffset="43"/></Target><Target id="@+id/trend_indicator" view="TextView"><Expressions/><location startLine="227" startOffset="24" endLine="233" endOffset="61"/></Target><Target id="@+id/trend_description" view="TextView"><Expressions/><location startLine="235" startOffset="24" endLine="243" endOffset="54"/></Target><Target id="@+id/fastest_time_text" view="TextView"><Expressions/><location startLine="302" startOffset="32" endLine="309" endOffset="62"/></Target><Target id="@+id/average_time_text" view="TextView"><Expressions/><location startLine="328" startOffset="32" endLine="335" endOffset="62"/></Target><Target id="@+id/streak_text" view="TextView"><Expressions/><location startLine="362" startOffset="32" endLine="369" endOffset="62"/></Target><Target id="@+id/consistency_text" view="TextView"><Expressions/><location startLine="388" startOffset="32" endLine="395" endOffset="62"/></Target><Target id="@+id/recommendations_container" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="408" startOffset="12" endLine="452" endOffset="63"/></Target><Target id="@+id/recommendation_title" view="TextView"><Expressions/><location startLine="432" startOffset="20" endLine="440" endOffset="59"/></Target><Target id="@+id/recommendation_description" view="TextView"><Expressions/><location startLine="442" startOffset="20" endLine="448" endOffset="49"/></Target><Target id="@+id/results_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="476" startOffset="20" endLine="479" endOffset="62"/></Target></Targets></Layout>