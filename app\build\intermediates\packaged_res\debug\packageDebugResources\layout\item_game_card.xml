<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Game Image (Rectangle) -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="120dp">

            <ImageView
                android:id="@+id/game_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/primary_light_blue_light"
                tools:src="@drawable/ic_games" />

            <!-- Lock Icon for Premium/Locked Games -->
            <ImageView
                android:id="@+id/lock_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="top|end"
                android:layout_margin="8dp"
                android:background="@drawable/header_icon_background"
                android:padding="4dp"
                android:src="@drawable/ic_lock"
                android:tint="@color/text_secondary"
                android:visibility="gone"
                tools:visibility="visible" />

            <!-- Premium Badge -->
            <TextView
                android:id="@+id/premium_badge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|start"
                android:layout_margin="8dp"
                android:background="@drawable/premium_button_background"
                android:paddingStart="8dp"
                android:paddingTop="4dp"
                android:paddingEnd="8dp"
                android:paddingBottom="4dp"
                android:text="Premium"
                android:textColor="@color/text_primary"
                android:textSize="10sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:visibility="visible" />

        </FrameLayout>

        <!-- Game Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- Game Name -->
            <TextView
                android:id="@+id/game_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Card Matching"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="Card Matching" />

            <!-- Category -->
            <TextView
                android:id="@+id/game_category"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center"
                android:text="Memory"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="Memory" />

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/game_progress"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="4dp"
                android:layout_marginTop="8dp"
                android:max="100"
                android:progress="60"
                android:progressTint="@color/primary_light_blue"
                tools:progress="60" />

            <!-- Progress Text -->
            <TextView
                android:id="@+id/progress_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center"
                android:text="6/10 levels"
                android:textColor="@color/text_secondary"
                android:textSize="10sp"
                tools:text="6/10 levels" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
