package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityWordAssociationBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Word Association Game
 * Players choose semantically related words from options
 * Tests semantic memory, vocabulary knowledge, and word relationships
 */
class WordAssociationActivity : AppCompatActivity() {

    private lateinit var binding: ActivityWordAssociationBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "language_1"

    // Word association specific
    private var currentTargetWord = ""
    private var correctAssociation = ""
    private var answerOptions = mutableListOf<String>()
    private var problemStartTime = 0L
    private var currentTrial = 0
    private var trialsPerRound = 10
    private var reactionTimes = mutableListOf<Long>()

    // Word association database
    private val wordAssociations = mapOf(
        // Level 1-3: Basic concrete associations
        "cat" to listOf("dog", "pet", "animal", "kitten", "meow"),
        "sun" to listOf("moon", "bright", "hot", "day", "light"),
        "car" to listOf("drive", "road", "wheel", "engine", "vehicle"),
        "book" to listOf("read", "page", "story", "library", "author"),
        "water" to listOf("drink", "wet", "ocean", "swim", "blue"),
        "tree" to listOf("leaf", "green", "forest", "branch", "wood"),
        "house" to listOf("home", "door", "roof", "family", "live"),
        "food" to listOf("eat", "hungry", "cook", "meal", "taste"),
        
        // Level 4-6: More abstract associations
        "happy" to listOf("smile", "joy", "laugh", "cheerful", "glad"),
        "music" to listOf("song", "dance", "sound", "melody", "rhythm"),
        "school" to listOf("learn", "teacher", "student", "education", "study"),
        "friend" to listOf("buddy", "companion", "trust", "loyal", "support"),
        "time" to listOf("clock", "hour", "minute", "past", "future"),
        "love" to listOf("heart", "care", "affection", "romance", "family"),
        "work" to listOf("job", "office", "career", "effort", "labor"),
        "dream" to listOf("sleep", "wish", "hope", "imagination", "fantasy"),
        
        // Level 7-10: Complex associations
        "freedom" to listOf("liberty", "independence", "choice", "democracy", "rights"),
        "wisdom" to listOf("knowledge", "experience", "insight", "understanding", "sage"),
        "courage" to listOf("brave", "fearless", "bold", "heroic", "strength"),
        "justice" to listOf("fair", "law", "equality", "rights", "court"),
        "beauty" to listOf("attractive", "elegant", "gorgeous", "aesthetic", "art"),
        "peace" to listOf("calm", "harmony", "tranquil", "serenity", "quiet"),
        "success" to listOf("achievement", "victory", "accomplishment", "triumph", "win"),
        "creativity" to listOf("imagination", "artistic", "innovative", "original", "inspiration"),
        
        // Level 11-15: Abstract concepts
        "philosophy" to listOf("wisdom", "thinking", "ethics", "logic", "meaning"),
        "democracy" to listOf("freedom", "voting", "equality", "government", "people"),
        "evolution" to listOf("change", "adaptation", "development", "progress", "species"),
        "psychology" to listOf("mind", "behavior", "emotion", "mental", "therapy"),
        "technology" to listOf("computer", "innovation", "digital", "science", "modern"),
        "culture" to listOf("tradition", "society", "customs", "heritage", "values"),
        "economy" to listOf("money", "business", "trade", "market", "finance"),
        "environment" to listOf("nature", "ecology", "climate", "conservation", "green"),
        
        // Level 16+: Advanced abstract concepts
        "consciousness" to listOf("awareness", "mind", "perception", "thought", "existence"),
        "metaphor" to listOf("symbol", "comparison", "figurative", "analogy", "poetry"),
        "paradigm" to listOf("model", "framework", "pattern", "concept", "theory"),
        "synthesis" to listOf("combination", "merge", "unite", "blend", "integration"),
        "dialectic" to listOf("debate", "discussion", "argument", "reasoning", "logic"),
        "empirical" to listOf("evidence", "observation", "experience", "factual", "scientific"),
        "aesthetic" to listOf("beauty", "artistic", "visual", "design", "appreciation"),
        "existential" to listOf("existence", "meaning", "purpose", "life", "philosophy")
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWordAssociationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.word_association)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Choose the word most related to the target word!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup answer buttons
            setupAnswerButtons()
        }
    }

    private fun setupAnswerButtons() {
        binding.apply {
            btnOption1.setOnClickListener { selectAnswer(0) }
            btnOption2.setOnClickListener { selectAnswer(1) }
            btnOption3.setOnClickListener { selectAnswer(2) }
            btnOption4.setOnClickListener { selectAnswer(3) }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentTrial = 0
        reactionTimes.clear()
        trialsPerRound = getTrialsPerRound(currentLevel)
        
        binding.trialText.text = "Word 1/$trialsPerRound"
        
        generateNextAssociation()
    }

    private fun getTrialsPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 10      // 10 words per round
            in 6..10 -> 12     // 12 words per round
            in 11..15 -> 15    // 15 words per round
            in 16..20 -> 18    // 18 words per round
            else -> 20         // 20 words per round
        }
    }

    private fun generateNextAssociation() {
        if (currentTrial >= trialsPerRound) {
            roundComplete()
            return
        }

        currentTrial++
        binding.trialText.text = "Word $currentTrial/$trialsPerRound"
        
        val availableWords = getAvailableWords(currentLevel)
        val selectedWord = availableWords.random()
        
        generateAssociationPuzzle(selectedWord)
        problemStartTime = System.currentTimeMillis()
    }

    private fun getAvailableWords(level: Int): List<String> {
        return when (level) {
            in 1..3 -> listOf("cat", "sun", "car", "book", "water", "tree", "house", "food")
            in 4..6 -> listOf("happy", "music", "school", "friend", "time", "love", "work", "dream")
            in 7..10 -> listOf("freedom", "wisdom", "courage", "justice", "beauty", "peace", "success", "creativity")
            in 11..15 -> listOf("philosophy", "democracy", "evolution", "psychology", "technology", "culture", "economy", "environment")
            else -> listOf("consciousness", "metaphor", "paradigm", "synthesis", "dialectic", "empirical", "aesthetic", "existential")
        }
    }

    private fun generateAssociationPuzzle(targetWord: String) {
        currentTargetWord = targetWord
        val associations = wordAssociations[targetWord] ?: listOf("related", "connected", "similar", "linked")
        
        // Choose correct association
        correctAssociation = associations.random()
        
        // Generate wrong options from other word categories
        val wrongOptions = mutableListOf<String>()
        val allWords = wordAssociations.keys.toList()
        val otherWords = allWords.filter { it != targetWord }
        
        while (wrongOptions.size < 3) {
            val randomWord = otherWords.random()
            val randomAssociations = wordAssociations[randomWord] ?: continue
            val wrongOption = randomAssociations.random()
            
            if (wrongOption !in wrongOptions && wrongOption != correctAssociation) {
                wrongOptions.add(wrongOption)
            }
        }
        
        // Create answer options
        answerOptions = mutableListOf(correctAssociation)
        answerOptions.addAll(wrongOptions)
        answerOptions.shuffle()
        
        displayAssociationPuzzle()
    }

    private fun displayAssociationPuzzle() {
        binding.apply {
            targetWordDisplay.text = currentTargetWord.uppercase()
            
            btnOption1.text = answerOptions[0]
            btnOption2.text = answerOptions[1]
            btnOption3.text = answerOptions[2]
            btnOption4.text = answerOptions[3]
        }
    }

    private fun selectAnswer(optionIndex: Int) {
        val selectedAnswer = answerOptions[optionIndex]
        val reactionTime = System.currentTimeMillis() - problemStartTime
        
        totalAttempts++
        val isCorrect = selectedAnswer == correctAssociation
        
        if (isCorrect) {
            totalCorrect++
            reactionTimes.add(reactionTime)
        }
        
        showFeedback(selectedAnswer, isCorrect, reactionTime)
        
        // Continue to next association after delay
        Handler(Looper.getMainLooper()).postDelayed({
            generateNextAssociation()
        }, 2000)
    }

    private fun showFeedback(selectedAnswer: String, isCorrect: Boolean, reactionTime: Long) {
        val feedback = if (isCorrect) {
            "Correct! '$currentTargetWord' → '$correctAssociation' (${reactionTime}ms)"
        } else {
            "Wrong! '$currentTargetWord' → '$correctAssociation' (not '$selectedAnswer')"
        }
        
        binding.instructionText.text = feedback
        binding.instructionText.setTextColor(
            ContextCompat.getColor(this, if (isCorrect) R.color.stroop_green else R.color.stroop_red)
        )
        
        // Reset color after delay
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.text = "Choose the word most related to the target word!"
            binding.instructionText.setTextColor(
                ContextCompat.getColor(this, R.color.text_secondary)
            )
        }, 2000)
    }

    private fun roundComplete() {
        val avgReactionTime = if (reactionTimes.isNotEmpty()) {
            reactionTimes.average().toLong()
        } else 0L
        
        val roundAccuracy = if (trialsPerRound > 0) {
            (totalCorrect.toDouble() / trialsPerRound)
        } else 1.0
        
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Accuracy: ${(roundAccuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentTrial = 0
            reactionTimes.clear()
            
            Handler(Looper.getMainLooper()).postDelayed({
                generateNextAssociation()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0
        val avgReactionTime = if (reactionTimes.isNotEmpty()) reactionTimes.average().toLong() else 0L

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7,
                timeSpentMs = totalTime / maxRounds,
                attempts = trialsPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
                Associations: $totalAttempts

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Total Associations: $totalAttempts

                Word Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Choose words that are most related to the target word

                📋 RULES:
                • Look at the target word displayed prominently
                • Choose the option that has the strongest association
                • Think about semantic relationships and meanings
                • Consider synonyms, categories, and related concepts

                💡 TIPS:
                • Think about word meanings and connections
                • Consider different types of relationships (synonyms, categories, functions)
                • Trust your first instinct for word associations
                • Higher levels have more abstract concepts

                🏆 SCORING:
                • Accuracy = correct associations / total words
                • Speed = average association time
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
