<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_problem_solving_style" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_problem_solving_style.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_problem_solving_style_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="398" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="50"/></Target><Target id="@+id/progress_text" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="42"/></Target><Target id="@+id/timer_container" view="LinearLayout"><Expressions/><location startLine="45" startOffset="12" endLine="71" endOffset="26"/></Target><Target id="@+id/timer_text" view="TextView"><Expressions/><location startLine="62" startOffset="16" endLine="69" endOffset="46"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="75" startOffset="8" endLine="81" endOffset="62"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="86" startOffset="4" endLine="96" endOffset="32"/></Target><Target id="@+id/question_number_text" view="TextView"><Expressions/><location startLine="131" startOffset="24" endLine="139" endOffset="54"/></Target><Target id="@+id/question_type_text" view="TextView"><Expressions/><location startLine="141" startOffset="24" endLine="149" endOffset="51"/></Target><Target id="@+id/problem_statement" view="TextView"><Expressions/><location startLine="163" startOffset="20" endLine="173" endOffset="48"/></Target><Target id="@+id/question_text" view="TextView"><Expressions/><location startLine="176" startOffset="20" endLine="184" endOffset="56"/></Target><Target id="@+id/options_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="212" startOffset="20" endLine="215" endOffset="62"/></Target><Target id="@+id/btn_next" view="Button"><Expressions/><location startLine="374" startOffset="8" endLine="383" endOffset="39"/></Target><Target id="@+id/btn_submit" view="Button"><Expressions/><location startLine="385" startOffset="8" endLine="394" endOffset="39"/></Target></Targets></Layout>