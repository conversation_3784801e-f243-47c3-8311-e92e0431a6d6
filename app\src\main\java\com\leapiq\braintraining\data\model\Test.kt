package com.leapiq.braintraining.data.model

import java.util.Date

data class Test(
    val id: String,
    val name: String,
    val title: String = name, // Add title property
    val category: TestCategory,
    val description: String,
    val imageResource: String,
    val estimatedDuration: Int = 10, // Add estimated duration in minutes
    val isCompleted: Boolean = false,
    val lastScore: Int? = null, // percentage score
    val lastCompletedAt: Date? = null,
    val timesCompleted: Int = 0
)

enum class TestCategory(val displayName: String) {
    ALL("All"),
    COGNITIVE("Cognitive"),
    PERSONALITY("Personality")
}

/**
 * Represents the result of a single test question/task
 */
data class TestQuestionResult(
    val questionNumber: Int,
    val isCorrect: Boolean? = null, // null for personality tests
    val responseTime: Long,
    val selectedAnswer: String? = null,
    val correctAnswer: String? = null,
    val confidence: Int? = null // 1-5 scale for personality tests
)

/**
 * Represents the complete result of a test session
 */
data class TestResult(
    val testId: String,
    val testType: TestType,
    val questions: List<TestQuestionResult>,
    val totalTimeMs: Long,
    val score: Int, // 0-100 for cognitive, custom for personality
    val completedAt: Date = Date(),
    val detailedScores: Map<String, Double> = emptyMap(), // For multi-dimensional scoring
    val insights: List<String> = emptyList(),
    val recommendations: List<String> = emptyList()
) {
    val accuracyPercentage: Int
        get() = if (testType == TestType.COGNITIVE) {
            val correctAnswers = questions.count { it.isCorrect == true }
            if (questions.isNotEmpty()) (correctAnswers * 100) / questions.size else 0
        } else score

    val averageResponseTime: Long
        get() = if (questions.isNotEmpty()) questions.map { it.responseTime }.average().toLong() else 0L
}

/**
 * Represents overall progress for a specific test
 */
data class TestProgress(
    val testId: String,
    val timesCompleted: Int,
    val bestScore: Int,
    val averageScore: Double,
    val totalTimeTaken: Long,
    val lastCompletedAt: Date?,
    val testResults: List<TestResult> = emptyList(),
    val improvementTrend: Double = 0.0 // Positive = improving, negative = declining
) {
    val bestScorePercentage: Int
        get() = bestScore

    val averageScorePercentage: Int
        get() = averageScore.toInt()
}

enum class TestType {
    COGNITIVE,    // Scored on accuracy and speed
    PERSONALITY   // Scored on personality dimensions
}
