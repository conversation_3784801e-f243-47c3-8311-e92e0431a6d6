<?xml version="1.0" encoding="utf-8"?>
<shape xmlns:android="http://schemas.android.com/apk/res/android"
    android:shape="oval">
    
    <!-- Gradient fill for depth -->
    <gradient
        android:startColor="#FFFFFF"
        android:endColor="@color/stroop_blue"
        android:angle="45"
        android:type="radial"
        android:gradientRadius="80dp" />
    
    <!-- Thicker, more prominent border -->
    <stroke
        android:width="6dp"
        android:color="@color/primary_light_blue_dark" />
        
</shape>
