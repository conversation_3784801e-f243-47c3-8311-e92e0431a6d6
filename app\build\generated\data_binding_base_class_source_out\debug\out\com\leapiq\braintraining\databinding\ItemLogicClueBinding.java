// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemLogicClueBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout clueContainer;

  @NonNull
  public final TextView clueNumber;

  @NonNull
  public final TextView clueText;

  @NonNull
  public final TextView clueType;

  private ItemLogicClueBinding(@NonNull LinearLayout rootView, @NonNull LinearLayout clueContainer,
      @NonNull TextView clueNumber, @NonNull TextView clueText, @NonNull TextView clueType) {
    this.rootView = rootView;
    this.clueContainer = clueContainer;
    this.clueNumber = clueNumber;
    this.clueText = clueText;
    this.clueType = clueType;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemLogicClueBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemLogicClueBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_logic_clue, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemLogicClueBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      LinearLayout clueContainer = (LinearLayout) rootView;

      id = R.id.clue_number;
      TextView clueNumber = ViewBindings.findChildViewById(rootView, id);
      if (clueNumber == null) {
        break missingId;
      }

      id = R.id.clue_text;
      TextView clueText = ViewBindings.findChildViewById(rootView, id);
      if (clueText == null) {
        break missingId;
      }

      id = R.id.clue_type;
      TextView clueType = ViewBindings.findChildViewById(rootView, id);
      if (clueType == null) {
        break missingId;
      }

      return new ItemLogicClueBinding((LinearLayout) rootView, clueContainer, clueNumber, clueText,
          clueType);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
