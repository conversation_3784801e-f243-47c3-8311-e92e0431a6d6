<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp">

    <TextView
        android:id="@+id/word_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="WORD"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold"
        android:gravity="center"
        tools:text="FREEDOM" />

</LinearLayout>
