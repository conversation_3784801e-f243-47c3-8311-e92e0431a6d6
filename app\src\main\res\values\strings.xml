<resources>
    <string name="app_name">LeapIQ</string>
    
    <!-- Navigation -->
    <string name="nav_today">Today</string>
    <string name="nav_games">Games</string>
    <string name="nav_tests">Tests</string>
    <string name="nav_progress">Progress</string>
    
    <!-- Header -->
    <string name="welcome_user">Welcome User</string>
    <string name="premium">Premium</string>
    <string name="streak">Streak</string>
    <string name="settings">Settings</string>
    
    <!-- Today Page -->
    <string name="daily_challenges">Daily Challenges</string>
    <string name="start_challenge">Start Challenge</string>
    
    <!-- Games Page -->
    <string name="all_games">All</string>
    <string name="memory_games">Memory</string>
    <string name="attention_games">Attention</string>
    <string name="math_games">Math</string>
    <string name="logic_games">Logic</string>
    <string name="language_games">Language</string>
    
    <!-- Memory Games -->
    <string name="card_matching">Card Matching</string>
    <string name="sequence_recall">Sequence Recall</string>
    <string name="pattern_memory">Pattern Memory</string>
    <string name="number_memory">Number Memory</string>
    
    <!-- Attention Games -->
    <string name="stroop_test">Stroop Test</string>
    <string name="reaction_time">Reaction Time</string>
    <string name="visual_search">Visual Search</string>
    <string name="focus_challenge">Focus Challenge</string>
    
    <!-- Math Games -->
    <string name="mental_arithmetic">Mental Arithmetic</string>
    <string name="number_sequences">Number Sequences</string>
    <string name="speed_math">Speed Math</string>
    <string name="estimation">Estimation</string>
    
    <!-- Logic Games -->
    <string name="pattern_completion">Pattern Completion</string>
    <string name="logic_puzzles">Logic Puzzles</string>
    <string name="shape_matching">Shape Matching</string>
    <string name="rule_discovery">Rule Discovery</string>
    <string name="tube_sort">Tube Sort</string>
    <string name="logical_reasoning">Logical Reasoning</string>
    <string name="spatial_rotation">Spatial Rotation</string>
    <string name="tower_of_hanoi">Tower of Hanoi</string>
    
    <!-- Language Games -->
    <string name="word_association">Word Association</string>
    <string name="anagrams">Anagrams</string>
    <string name="vocabulary">Vocabulary</string>
    <string name="word_search">Word Search</string>

    <!-- Game Results -->
    <string name="level_completed">Level %d Completed</string>
    <string name="accuracy_result">%d%%</string>
    <string name="time_result">%s</string>
    <string name="score_result">%d</string>
    <string name="rounds_result" formatted="false">%d/%d</string>
    <string name="next_level">Next Level (%d)</string>
    <string name="retry_level">Retry Level</string>
    <string name="level_locked">Level Locked</string>

    <!-- Game UI -->
    <string name="level_format">Level %d</string>
    <string name="round_format" formatted="false">Round %d/%d</string>
    
    <!-- Tests Page -->
    <string name="all_tests">All</string>
    <string name="cognitive_tests">Cognitive</string>
    <string name="personality_tests">Personality</string>
    
    <!-- Personality Tests -->
    <string name="learning_style">Learning Style</string>
    <string name="stress_response">Stress Response</string>
    <string name="problem_solving_style">Problem Solving Style</string>
    
    <!-- Progress Page -->
    <string name="total_score">Total Score</string>
    <string name="current_level">Level %d</string>
    <string name="max_streak">Max Streak: %d days</string>
    <string name="current_streak">Current: %d days</string>
    <string name="games_played_week">Games Played: %d this week</string>
    <string name="achievements">Achievements</string>
    <string name="category_progress">Category Progress</string>
    
    <!-- Common -->
    <string name="play">Play</string>
    <string name="start">Start</string>
    <string name="continue_game">Continue</string>
    <string name="locked">Locked</string>
    <string name="free">Free</string>
    <string name="premium_only">Premium</string>
    <string name="price_placeholder">$0.00</string>
</resources>
