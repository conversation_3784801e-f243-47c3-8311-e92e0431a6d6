package com.leapiq.braintraining.ui.games

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.leapiq.braintraining.MainActivity
import com.leapiq.braintraining.R
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.databinding.ActivityGameResultBinding

/**
 * Shows results after completing a game level
 * Displays accuracy, time, score and provides options to continue or return to games
 */
class GameResultActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityGameResultBinding
    private lateinit var progressManager: GameProgressManager
    
    companion object {
        const val EXTRA_GAME_ID = "game_id"
        const val EXTRA_LEVEL = "level"
        const val EXTRA_ACCURACY = "accuracy"
        const val EXTRA_TIME_MS = "time_ms"
        const val EXTRA_SCORE = "score"
        const val EXTRA_CORRECT_ROUNDS = "correct_rounds"
        const val EXTRA_TOTAL_ROUNDS = "total_rounds"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGameResultBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        progressManager = GameProgressManager.getInstance(this)
        
        setupUI()
        displayResults()
        setupButtons()
    }
    
    private fun setupUI() {
        // Hide system bars for immersive experience
        supportActionBar?.hide()
    }
    
    private fun displayResults() {
        val gameId = intent.getStringExtra(EXTRA_GAME_ID) ?: ""
        val level = intent.getIntExtra(EXTRA_LEVEL, 1)
        val accuracy = intent.getIntExtra(EXTRA_ACCURACY, 0)
        val timeMs = intent.getLongExtra(EXTRA_TIME_MS, 0)
        val score = intent.getIntExtra(EXTRA_SCORE, 0)
        val correctRounds = intent.getIntExtra(EXTRA_CORRECT_ROUNDS, 0)
        val totalRounds = intent.getIntExtra(EXTRA_TOTAL_ROUNDS, 5)
        
        binding.apply {
            // Game info
            gameTitle.text = getGameName(gameId)
            levelText.text = getString(R.string.level_completed, level)
            
            // Results
            accuracyText.text = getString(R.string.accuracy_result, accuracy)
            timeText.text = getString(R.string.time_result, formatTime(timeMs))
            scoreText.text = getString(R.string.score_result, score)
            roundsText.text = getString(R.string.rounds_result, correctRounds, totalRounds)
            
            // Progress indicator
            accuracyProgress.progress = accuracy
            
            // Performance message
            performanceMessage.text = getPerformanceMessage(accuracy)
            
            // Check if next level is unlocked
            val nextLevel = level + 1
            val isNextLevelUnlocked = progressManager.isLevelUnlocked(gameId, nextLevel)
            
            if (accuracy >= 60 && isNextLevelUnlocked) {
                nextLevelButton.text = getString(R.string.next_level, nextLevel)
                nextLevelButton.isEnabled = true
            } else if (accuracy < 60) {
                nextLevelButton.text = getString(R.string.retry_level)
                nextLevelButton.isEnabled = true
            } else {
                nextLevelButton.text = getString(R.string.level_locked)
                nextLevelButton.isEnabled = false
            }
        }
    }
    
    private fun setupButtons() {
        val gameId = intent.getStringExtra(EXTRA_GAME_ID) ?: ""
        val level = intent.getIntExtra(EXTRA_LEVEL, 1)
        val accuracy = intent.getIntExtra(EXTRA_ACCURACY, 0)
        
        binding.apply {
            // Back to games
            backToGamesButton.setOnClickListener {
                navigateToGames()
            }
            
            // Next level or retry
            nextLevelButton.setOnClickListener {
                val nextLevel = if (accuracy >= 60) level + 1 else level
                startGame(gameId, nextLevel)
            }
            
            // Replay current level
            replayButton.setOnClickListener {
                startGame(gameId, level)
            }
        }
    }
    
    private fun getGameName(gameId: String): String {
        return when (gameId) {
            "memory_1" -> getString(R.string.card_matching)
            "memory_2" -> getString(R.string.sequence_recall)
            "memory_3" -> getString(R.string.pattern_memory)
            "memory_4" -> getString(R.string.number_memory)
            "attention_1" -> getString(R.string.stroop_test)
            "attention_2" -> getString(R.string.reaction_time)
            "attention_3" -> getString(R.string.visual_search)
            "attention_4" -> getString(R.string.focus_challenge)
            "math_1" -> getString(R.string.mental_arithmetic)
            "math_2" -> getString(R.string.number_sequences)
            "math_3" -> getString(R.string.speed_math)
            "math_4" -> getString(R.string.estimation)
            "logic_1" -> getString(R.string.pattern_completion)
            "logic_2" -> getString(R.string.logic_puzzles)
            "logic_3" -> getString(R.string.shape_matching)
            "logic_4" -> getString(R.string.rule_discovery)
            "logic_5" -> getString(R.string.tube_sort)
            "language_1" -> getString(R.string.word_association)
            "language_2" -> getString(R.string.anagrams)
            "language_3" -> getString(R.string.vocabulary)
            "language_4" -> getString(R.string.word_search)
            else -> "Unknown Game"
        }
    }
    
    private fun getPerformanceMessage(accuracy: Int): String {
        return when {
            accuracy >= 90 -> "Excellent! Outstanding performance!"
            accuracy >= 80 -> "Great job! Very good accuracy!"
            accuracy >= 70 -> "Good work! Nice improvement!"
            accuracy >= 60 -> "Well done! You're making progress!"
            else -> "Keep practicing! You'll improve with time!"
        }
    }
    
    private fun formatTime(timeMs: Long): String {
        val seconds = timeMs / 1000
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        
        return if (minutes > 0) {
            String.format("%d:%02d", minutes, remainingSeconds)
        } else {
            String.format("%d sec", remainingSeconds)
        }
    }
    
    private fun navigateToGames() {
        val intent = Intent(this, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
            putExtra("navigate_to", "games")
        }
        startActivity(intent)
        finish()
    }
    
    private fun startGame(gameId: String, level: Int) {
        val intent = when (gameId) {
            "memory_1" -> Intent(this, CardMatchingActivity::class.java)
            // Add other games here as they are implemented
            else -> {
                navigateToGames()
                return
            }
        }
        
        intent.putExtra(BaseGameActivity.EXTRA_GAME_ID, gameId)
        intent.putExtra(BaseGameActivity.EXTRA_LEVEL, level)
        startActivity(intent)
        finish()
    }
}
