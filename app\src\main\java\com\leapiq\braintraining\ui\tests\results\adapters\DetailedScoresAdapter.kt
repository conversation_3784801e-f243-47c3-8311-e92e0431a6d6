package com.leapiq.braintraining.ui.tests.results.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.databinding.ItemDetailedScoreBinding

/**
 * Adapter for displaying detailed test scores
 */
class DetailedScoresAdapter : RecyclerView.Adapter<DetailedScoresAdapter.DetailedScoreViewHolder>() {
    
    private var scores = mapOf<String, Double>()
    
    fun updateScores(newScores: Map<String, Double>) {
        scores = newScores
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DetailedScoreViewHolder {
        val binding = ItemDetailedScoreBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return DetailedScoreViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: DetailedScoreViewHolder, position: Int) {
        val entry = scores.entries.toList()[position]
        holder.bind(entry.key, entry.value)
    }
    
    override fun getItemCount(): Int = scores.size
    
    inner class DetailedScoreViewHolder(
        private val binding: ItemDetailedScoreBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(category: String, score: Double) {
            binding.apply {
                categoryText.text = category
                scoreText.text = "${score.toInt()}%"
                scoreProgress.progress = score.toInt()
                
                // Set color based on score
                val color = when {
                    score >= 80 -> android.graphics.Color.GREEN
                    score >= 60 -> android.graphics.Color.parseColor("#FFA500") // Orange
                    else -> android.graphics.Color.RED
                }
                scoreProgress.progressTintList = android.content.res.ColorStateList.valueOf(color)
            }
        }
    }
}
