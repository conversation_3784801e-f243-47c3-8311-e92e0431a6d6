package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityWordSearchBinding
import com.leapiq.braintraining.ui.games.adapter.WordSearchGridAdapter
import com.leapiq.braintraining.ui.games.adapter.WordListAdapter
import com.leapiq.braintraining.ui.games.model.WordSearchGrid
import com.leapiq.braintraining.ui.games.model.WordSearchWord
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Word Search Game
 * Players find hidden words in letter grids by visual scanning
 * Tests visual attention, pattern recognition, and word recognition
 */
class WordSearchActivity : AppCompatActivity() {

    private lateinit var binding: ActivityWordSearchBinding
    private lateinit var gridAdapter: WordSearchGridAdapter
    private lateinit var wordListAdapter: WordListAdapter
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val roundAccuracies = mutableListOf<Double>()
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "language_4"

    // Word search specific
    private var grid = WordSearchGrid(8, 8)
    private var wordsToFind = mutableListOf<WordSearchWord>()
    private var foundWords = mutableSetOf<String>()
    private var puzzleStartTime = 0L
    private var currentPuzzle = 1
    private var puzzlesPerRound = 3

    // Word lists by difficulty
    private val wordLists = mapOf(
        // Level 1-3: 3-4 letter words
        1 to listOf("cat", "dog", "sun", "car", "run", "big", "red", "top", "cup", "hat"),
        2 to listOf("book", "tree", "fish", "bird", "hand", "foot", "door", "moon", "star", "fire"),
        3 to listOf("house", "water", "green", "happy", "music", "dance", "smile", "heart", "light", "peace"),
        
        // Level 4-6: 4-5 letter words
        4 to listOf("friend", "school", "family", "garden", "flower", "animal", "planet", "forest", "bridge", "castle"),
        5 to listOf("freedom", "wisdom", "beauty", "nature", "future", "dreams", "wonder", "spirit", "energy", "health"),
        6 to listOf("journey", "mystery", "harmony", "balance", "courage", "passion", "victory", "destiny", "miracle", "diamond"),
        
        // Level 7+: 5-6 letter words
        7 to listOf("science", "history", "culture", "society", "economy", "justice", "liberty", "dignity", "respect", "honesty"),
        8 to listOf("creative", "positive", "powerful", "peaceful", "grateful", "mindful", "hopeful", "careful", "helpful", "joyful")
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityWordSearchBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.word_search)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Find all the hidden words in the grid!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup hint button
            btnHint.setOnClickListener {
                showHint()
            }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentPuzzle = 1
        puzzlesPerRound = getPuzzlesPerRound(currentLevel)
        
        binding.puzzleText.text = "Puzzle $currentPuzzle/$puzzlesPerRound"
        
        setupAdapters()
        generatePuzzle()
    }

    private fun getPuzzlesPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 3       // 3 puzzles per round
            in 6..10 -> 4      // 4 puzzles per round
            in 11..15 -> 4     // 4 puzzles per round
            in 16..20 -> 5     // 5 puzzles per round
            else -> 5          // 5 puzzles per round
        }
    }

    private fun setupAdapters() {
        // Setup grid adapter
        gridAdapter = WordSearchGridAdapter(grid) { row, col ->
            onGridCellClicked(row, col)
        }

        val gridSize = getGridSize(currentLevel)
        binding.gridRecyclerView.apply {
            layoutManager = GridLayoutManager(this@WordSearchActivity, gridSize)
            adapter = gridAdapter
        }

        // Setup word list adapter
        wordListAdapter = WordListAdapter(wordsToFind)
        binding.wordListRecyclerView.apply {
            layoutManager = androidx.recyclerview.widget.LinearLayoutManager(this@WordSearchActivity)
            adapter = wordListAdapter
        }
    }

    private fun generatePuzzle() {
        puzzleStartTime = System.currentTimeMillis()
        foundWords.clear()
        
        val gridSize = getGridSize(currentLevel)
        val wordCount = getWordCount(currentLevel)
        
        grid = WordSearchGrid(gridSize, gridSize)
        wordsToFind.clear()
        
        // Get words for this level
        val levelGroup = getLevelGroup(currentLevel)
        val availableWords = wordLists[levelGroup] ?: wordLists[1]!!
        val selectedWords = availableWords.shuffled().take(wordCount)
        
        // Place words in grid
        selectedWords.forEach { word ->
            val placed = placeWordInGrid(word.uppercase())
            if (placed != null) {
                wordsToFind.add(placed)
            }
        }
        
        // Fill empty cells with random letters
        fillEmptyCells()
        
        // Update adapters
        gridAdapter.updateGrid(grid)
        wordListAdapter.updateWords(wordsToFind)
        
        updateUI()
    }

    private fun getGridSize(level: Int): Int {
        return when (level) {
            in 1..3 -> 8       // 8x8 grid
            in 4..6 -> 10      // 10x10 grid
            in 7..10 -> 12     // 12x12 grid
            in 11..15 -> 14    // 14x14 grid
            else -> 16         // 16x16 grid
        }
    }

    private fun getWordCount(level: Int): Int {
        return when (level) {
            in 1..3 -> 4       // 4 words
            in 4..6 -> 5       // 5 words
            in 7..10 -> 6      // 6 words
            in 11..15 -> 7     // 7 words
            else -> 8          // 8 words
        }
    }

    private fun getLevelGroup(level: Int): Int {
        return when (level) {
            in 1..3 -> Random.nextInt(1, 4)
            in 4..6 -> Random.nextInt(4, 7)
            else -> Random.nextInt(7, 9)
        }
    }

    private fun placeWordInGrid(word: String): WordSearchWord? {
        val directions = listOf(
            Pair(0, 1),   // Horizontal
            Pair(1, 0),   // Vertical
            Pair(1, 1),   // Diagonal down-right
            Pair(1, -1)   // Diagonal down-left
        )
        
        // Try to place word in random direction
        directions.shuffled().forEach { (dRow, dCol) ->
            for (attempt in 0 until 50) { // Max attempts
                val startRow = Random.nextInt(grid.size)
                val startCol = Random.nextInt(grid.size)
                
                if (canPlaceWord(word, startRow, startCol, dRow, dCol)) {
                    placeWord(word, startRow, startCol, dRow, dCol)
                    return WordSearchWord(
                        word = word,
                        startRow = startRow,
                        startCol = startCol,
                        endRow = startRow + (word.length - 1) * dRow,
                        endCol = startCol + (word.length - 1) * dCol,
                        isFound = false
                    )
                }
            }
        }
        
        return null
    }

    private fun canPlaceWord(word: String, startRow: Int, startCol: Int, dRow: Int, dCol: Int): Boolean {
        for (i in word.indices) {
            val row = startRow + i * dRow
            val col = startCol + i * dCol
            
            if (row < 0 || row >= grid.size || col < 0 || col >= grid.size) {
                return false
            }
            
            if (grid.getCell(row, col) != ' ' && grid.getCell(row, col) != word[i]) {
                return false
            }
        }
        
        return true
    }

    private fun placeWord(word: String, startRow: Int, startCol: Int, dRow: Int, dCol: Int) {
        for (i in word.indices) {
            val row = startRow + i * dRow
            val col = startCol + i * dCol
            grid.setCell(row, col, word[i])
        }
    }

    private fun fillEmptyCells() {
        val letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        
        for (row in 0 until grid.size) {
            for (col in 0 until grid.size) {
                if (grid.getCell(row, col) == ' ') {
                    grid.setCell(row, col, letters.random())
                }
            }
        }
    }

    private fun onGridCellClicked(row: Int, col: Int) {
        // For now, just highlight the cell
        // In a full implementation, you'd handle word selection by dragging
        checkForWordAt(row, col)
    }

    private fun checkForWordAt(row: Int, col: Int) {
        // Simple implementation: check if clicking on start of any unfound word
        wordsToFind.forEach { word ->
            if (!word.isFound && word.startRow == row && word.startCol == col) {
                markWordAsFound(word)
            }
        }
    }

    private fun markWordAsFound(word: WordSearchWord) {
        word.isFound = true
        foundWords.add(word.word)
        
        wordListAdapter.notifyDataSetChanged()
        
        if (foundWords.size == wordsToFind.size) {
            puzzleComplete()
        }
        
        updateUI()
    }

    private fun showHint() {
        val unfoundWords = wordsToFind.filter { !it.isFound }
        if (unfoundWords.isNotEmpty()) {
            val hintWord = unfoundWords.random()
            val hint = "Look for '${hintWord.word}' starting at row ${hintWord.startRow + 1}, column ${hintWord.startCol + 1}"
            
            AlertDialog.Builder(this)
                .setTitle("Hint")
                .setMessage(hint)
                .setPositiveButton("Got it!") { dialog, _ ->
                    dialog.dismiss()
                }
                .show()
        }
    }

    private fun puzzleComplete() {
        val puzzleTime = System.currentTimeMillis() - puzzleStartTime
        totalCorrect++
        totalAttempts++
        
        binding.instructionText.text = "Puzzle complete! All words found in ${puzzleTime / 1000}s"
        
        currentPuzzle++
        
        if (currentPuzzle > puzzlesPerRound) {
            // Round complete
            Handler(Looper.getMainLooper()).postDelayed({
                roundComplete()
            }, 2000)
        } else {
            // Next puzzle
            binding.puzzleText.text = "Puzzle $currentPuzzle/$puzzlesPerRound"
            
            Handler(Looper.getMainLooper()).postDelayed({
                generatePuzzle()
            }, 2000)
        }
    }

    private fun updateUI() {
        binding.apply {
            foundText.text = "Found: ${foundWords.size}/${wordsToFind.size}"
        }
    }

    private fun roundComplete() {
        val roundAccuracy = if (puzzlesPerRound > 0) {
            (totalCorrect.toDouble() / puzzlesPerRound)
        } else 1.0

        // Store round accuracy for detailed scoring
        roundAccuracies.add(roundAccuracy)

        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Puzzles solved: $totalCorrect/$puzzlesPerRound
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentPuzzle = 1
            totalCorrect = 0
            
            Handler(Looper.getMainLooper()).postDelayed({
                generatePuzzle()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7,
                timeSpentMs = totalTime / maxRounds,
                attempts = puzzlesPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Puzzles Solved: $totalCorrect
                Grid Size: ${getGridSize(currentLevel)}x${getGridSize(currentLevel)}
                Total Time: ${totalTime / 1000}s

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Total Puzzles: $totalCorrect
                Max Grid: ${getGridSize(currentLevel)}x${getGridSize(currentLevel)}

                Word Search Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Find all hidden words in the letter grid

                📋 RULES:
                • Words can be horizontal, vertical, or diagonal
                • Words can be forwards or backwards
                • Click on the starting letter of a word to select it
                • Found words will be marked in the word list

                💡 TIPS:
                • Scan systematically row by row, then column by column
                • Look for common letter patterns
                • Use the hint button if you're stuck
                • Words can overlap and share letters

                🏆 SCORING:
                • Complete all puzzles to advance levels
                • Larger grids and more words in higher levels
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
