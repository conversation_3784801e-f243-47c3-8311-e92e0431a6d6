package com.leapiq.braintraining.ui.welcome

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.FragmentWelcomePageBinding

class WelcomePageFragment : Fragment() {
    
    private var _binding: FragmentWelcomePageBinding? = null
    private val binding get() = _binding!!
    
    private var pagePosition: Int = 0
    
    companion object {
        private const val ARG_POSITION = "position"
        
        fun newInstance(position: Int): WelcomePageFragment {
            val fragment = WelcomePageFragment()
            val args = Bundle()
            args.putInt(ARG_POSITION, position)
            fragment.arguments = args
            return fragment
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        pagePosition = arguments?.getInt(ARG_POSITION) ?: 0
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentWelcomePageBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        setupContent()
    }
    
    private fun setupContent() {
        when (pagePosition) {
            0 -> {
                // Welcome page
                binding.welcomeIcon.setImageResource(R.drawable.ic_home)
                binding.welcomeTitle.text = "Welcome to LeapIQ"
                binding.welcomeDescription.text = """
                    Train your brain with scientifically-designed games and assessments.
                    
                    Improve your memory, attention, problem-solving, and cognitive skills through engaging daily challenges.
                """.trimIndent()
            }
            1 -> {
                // Today page
                binding.welcomeIcon.setImageResource(R.drawable.ic_today)
                binding.welcomeTitle.text = "Daily Challenges"
                binding.welcomeDescription.text = """
                    Start each day with personalized brain training challenges.
                    
                    • Fresh daily puzzles and games
                    • Track your daily progress
                    • Build consistent training habits
                """.trimIndent()
            }
            2 -> {
                // Games & Tests page
                binding.welcomeIcon.setImageResource(R.drawable.ic_games)
                binding.welcomeTitle.text = "Games & Tests"
                binding.welcomeDescription.text = """
                    Explore a variety of brain training games and cognitive assessments.
                    
                    • Memory, attention, and logic games
                    • Cognitive performance tests
                    • Adaptive difficulty levels
                """.trimIndent()
            }
            3 -> {
                // Progress page
                binding.welcomeIcon.setImageResource(R.drawable.ic_progress)
                binding.welcomeTitle.text = "Track Your Progress"
                binding.welcomeDescription.text = """
                    Monitor your cognitive improvement over time.
                    
                    • Detailed performance analytics
                    • Personal achievement tracking
                    • Customize your training experience
                """.trimIndent()
            }
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
