package com.leapiq.braintraining.data.model

data class Achievement(
    val id: String,
    val name: String,
    val description: String,
    val iconResource: String,
    val isUnlocked: Boolean = false,
    val type: AchievementType
)

enum class AchievementType {
    LEVEL,      // Level-based achievements
    STREAK,     // Streak-based achievements  
    SCORE,      // Score-based achievements
    ACCURACY    // Accuracy-based achievements
}

data class CategoryAccuracy(
    val categoryName: String,
    val accuracy: Int // percentage
)
