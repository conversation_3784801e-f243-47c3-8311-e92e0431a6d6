package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityFocusChallengeBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Focus Challenge Game (Sustained Attention/Vigilance Task)
 * Players monitor a stream of stimuli and respond only to rare targets
 * Tests sustained attention and concentration over time
 */
class FocusChallengeActivity : AppCompatActivity() {

    private lateinit var binding: ActivityFocusChallengeBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val roundMetrics = mutableListOf<FocusMetrics>()
    private val gameId = "attention_4"

    // Focus challenge specific
    private var isTaskActive = false
    private var currentStimulus = ""
    private var targetStimulus = ""
    private var stimulusStartTime = 0L
    private var stimulusCount = 0
    private var targetCount = 0
    private var missedTargets = 0
    private var falseAlarms = 0
    private var stimuliPerRound = 60
    private var targetFrequency = 0.15f // 15% targets

    // Stimulus types
    private val stimuli = listOf("A", "B", "C", "D", "E", "F", "G", "H", "X", "Y", "Z")
    private val colors = listOf("blue", "green", "red", "purple")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityFocusChallengeBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.focus_challenge)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup response area
            responseArea.setOnClickListener {
                onResponseAreaClicked()
            }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        stimulusCount = 0
        targetCount = 0
        missedTargets = 0
        falseAlarms = 0
        
        stimuliPerRound = getStimuliPerRound(currentLevel)
        targetFrequency = getTargetFrequency(currentLevel)
        targetStimulus = getTargetStimulus(currentLevel)
        
        binding.apply {
            instructionText.text = "Watch for '$targetStimulus' and tap when you see it!"
            targetDisplay.text = "Target: $targetStimulus"
            progressText.text = "Stimulus: 0/$stimuliPerRound"
            stimulusDisplay.text = ""
            stimulusDisplay.visibility = android.view.View.INVISIBLE
        }
        
        isTaskActive = true
        scheduleNextStimulus()
    }

    private fun getStimuliPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 60      // 60 stimuli per round
            in 6..10 -> 80     // 80 stimuli per round
            in 11..15 -> 100   // 100 stimuli per round
            in 16..20 -> 120   // 120 stimuli per round
            else -> 150        // 150 stimuli per round
        }
    }

    private fun getTargetFrequency(level: Int): Float {
        return when (level) {
            in 1..5 -> 0.20f   // 20% targets (easier)
            in 6..10 -> 0.15f  // 15% targets
            in 11..15 -> 0.12f // 12% targets
            in 16..20 -> 0.10f // 10% targets (harder)
            else -> 0.08f      // 8% targets (very hard)
        }
    }

    private fun getTargetStimulus(level: Int): String {
        return when (level) {
            in 1..5 -> "X"     // Simple target
            in 6..10 -> "Z"    // Different target
            in 11..15 -> "Q"   // Less common letter
            else -> "3"        // Number among letters
        }
    }

    private fun scheduleNextStimulus() {
        if (!isTaskActive || stimulusCount >= stimuliPerRound) {
            roundComplete()
            return
        }

        val interval = getStimulusInterval(currentLevel)
        Handler(Looper.getMainLooper()).postDelayed({
            showNextStimulus()
        }, interval)
    }

    private fun getStimulusInterval(level: Int): Long {
        return when (level) {
            in 1..5 -> Random.nextLong(1500, 2500)   // 1.5-2.5 seconds
            in 6..10 -> Random.nextLong(1200, 2000)  // 1.2-2.0 seconds
            in 11..15 -> Random.nextLong(1000, 1800) // 1.0-1.8 seconds
            in 16..20 -> Random.nextLong(800, 1500)  // 0.8-1.5 seconds
            else -> Random.nextLong(600, 1200)       // 0.6-1.2 seconds
        }
    }

    private fun showNextStimulus() {
        if (!isTaskActive) return

        stimulusCount++
        binding.progressText.text = "Stimulus: $stimulusCount/$stimuliPerRound"

        // Determine if this should be a target
        val isTarget = Random.nextFloat() < targetFrequency
        
        if (isTarget) {
            currentStimulus = targetStimulus
            targetCount++
        } else {
            // Select random non-target stimulus
            val nonTargets = stimuli.filter { it != targetStimulus }
            currentStimulus = nonTargets.random()
        }

        // Display stimulus
        binding.stimulusDisplay.text = currentStimulus
        binding.stimulusDisplay.visibility = android.view.View.VISIBLE
        
        // Set random color for visual variety
        val colorRes = when (colors.random()) {
            "blue" -> R.color.stroop_blue
            "green" -> R.color.stroop_green
            "red" -> R.color.stroop_red
            "purple" -> R.color.search_purple
            else -> R.color.text_primary
        }
        val color = ContextCompat.getColor(this, colorRes)
        binding.stimulusDisplay.setTextColor(color)

        stimulusStartTime = System.currentTimeMillis()
        totalAttempts++

        // Hide stimulus after display duration
        val displayDuration = getStimulusDisplayDuration(currentLevel)
        Handler(Looper.getMainLooper()).postDelayed({
            hideStimulus(isTarget)
        }, displayDuration)
    }

    private fun getStimulusDisplayDuration(level: Int): Long {
        return when (level) {
            in 1..5 -> 800L    // 0.8 seconds
            in 6..10 -> 600L   // 0.6 seconds
            in 11..15 -> 500L  // 0.5 seconds
            in 16..20 -> 400L  // 0.4 seconds
            else -> 300L       // 0.3 seconds
        }
    }

    private fun hideStimulus(wasTarget: Boolean) {
        binding.stimulusDisplay.visibility = android.view.View.INVISIBLE
        
        if (wasTarget) {
            // This was a target that wasn't responded to - miss
            missedTargets++
            showFeedback("Miss!", false)
        }
        
        // Schedule next stimulus
        scheduleNextStimulus()
    }

    private fun onResponseAreaClicked() {
        if (!isTaskActive || binding.stimulusDisplay.visibility != android.view.View.VISIBLE) {
            return
        }

        val reactionTime = System.currentTimeMillis() - stimulusStartTime
        val wasTarget = currentStimulus == targetStimulus

        if (wasTarget) {
            // Correct hit
            totalCorrect++
            showFeedback("Hit! (${reactionTime}ms)", true)
        } else {
            // False alarm
            falseAlarms++
            showFeedback("False alarm!", false)
        }

        // Hide stimulus immediately after response
        binding.stimulusDisplay.visibility = android.view.View.INVISIBLE
        
        // Schedule next stimulus
        scheduleNextStimulus()
    }

    private fun showFeedback(message: String, isCorrect: Boolean) {
        binding.feedbackText.text = message
        binding.feedbackText.setTextColor(
            ContextCompat.getColor(this, if (isCorrect) R.color.stroop_green else R.color.stroop_red)
        )
        
        // Clear feedback after delay
        Handler(Looper.getMainLooper()).postDelayed({
            binding.feedbackText.text = ""
        }, 1000)
    }

    private fun roundComplete() {
        isTaskActive = false
        
        val hits = totalCorrect
        val misses = missedTargets
        val falseAlarmCount = falseAlarms
        val correctRejections = stimulusCount - targetCount - falseAlarmCount
        
        val accuracy = if (targetCount > 0) hits.toDouble() / targetCount else 1.0
        val falseAlarmRate = if (stimulusCount - targetCount > 0) {
            falseAlarmCount.toDouble() / (stimulusCount - targetCount)
        } else 0.0

        // Store round metrics for detailed analysis
        roundMetrics.add(FocusMetrics(
            hits = hits,
            misses = misses,
            correctRejections = correctRejections,
            falseAlarms = falseAlarmCount,
            accuracy = accuracy,
            falseAlarmRate = falseAlarmRate
        ))

        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                
                Hits: $hits/$targetCount (${(accuracy * 100).toInt()}%)
                False Alarms: $falseAlarmCount
                
                Round $currentRound starting...
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            stimulusCount = 0
            targetCount = 0
            missedTargets = 0
            falseAlarms = 0
            
            Handler(Looper.getMainLooper()).postDelayed({
                setupGame()
            }, 3000)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val overallAccuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = overallAccuracy > 0.7, // 70% threshold
                timeSpentMs = totalTime / maxRounds,
                attempts = stimuliPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = overallAccuracy,
            score = (overallAccuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Overall Accuracy: ${(overallAccuracy * 100).toInt()}%
                Total Stimuli: $totalAttempts
                Concentration Time: ${totalTime / 1000}s

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 4000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(overallAccuracy * 100).toInt()}%
                Total Focus Time: ${totalTime / 1000}s

                Focus Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        isTaskActive = false // Pause the task
        
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
            isTaskActive = true // Resume the task
            scheduleNextStimulus()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        isTaskActive = false
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Maintain focus and respond only to targets

                📋 RULES:
                • Watch the stream of letters/numbers
                • Tap ONLY when you see the target stimulus
                • Ignore all other stimuli (don't tap)
                • Stay focused for the entire round

                💡 TIPS:
                • Concentrate throughout the task
                • Avoid false alarms (tapping non-targets)
                • Don't miss targets when they appear
                • Higher levels are faster and longer

                🏆 SCORING:
                • Hits = correctly identifying targets
                • Misses = missing targets
                • False alarms = responding to non-targets
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
                isTaskActive = true
                scheduleNextStimulus()
            }
            .show()
    }
}

/**
 * Data class to store detailed focus/attention metrics
 */
data class FocusMetrics(
    val hits: Int,
    val misses: Int,
    val correctRejections: Int,
    val falseAlarms: Int,
    val accuracy: Double,
    val falseAlarmRate: Double
)
