<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.leapiq.braintraining"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="26"
        android:targetSdkVersion="34" />

    <permission
        android:name="com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.LeapIQ" >
        <activity
            android:name="com.leapiq.braintraining.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.LeapIQ" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Game Activities -->
        <activity
            android:name="com.leapiq.braintraining.ui.games.CardMatchingActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.SpeedMathActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.StroopTestActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.VocabularyActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.WordSearchActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />

        <!-- Memory Games -->
        <activity
            android:name="com.leapiq.braintraining.ui.games.SequenceRecallActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.PatternMemoryActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.NumberMemoryActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />

        <!-- Attention Games -->
        <activity
            android:name="com.leapiq.braintraining.ui.games.ReactionTimeActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.VisualSearchActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.FocusChallengeActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />

        <!-- Math Games -->
        <activity
            android:name="com.leapiq.braintraining.ui.games.MentalArithmeticActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.NumberSequencesActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.EstimationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />

        <!-- Logic Games -->
        <activity
            android:name="com.leapiq.braintraining.ui.games.TubeSortActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.LogicalReasoningActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.SpatialRotationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.TowerOfHanoiActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.PatternCompletionActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />

        <!-- Language Games -->
        <activity
            android:name="com.leapiq.braintraining.ui.games.WordAssociationActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.AnagramsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.LogicPuzzlesActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.games.GameResultActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />

        <!-- Test Info Activities -->
        <activity
            android:name="com.leapiq.braintraining.ui.tests.LearningStyleInfoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.tests.StressResponseInfoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.tests.ProblemSolvingStyleInfoActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />

        <!-- Test Activities -->
        <activity
            android:name="com.leapiq.braintraining.ui.tests.LearningStyleActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.tests.StressResponseActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.tests.results.TestResultsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.tests.results.AllResultsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />
        <activity
            android:name="com.leapiq.braintraining.ui.tests.results.MyResultsActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.LeapIQ" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.leapiq.braintraining.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <uses-library
            android:name="androidx.window.extensions"
            android:required="false" />
        <uses-library
            android:name="androidx.window.sidecar"
            android:required="false" />

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>