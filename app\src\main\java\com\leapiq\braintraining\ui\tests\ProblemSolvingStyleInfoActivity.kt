package com.leapiq.braintraining.ui.tests

import com.leapiq.braintraining.R

/**
 * Information page for Problem Solving Style Test
 * Shows test description, instructions, and start button
 */
class ProblemSolvingStyleInfoActivity : BaseTestInfoActivity() {
    
    override fun loadTestInfo() {
        setTestInfo(
            title = "Problem Solving Style",
            subtitle = "Personality Test",
            description = "Discover your approach to problem-solving and decision-making. Learn whether you tend to be more analytical and systematic or intuitive and creative.",
            keyPoints = listOf(
                "Face various scenarios and logic puzzles",
                "Choose your natural approach, not the 'correct' one",
                "Navigate freely between questions",
                "Take your time for thoughtful responses"
            ),
            estimatedMinutes = 12,
            iconResource = R.drawable.ic_problem_solving
        )
    }
    
    override fun getTestActivityClass(): Class<*> {
        return ProblemSolvingStyleActivity::class.java
    }
    
    override fun getHeroGradient(): Int {
        return R.drawable.gradient_problem_solving
    }
}
