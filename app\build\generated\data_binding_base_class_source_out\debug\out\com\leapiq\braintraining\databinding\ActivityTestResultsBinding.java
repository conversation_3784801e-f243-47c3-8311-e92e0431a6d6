// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTestResultsBinding implements ViewBinding {
  @NonNull
  private final ScrollView rootView;

  @NonNull
  public final LinearLayout accuracyContainer;

  @NonNull
  public final TextView accuracyValue;

  @NonNull
  public final TextView avgResponseTime;

  @NonNull
  public final MaterialButton btnBackToTests;

  @NonNull
  public final MaterialButton btnRetakeTest;

  @NonNull
  public final MaterialButton btnViewProgress;

  @NonNull
  public final TextView completionDate;

  @NonNull
  public final MaterialCardView detailedScoresContainer;

  @NonNull
  public final RecyclerView detailedScoresRecycler;

  @NonNull
  public final MaterialCardView insightsContainer;

  @NonNull
  public final TextView insightsText;

  @NonNull
  public final TextView performanceLevelText;

  @NonNull
  public final TextView questionsAnswered;

  @NonNull
  public final MaterialCardView recommendationsContainer;

  @NonNull
  public final TextView recommendationsText;

  @NonNull
  public final TextView scoreLabel;

  @NonNull
  public final TextView scoreValue;

  @NonNull
  public final TextView testName;

  @NonNull
  public final TextView testType;

  @NonNull
  public final TextView timeTaken;

  @NonNull
  public final Toolbar toolbar;

  private ActivityTestResultsBinding(@NonNull ScrollView rootView,
      @NonNull LinearLayout accuracyContainer, @NonNull TextView accuracyValue,
      @NonNull TextView avgResponseTime, @NonNull MaterialButton btnBackToTests,
      @NonNull MaterialButton btnRetakeTest, @NonNull MaterialButton btnViewProgress,
      @NonNull TextView completionDate, @NonNull MaterialCardView detailedScoresContainer,
      @NonNull RecyclerView detailedScoresRecycler, @NonNull MaterialCardView insightsContainer,
      @NonNull TextView insightsText, @NonNull TextView performanceLevelText,
      @NonNull TextView questionsAnswered, @NonNull MaterialCardView recommendationsContainer,
      @NonNull TextView recommendationsText, @NonNull TextView scoreLabel,
      @NonNull TextView scoreValue, @NonNull TextView testName, @NonNull TextView testType,
      @NonNull TextView timeTaken, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.accuracyContainer = accuracyContainer;
    this.accuracyValue = accuracyValue;
    this.avgResponseTime = avgResponseTime;
    this.btnBackToTests = btnBackToTests;
    this.btnRetakeTest = btnRetakeTest;
    this.btnViewProgress = btnViewProgress;
    this.completionDate = completionDate;
    this.detailedScoresContainer = detailedScoresContainer;
    this.detailedScoresRecycler = detailedScoresRecycler;
    this.insightsContainer = insightsContainer;
    this.insightsText = insightsText;
    this.performanceLevelText = performanceLevelText;
    this.questionsAnswered = questionsAnswered;
    this.recommendationsContainer = recommendationsContainer;
    this.recommendationsText = recommendationsText;
    this.scoreLabel = scoreLabel;
    this.scoreValue = scoreValue;
    this.testName = testName;
    this.testType = testType;
    this.timeTaken = timeTaken;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ScrollView getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTestResultsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTestResultsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_test_results, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTestResultsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.accuracy_container;
      LinearLayout accuracyContainer = ViewBindings.findChildViewById(rootView, id);
      if (accuracyContainer == null) {
        break missingId;
      }

      id = R.id.accuracy_value;
      TextView accuracyValue = ViewBindings.findChildViewById(rootView, id);
      if (accuracyValue == null) {
        break missingId;
      }

      id = R.id.avg_response_time;
      TextView avgResponseTime = ViewBindings.findChildViewById(rootView, id);
      if (avgResponseTime == null) {
        break missingId;
      }

      id = R.id.btn_back_to_tests;
      MaterialButton btnBackToTests = ViewBindings.findChildViewById(rootView, id);
      if (btnBackToTests == null) {
        break missingId;
      }

      id = R.id.btn_retake_test;
      MaterialButton btnRetakeTest = ViewBindings.findChildViewById(rootView, id);
      if (btnRetakeTest == null) {
        break missingId;
      }

      id = R.id.btn_view_progress;
      MaterialButton btnViewProgress = ViewBindings.findChildViewById(rootView, id);
      if (btnViewProgress == null) {
        break missingId;
      }

      id = R.id.completion_date;
      TextView completionDate = ViewBindings.findChildViewById(rootView, id);
      if (completionDate == null) {
        break missingId;
      }

      id = R.id.detailed_scores_container;
      MaterialCardView detailedScoresContainer = ViewBindings.findChildViewById(rootView, id);
      if (detailedScoresContainer == null) {
        break missingId;
      }

      id = R.id.detailed_scores_recycler;
      RecyclerView detailedScoresRecycler = ViewBindings.findChildViewById(rootView, id);
      if (detailedScoresRecycler == null) {
        break missingId;
      }

      id = R.id.insights_container;
      MaterialCardView insightsContainer = ViewBindings.findChildViewById(rootView, id);
      if (insightsContainer == null) {
        break missingId;
      }

      id = R.id.insights_text;
      TextView insightsText = ViewBindings.findChildViewById(rootView, id);
      if (insightsText == null) {
        break missingId;
      }

      id = R.id.performance_level_text;
      TextView performanceLevelText = ViewBindings.findChildViewById(rootView, id);
      if (performanceLevelText == null) {
        break missingId;
      }

      id = R.id.questions_answered;
      TextView questionsAnswered = ViewBindings.findChildViewById(rootView, id);
      if (questionsAnswered == null) {
        break missingId;
      }

      id = R.id.recommendations_container;
      MaterialCardView recommendationsContainer = ViewBindings.findChildViewById(rootView, id);
      if (recommendationsContainer == null) {
        break missingId;
      }

      id = R.id.recommendations_text;
      TextView recommendationsText = ViewBindings.findChildViewById(rootView, id);
      if (recommendationsText == null) {
        break missingId;
      }

      id = R.id.score_label;
      TextView scoreLabel = ViewBindings.findChildViewById(rootView, id);
      if (scoreLabel == null) {
        break missingId;
      }

      id = R.id.score_value;
      TextView scoreValue = ViewBindings.findChildViewById(rootView, id);
      if (scoreValue == null) {
        break missingId;
      }

      id = R.id.test_name;
      TextView testName = ViewBindings.findChildViewById(rootView, id);
      if (testName == null) {
        break missingId;
      }

      id = R.id.test_type;
      TextView testType = ViewBindings.findChildViewById(rootView, id);
      if (testType == null) {
        break missingId;
      }

      id = R.id.time_taken;
      TextView timeTaken = ViewBindings.findChildViewById(rootView, id);
      if (timeTaken == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityTestResultsBinding((ScrollView) rootView, accuracyContainer, accuracyValue,
          avgResponseTime, btnBackToTests, btnRetakeTest, btnViewProgress, completionDate,
          detailedScoresContainer, detailedScoresRecycler, insightsContainer, insightsText,
          performanceLevelText, questionsAnswered, recommendationsContainer, recommendationsText,
          scoreLabel, scoreValue, testName, testType, timeTaken, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
