package com.leapiq.braintraining.ui.tests.adapters

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ItemMemoryGridBinding

/**
 * Adapter for memory grid used in spatial memory tasks
 */
class MemoryGridAdapter(
    private val onItemClick: (Int) -> Unit
) : RecyclerView.Adapter<MemoryGridAdapter.GridViewHolder>() {
    
    private var gridSize = 16 // Default 4x4 grid
    private var highlightedPositions = mutableSetOf<Int>()
    private var selectedPositions = mutableSetOf<Int>()
    private var patternPositions = mutableSetOf<Int>()
    private var showingPattern = false
    
    fun setupGrid(size: Int) {
        gridSize = size
        clearAll()
        notifyDataSetChanged()
    }
    
    fun highlightPosition(position: Int) {
        highlightedPositions.clear()
        highlightedPositions.add(position)
        notifyItemChanged(position)
    }
    
    fun clearHighlight() {
        val previousHighlighted = highlightedPositions.toList()
        highlightedPositions.clear()
        previousHighlighted.forEach { notifyItemChanged(it) }
    }
    
    fun selectPosition(position: Int) {
        if (selectedPositions.contains(position)) {
            selectedPositions.remove(position)
        } else {
            selectedPositions.add(position)
        }
        notifyItemChanged(position)
    }
    
    fun clearSelections() {
        val previousSelected = selectedPositions.toList()
        selectedPositions.clear()
        previousSelected.forEach { notifyItemChanged(it) }
    }
    
    fun showPattern(positions: List<Int>) {
        patternPositions.clear()
        patternPositions.addAll(positions)
        showingPattern = true
        positions.forEach { notifyItemChanged(it) }
    }
    
    fun hidePattern() {
        val previousPattern = patternPositions.toList()
        showingPattern = false
        patternPositions.clear()
        previousPattern.forEach { notifyItemChanged(it) }
    }
    
    fun clearAll() {
        highlightedPositions.clear()
        selectedPositions.clear()
        patternPositions.clear()
        showingPattern = false
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GridViewHolder {
        val binding = ItemMemoryGridBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return GridViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: GridViewHolder, position: Int) {
        holder.bind(position)
    }
    
    override fun getItemCount(): Int = gridSize
    
    inner class GridViewHolder(
        private val binding: ItemMemoryGridBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(position: Int) {
            binding.apply {
                // Set click listener
                root.setOnClickListener {
                    onItemClick(position)
                }
                
                // Set appearance based on state
                when {
                    highlightedPositions.contains(position) -> {
                        // Currently highlighted (during sequence display)
                        gridItem.setBackgroundResource(R.drawable.memory_grid_highlighted)
                        gridItem.alpha = 1.0f
                    }
                    selectedPositions.contains(position) -> {
                        // User selected
                        gridItem.setBackgroundResource(R.drawable.memory_grid_selected)
                        gridItem.alpha = 1.0f
                    }
                    showingPattern && patternPositions.contains(position) -> {
                        // Part of pattern being shown
                        gridItem.setBackgroundResource(R.drawable.memory_grid_pattern)
                        gridItem.alpha = 1.0f
                    }
                    else -> {
                        // Default state
                        gridItem.setBackgroundResource(R.drawable.memory_grid_default)
                        gridItem.alpha = 0.7f
                    }
                }
                
                // Add position number for debugging (can be removed)
                if (gridSize <= 25) {
                    gridText.text = (position + 1).toString()
                    gridText.visibility = View.GONE // Hide numbers in production
                } else {
                    gridText.visibility = View.GONE
                }
            }
        }
    }
}
