<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_logic_puzzles" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_logic_puzzles.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_logic_puzzles_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="243" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="64" startOffset="8" endLine="73" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="76" startOffset="8" endLine="85" endOffset="38"/></Target><Target id="@+id/puzzle_text" view="TextView"><Expressions/><location startLine="88" startOffset="8" endLine="97" endOffset="38"/></Target><Target id="@+id/hints_text" view="TextView"><Expressions/><location startLine="100" startOffset="8" endLine="108" endOffset="37"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="113" startOffset="4" endLine="122" endOffset="33"/></Target><Target id="@+id/grid_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="153" startOffset="12" endLine="159" endOffset="62"/></Target><Target id="@+id/clues_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="184" startOffset="12" endLine="190" endOffset="58"/></Target><Target id="@+id/btn_hint" view="Button"><Expressions/><location startLine="204" startOffset="8" endLine="214" endOffset="36"/></Target><Target id="@+id/btn_check_solution" view="Button"><Expressions/><location startLine="216" startOffset="8" endLine="227" endOffset="36"/></Target><Target id="@+id/btn_reset" view="Button"><Expressions/><location startLine="229" startOffset="8" endLine="239" endOffset="36"/></Target></Targets></Layout>