// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAnagramsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnClear;

  @NonNull
  public final Button btnHint;

  @NonNull
  public final ImageButton btnMenu;

  @NonNull
  public final ImageButton btnQuit;

  @NonNull
  public final Button btnSubmit;

  @NonNull
  public final TextView gameTitle;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final LinearLayout lettersContainer;

  @NonNull
  public final TextView levelText;

  @NonNull
  public final TextView roundText;

  @NonNull
  public final TextView scrambledWordDisplay;

  @NonNull
  public final TextView trialText;

  @NonNull
  public final TextView userInputDisplay;

  private ActivityAnagramsBinding(@NonNull LinearLayout rootView, @NonNull Button btnClear,
      @NonNull Button btnHint, @NonNull ImageButton btnMenu, @NonNull ImageButton btnQuit,
      @NonNull Button btnSubmit, @NonNull TextView gameTitle, @NonNull TextView instructionText,
      @NonNull LinearLayout lettersContainer, @NonNull TextView levelText,
      @NonNull TextView roundText, @NonNull TextView scrambledWordDisplay,
      @NonNull TextView trialText, @NonNull TextView userInputDisplay) {
    this.rootView = rootView;
    this.btnClear = btnClear;
    this.btnHint = btnHint;
    this.btnMenu = btnMenu;
    this.btnQuit = btnQuit;
    this.btnSubmit = btnSubmit;
    this.gameTitle = gameTitle;
    this.instructionText = instructionText;
    this.lettersContainer = lettersContainer;
    this.levelText = levelText;
    this.roundText = roundText;
    this.scrambledWordDisplay = scrambledWordDisplay;
    this.trialText = trialText;
    this.userInputDisplay = userInputDisplay;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAnagramsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAnagramsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_anagrams, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAnagramsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_clear;
      Button btnClear = ViewBindings.findChildViewById(rootView, id);
      if (btnClear == null) {
        break missingId;
      }

      id = R.id.btn_hint;
      Button btnHint = ViewBindings.findChildViewById(rootView, id);
      if (btnHint == null) {
        break missingId;
      }

      id = R.id.btn_menu;
      ImageButton btnMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnMenu == null) {
        break missingId;
      }

      id = R.id.btn_quit;
      ImageButton btnQuit = ViewBindings.findChildViewById(rootView, id);
      if (btnQuit == null) {
        break missingId;
      }

      id = R.id.btn_submit;
      Button btnSubmit = ViewBindings.findChildViewById(rootView, id);
      if (btnSubmit == null) {
        break missingId;
      }

      id = R.id.game_title;
      TextView gameTitle = ViewBindings.findChildViewById(rootView, id);
      if (gameTitle == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.letters_container;
      LinearLayout lettersContainer = ViewBindings.findChildViewById(rootView, id);
      if (lettersContainer == null) {
        break missingId;
      }

      id = R.id.level_text;
      TextView levelText = ViewBindings.findChildViewById(rootView, id);
      if (levelText == null) {
        break missingId;
      }

      id = R.id.round_text;
      TextView roundText = ViewBindings.findChildViewById(rootView, id);
      if (roundText == null) {
        break missingId;
      }

      id = R.id.scrambled_word_display;
      TextView scrambledWordDisplay = ViewBindings.findChildViewById(rootView, id);
      if (scrambledWordDisplay == null) {
        break missingId;
      }

      id = R.id.trial_text;
      TextView trialText = ViewBindings.findChildViewById(rootView, id);
      if (trialText == null) {
        break missingId;
      }

      id = R.id.user_input_display;
      TextView userInputDisplay = ViewBindings.findChildViewById(rootView, id);
      if (userInputDisplay == null) {
        break missingId;
      }

      return new ActivityAnagramsBinding((LinearLayout) rootView, btnClear, btnHint, btnMenu,
          btnQuit, btnSubmit, gameTitle, instructionText, lettersContainer, levelText, roundText,
          scrambledWordDisplay, trialText, userInputDisplay);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
