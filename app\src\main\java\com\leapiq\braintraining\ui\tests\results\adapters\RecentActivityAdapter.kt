package com.leapiq.braintraining.ui.tests.results.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ItemRecentActivityBinding
import com.leapiq.braintraining.data.ActivityItem
import com.leapiq.braintraining.data.ActivityType
import java.text.SimpleDateFormat
import java.util.*

/**
 * Adapter for displaying recent activity items
 */
class RecentActivityAdapter(
    private val onItemClick: (String) -> Unit
) : ListAdapter<ActivityItem, RecentActivityAdapter.RecentActivityViewHolder>(DiffCallback()) {
    
    private val dateFormat = SimpleDateFormat("MMM dd", Locale.getDefault())
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecentActivityViewHolder {
        val binding = ItemRecentActivityBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return RecentActivityViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: RecentActivityViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class RecentActivityViewHolder(
        private val binding: ItemRecentActivityBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(item: ActivityItem) {
            binding.apply {
                testNameText.text = item.testName
                scoreText.text = "${item.score}%"
                dateText.text = dateFormat.format(item.completedAt)
                
                // Set activity type icon and color
                val (icon, color) = when (item.activityType) {
                    ActivityType.COMPLETION -> "✅" to R.color.primary_light_blue
                    ActivityType.IMPROVEMENT -> "📈" to R.color.success_green
                    ActivityType.MILESTONE -> "🏆" to R.color.warning_orange
                }
                
                activityIcon.text = icon
                activityIcon.setTextColor(ContextCompat.getColor(root.context, color))
                
                // Set score color based on performance
                val scoreColor = when {
                    item.score >= 80 -> R.color.success_green
                    item.score >= 60 -> R.color.warning_orange
                    else -> R.color.error_red
                }
                
                scoreText.setTextColor(ContextCompat.getColor(root.context, scoreColor))
                
                // Set click listener
                root.setOnClickListener {
                    onItemClick(item.testId)
                }
            }
        }
    }
    
    private class DiffCallback : DiffUtil.ItemCallback<ActivityItem>() {
        override fun areItemsTheSame(oldItem: ActivityItem, newItem: ActivityItem): Boolean {
            return oldItem.testId == newItem.testId && oldItem.completedAt == newItem.completedAt
        }
        
        override fun areContentsTheSame(oldItem: ActivityItem, newItem: ActivityItem): Boolean {
            return oldItem == newItem
        }
    }
}
