package com.leapiq.braintraining.ui.tests.results

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.leapiq.braintraining.databinding.ActivityTestProgressBinding
import com.leapiq.braintraining.data.TestProgressManager
import com.leapiq.braintraining.data.TestResultsRepository
import com.leapiq.braintraining.analysis.TestAnalysisEngine
import com.leapiq.braintraining.ui.tests.results.adapters.TestResultsAdapter
import com.google.gson.Gson

/**
 * Activity to display detailed progress for a specific test
 */
class TestProgressActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityTestProgressBinding
    private lateinit var progressManager: TestProgressManager
    private lateinit var repository: TestResultsRepository
    private lateinit var analysisEngine: TestAnalysisEngine
    private lateinit var resultsAdapter: TestResultsAdapter
    
    private lateinit var testId: String
    
    companion object {
        const val EXTRA_TEST_ID = "extra_test_id"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTestProgressBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        testId = intent.getStringExtra(EXTRA_TEST_ID) ?: return
        
        progressManager = TestProgressManager.getInstance(this)
        repository = TestResultsRepository.getInstance(this)
        analysisEngine = TestAnalysisEngine(repository)
        
        setupUI()
        loadTestProgress()
    }
    
    private fun setupUI() {
        binding.apply {
            // Setup toolbar
            toolbar.setNavigationOnClickListener { finish() }
            toolbar.title = getTestName(testId)
            
            // Setup RecyclerView
            resultsAdapter = TestResultsAdapter { testResult ->
                // Open detailed result analysis
                val intent = Intent(this@TestProgressActivity, TestResultsActivity::class.java).apply {
                    putExtra(TestResultsActivity.EXTRA_TEST_RESULT, Gson().toJson(testResult))
                }
                startActivity(intent)
            }
            
            resultsRecycler.apply {
                layoutManager = LinearLayoutManager(this@TestProgressActivity)
                adapter = resultsAdapter
            }
        }
    }
    
    private fun loadTestProgress() {
        val progress = progressManager.getTestProgress(testId)
        val results = progressManager.getTestResults(testId)
        val trend = progressManager.getPerformanceTrends(testId)
        val statistics = progressManager.getTestStatistics(testId)
        
        binding.apply {
            // Progress Overview
            timesCompletedText.text = progress.timesCompleted.toString()
            bestScoreText.text = "${progress.bestScore}%"
            averageScoreText.text = "${progress.averageScore.toInt()}%"
            
            // Progress Bar
            progressBar.progress = progress.averageScore.toInt()
            
            // Trend
            val trendIcon = when (trend.trendDirection) {
                com.leapiq.braintraining.data.TrendDirection.IMPROVING -> "📈"
                com.leapiq.braintraining.data.TrendDirection.DECLINING -> "📉"
                com.leapiq.braintraining.data.TrendDirection.STABLE -> "➡️"
            }
            
            trendIndicator.text = trendIcon
            trendDescription.text = when (trend.trendDirection) {
                com.leapiq.braintraining.data.TrendDirection.IMPROVING -> "Improving (${String.format("%.1f", trend.improvementPercentage)}%)"
                com.leapiq.braintraining.data.TrendDirection.DECLINING -> "Declining (${String.format("%.1f", kotlin.math.abs(trend.improvementPercentage))}%)"
                com.leapiq.braintraining.data.TrendDirection.STABLE -> "Stable performance"
            }
            
            // Statistics
            fastestTimeText.text = formatTime(statistics.fastestTime)
            averageTimeText.text = formatTime(statistics.averageTime)
            streakText.text = "${statistics.streakCount} attempts"
            consistencyText.text = "${trend.consistencyScore.toInt()}%"
            
            // Load results
            resultsAdapter.submitList(results.sortedByDescending { it.completedAt })
            
            // Show/hide empty state
            if (results.isEmpty()) {
                emptyStateContainer.visibility = android.view.View.VISIBLE
                contentContainer.visibility = android.view.View.GONE
            } else {
                emptyStateContainer.visibility = android.view.View.GONE
                contentContainer.visibility = android.view.View.VISIBLE
            }
            
            // Insights
            if (results.isNotEmpty()) {
                val latestResult = results.maxByOrNull { it.completedAt }
                latestResult?.let { result ->
                    val analysis = analysisEngine.analyzeTestResult(result)
                    
                    if (analysis.improvementRecommendations.isNotEmpty()) {
                        val topRecommendation = analysis.improvementRecommendations.first()
                        recommendationTitle.text = topRecommendation.title
                        recommendationDescription.text = topRecommendation.description
                        recommendationsContainer.visibility = android.view.View.VISIBLE
                    }
                }
            }
        }
    }
    
    private fun getTestName(testId: String): String {
        return when (testId) {
            "memory_assessment" -> "Memory Assessment"
            "attention_test" -> "Attention Test"
            "processing_speed" -> "Processing Speed"
            "learning_style" -> "Learning Style"
            "stress_response" -> "Stress Response"
            "problem_solving" -> "Problem Solving Style"
            else -> "Unknown Test"
        }
    }
    
    private fun formatTime(timeMs: Long): String {
        val seconds = timeMs / 1000
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        
        return if (minutes > 0) {
            "${minutes}m ${remainingSeconds}s"
        } else {
            "${remainingSeconds}s"
        }
    }
}
