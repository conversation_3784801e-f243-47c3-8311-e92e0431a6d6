<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_estimation" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_estimation.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_estimation_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="336" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/trial_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/estimation_type_text" view="TextView"><Expressions/><location startLine="103" startOffset="4" endLine="113" endOffset="34"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="116" startOffset="4" endLine="125" endOffset="33"/></Target><Target id="@+id/estimation_display" view="TextView"><Expressions/><location startLine="134" startOffset="8" endLine="145" endOffset="58"/></Target><Target id="@+id/answer_input" view="TextView"><Expressions/><location startLine="166" startOffset="8" endLine="176" endOffset="38"/></Target><Target id="@+id/btn_1" view="Button"><Expressions/><location startLine="197" startOffset="12" endLine="206" endOffset="40"/></Target><Target id="@+id/btn_2" view="Button"><Expressions/><location startLine="208" startOffset="12" endLine="217" endOffset="40"/></Target><Target id="@+id/btn_3" view="Button"><Expressions/><location startLine="219" startOffset="12" endLine="228" endOffset="40"/></Target><Target id="@+id/btn_4" view="Button"><Expressions/><location startLine="231" startOffset="12" endLine="240" endOffset="40"/></Target><Target id="@+id/btn_5" view="Button"><Expressions/><location startLine="242" startOffset="12" endLine="251" endOffset="40"/></Target><Target id="@+id/btn_6" view="Button"><Expressions/><location startLine="253" startOffset="12" endLine="262" endOffset="40"/></Target><Target id="@+id/btn_7" view="Button"><Expressions/><location startLine="265" startOffset="12" endLine="274" endOffset="40"/></Target><Target id="@+id/btn_8" view="Button"><Expressions/><location startLine="276" startOffset="12" endLine="285" endOffset="40"/></Target><Target id="@+id/btn_9" view="Button"><Expressions/><location startLine="287" startOffset="12" endLine="296" endOffset="40"/></Target><Target id="@+id/btn_clear" view="Button"><Expressions/><location startLine="299" startOffset="12" endLine="308" endOffset="40"/></Target><Target id="@+id/btn_0" view="Button"><Expressions/><location startLine="310" startOffset="12" endLine="319" endOffset="40"/></Target><Target id="@+id/btn_submit" view="Button"><Expressions/><location startLine="321" startOffset="12" endLine="330" endOffset="40"/></Target></Targets></Layout>