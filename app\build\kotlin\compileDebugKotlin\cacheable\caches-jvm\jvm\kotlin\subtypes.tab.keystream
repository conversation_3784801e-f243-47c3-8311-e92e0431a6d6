*com.leapiq.braintraining.data.BackupResult+com.leapiq.braintraining.data.RestoreResult*com.leapiq.braintraining.data.ExportResultkotlin.Enum2com.leapiq.braintraining.ui.tests.BaseTestActivity6com.leapiq.braintraining.ui.tests.BaseTestInfoActivity(androidx.appcompat.app.AppCompatActivity1androidx.recyclerview.widget.RecyclerView.Adapter2androidx.recyclerview.widget.DiffUtil.ItemCallbackandroidx.fragment.app.Fragment(androidx.recyclerview.widget.ListAdapter4androidx.recyclerview.widget.RecyclerView.ViewHolder androidx.viewbinding.ViewBinding0androidx.viewpager2.adapter.FragmentStateAdapter!android.content.BroadcastReceiver                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         