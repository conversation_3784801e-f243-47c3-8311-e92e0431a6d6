1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.leapiq.braintraining"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Notification permissions -->
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:6:5-77
12-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
14-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:8:5-74
14-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:8:22-71
15
16    <permission
16-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
17        android:name="com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
21
22    <application
22-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:10:5-256:19
23        android:allowBackup="true"
23-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:12:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:17:9-35
33        android:theme="@style/Theme.LeapIQ" >
33-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:18:9-44
34
35        <!-- Main Activity (Launcher with Splash Screen) -->
36        <activity
36-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:22:9-30:20
37            android:name="com.leapiq.braintraining.MainActivity"
37-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:23:13-41
38            android:exported="true"
38-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:24:13-36
39            android:theme="@style/Theme.LeapIQ.Splash" >
39-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:25:13-55
40            <intent-filter>
40-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:26:13-29:29
41                <action android:name="android.intent.action.MAIN" />
41-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:27:17-69
41-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:27:25-66
42
43                <category android:name="android.intent.category.LAUNCHER" />
43-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:28:17-77
43-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:28:27-74
44            </intent-filter>
45        </activity>
46
47        <!-- Onboarding Activities -->
48        <activity
48-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:33:9-37:52
49            android:name="com.leapiq.braintraining.WelcomeActivity"
49-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:34:13-44
50            android:exported="false"
50-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:35:13-37
51            android:screenOrientation="portrait"
51-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:37:13-49
52            android:theme="@style/Theme.LeapIQ" />
52-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:36:13-48
53        <activity
53-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:39:9-43:52
54            android:name="com.leapiq.braintraining.NameInputActivity"
54-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:40:13-46
55            android:exported="false"
55-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:41:13-37
56            android:screenOrientation="portrait"
56-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:43:13-49
57            android:theme="@style/Theme.LeapIQ" />
57-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:42:13-48
58        <activity
58-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:45:9-49:52
59            android:name="com.leapiq.braintraining.NotificationPermissionActivity"
59-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:46:13-59
60            android:exported="false"
60-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:47:13-37
61            android:screenOrientation="portrait"
61-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:49:13-49
62            android:theme="@style/Theme.LeapIQ" />
62-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:48:13-48
63
64        <!-- Game Activities -->
65        <activity
65-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:52:9-56:52
66            android:name="com.leapiq.braintraining.ui.games.CardMatchingActivity"
66-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:53:13-58
67            android:exported="false"
67-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:54:13-37
68            android:screenOrientation="portrait"
68-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:56:13-49
69            android:theme="@style/Theme.LeapIQ" />
69-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:55:13-48
70        <activity
70-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:58:9-62:52
71            android:name="com.leapiq.braintraining.ui.games.SpeedMathActivity"
71-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:59:13-55
72            android:exported="false"
72-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:60:13-37
73            android:screenOrientation="portrait"
73-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:62:13-49
74            android:theme="@style/Theme.LeapIQ" />
74-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:61:13-48
75        <activity
75-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:64:9-68:52
76            android:name="com.leapiq.braintraining.ui.games.StroopTestActivity"
76-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:65:13-56
77            android:exported="false"
77-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:66:13-37
78            android:screenOrientation="portrait"
78-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:68:13-49
79            android:theme="@style/Theme.LeapIQ" />
79-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:67:13-48
80        <activity
80-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:70:9-74:52
81            android:name="com.leapiq.braintraining.ui.games.VocabularyActivity"
81-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:71:13-56
82            android:exported="false"
82-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:72:13-37
83            android:screenOrientation="portrait"
83-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:74:13-49
84            android:theme="@style/Theme.LeapIQ" />
84-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:73:13-48
85        <activity
85-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:76:9-80:52
86            android:name="com.leapiq.braintraining.ui.games.WordSearchActivity"
86-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:77:13-56
87            android:exported="false"
87-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:78:13-37
88            android:screenOrientation="portrait"
88-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:80:13-49
89            android:theme="@style/Theme.LeapIQ" />
89-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:79:13-48
90
91        <!-- Memory Games -->
92        <activity
92-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:83:9-87:52
93            android:name="com.leapiq.braintraining.ui.games.SequenceRecallActivity"
93-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:84:13-60
94            android:exported="false"
94-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:85:13-37
95            android:screenOrientation="portrait"
95-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:87:13-49
96            android:theme="@style/Theme.LeapIQ" />
96-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:86:13-48
97        <activity
97-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:89:9-93:52
98            android:name="com.leapiq.braintraining.ui.games.PatternMemoryActivity"
98-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:90:13-59
99            android:exported="false"
99-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:91:13-37
100            android:screenOrientation="portrait"
100-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:93:13-49
101            android:theme="@style/Theme.LeapIQ" />
101-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:92:13-48
102        <activity
102-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:95:9-99:52
103            android:name="com.leapiq.braintraining.ui.games.NumberMemoryActivity"
103-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:96:13-58
104            android:exported="false"
104-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:97:13-37
105            android:screenOrientation="portrait"
105-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:99:13-49
106            android:theme="@style/Theme.LeapIQ" />
106-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:98:13-48
107
108        <!-- Attention Games -->
109        <activity
109-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:102:9-106:52
110            android:name="com.leapiq.braintraining.ui.games.ReactionTimeActivity"
110-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:103:13-58
111            android:exported="false"
111-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:104:13-37
112            android:screenOrientation="portrait"
112-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:106:13-49
113            android:theme="@style/Theme.LeapIQ" />
113-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:105:13-48
114        <activity
114-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:108:9-112:52
115            android:name="com.leapiq.braintraining.ui.games.VisualSearchActivity"
115-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:109:13-58
116            android:exported="false"
116-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:110:13-37
117            android:screenOrientation="portrait"
117-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:112:13-49
118            android:theme="@style/Theme.LeapIQ" />
118-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:111:13-48
119        <activity
119-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:114:9-118:52
120            android:name="com.leapiq.braintraining.ui.games.FocusChallengeActivity"
120-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:115:13-60
121            android:exported="false"
121-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:116:13-37
122            android:screenOrientation="portrait"
122-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:118:13-49
123            android:theme="@style/Theme.LeapIQ" />
123-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:117:13-48
124
125        <!-- Math Games -->
126        <activity
126-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:121:9-125:52
127            android:name="com.leapiq.braintraining.ui.games.MentalArithmeticActivity"
127-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:122:13-62
128            android:exported="false"
128-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:123:13-37
129            android:screenOrientation="portrait"
129-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:125:13-49
130            android:theme="@style/Theme.LeapIQ" />
130-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:124:13-48
131        <activity
131-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:127:9-131:52
132            android:name="com.leapiq.braintraining.ui.games.NumberSequencesActivity"
132-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:128:13-61
133            android:exported="false"
133-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:129:13-37
134            android:screenOrientation="portrait"
134-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:131:13-49
135            android:theme="@style/Theme.LeapIQ" />
135-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:130:13-48
136        <activity
136-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:133:9-137:52
137            android:name="com.leapiq.braintraining.ui.games.EstimationActivity"
137-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:134:13-56
138            android:exported="false"
138-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:135:13-37
139            android:screenOrientation="portrait"
139-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:137:13-49
140            android:theme="@style/Theme.LeapIQ" />
140-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:136:13-48
141
142        <!-- Logic Games -->
143        <activity
143-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:140:9-144:52
144            android:name="com.leapiq.braintraining.ui.games.TubeSortActivity"
144-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:141:13-54
145            android:exported="false"
145-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:142:13-37
146            android:screenOrientation="portrait"
146-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:144:13-49
147            android:theme="@style/Theme.LeapIQ" />
147-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:143:13-48
148        <activity
148-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:146:9-150:52
149            android:name="com.leapiq.braintraining.ui.games.LogicalReasoningActivity"
149-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:147:13-62
150            android:exported="false"
150-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:148:13-37
151            android:screenOrientation="portrait"
151-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:150:13-49
152            android:theme="@style/Theme.LeapIQ" />
152-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:149:13-48
153        <activity
153-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:152:9-156:52
154            android:name="com.leapiq.braintraining.ui.games.SpatialRotationActivity"
154-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:153:13-61
155            android:exported="false"
155-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:154:13-37
156            android:screenOrientation="portrait"
156-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:156:13-49
157            android:theme="@style/Theme.LeapIQ" />
157-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:155:13-48
158        <activity
158-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:158:9-162:52
159            android:name="com.leapiq.braintraining.ui.games.TowerOfHanoiActivity"
159-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:159:13-58
160            android:exported="false"
160-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:160:13-37
161            android:screenOrientation="portrait"
161-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:162:13-49
162            android:theme="@style/Theme.LeapIQ" />
162-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:161:13-48
163        <activity
163-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:164:9-168:52
164            android:name="com.leapiq.braintraining.ui.games.PatternCompletionActivity"
164-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:165:13-63
165            android:exported="false"
165-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:166:13-37
166            android:screenOrientation="portrait"
166-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:168:13-49
167            android:theme="@style/Theme.LeapIQ" />
167-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:167:13-48
168
169        <!-- Language Games -->
170        <activity
170-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:171:9-175:52
171            android:name="com.leapiq.braintraining.ui.games.WordAssociationActivity"
171-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:172:13-61
172            android:exported="false"
172-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:173:13-37
173            android:screenOrientation="portrait"
173-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:175:13-49
174            android:theme="@style/Theme.LeapIQ" />
174-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:174:13-48
175        <activity
175-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:177:9-181:52
176            android:name="com.leapiq.braintraining.ui.games.AnagramsActivity"
176-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:178:13-54
177            android:exported="false"
177-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:179:13-37
178            android:screenOrientation="portrait"
178-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:181:13-49
179            android:theme="@style/Theme.LeapIQ" />
179-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:180:13-48
180        <activity
180-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:183:9-187:52
181            android:name="com.leapiq.braintraining.ui.games.LogicPuzzlesActivity"
181-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:184:13-58
182            android:exported="false"
182-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:185:13-37
183            android:screenOrientation="portrait"
183-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:187:13-49
184            android:theme="@style/Theme.LeapIQ" />
184-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:186:13-48
185        <activity
185-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:189:9-193:52
186            android:name="com.leapiq.braintraining.ui.games.GameResultActivity"
186-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:190:13-56
187            android:exported="false"
187-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:191:13-37
188            android:screenOrientation="portrait"
188-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:193:13-49
189            android:theme="@style/Theme.LeapIQ" />
189-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:192:13-48
190
191        <!-- Test Info Activities -->
192        <activity
192-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:196:9-200:52
193            android:name="com.leapiq.braintraining.ui.tests.LearningStyleInfoActivity"
193-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:197:13-63
194            android:exported="false"
194-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:198:13-37
195            android:screenOrientation="portrait"
195-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:200:13-49
196            android:theme="@style/Theme.LeapIQ" />
196-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:199:13-48
197        <activity
197-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:202:9-206:52
198            android:name="com.leapiq.braintraining.ui.tests.StressResponseInfoActivity"
198-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:203:13-64
199            android:exported="false"
199-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:204:13-37
200            android:screenOrientation="portrait"
200-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:206:13-49
201            android:theme="@style/Theme.LeapIQ" />
201-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:205:13-48
202        <activity
202-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:208:9-212:52
203            android:name="com.leapiq.braintraining.ui.tests.ProblemSolvingStyleInfoActivity"
203-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:209:13-69
204            android:exported="false"
204-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:210:13-37
205            android:screenOrientation="portrait"
205-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:212:13-49
206            android:theme="@style/Theme.LeapIQ" />
206-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:211:13-48
207
208        <!-- Test Activities -->
209        <activity
209-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:215:9-219:52
210            android:name="com.leapiq.braintraining.ui.tests.LearningStyleActivity"
210-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:216:13-59
211            android:exported="false"
211-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:217:13-37
212            android:screenOrientation="portrait"
212-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:219:13-49
213            android:theme="@style/Theme.LeapIQ" />
213-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:218:13-48
214        <activity
214-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:221:9-225:52
215            android:name="com.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivity"
215-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:222:13-65
216            android:exported="false"
216-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:223:13-37
217            android:screenOrientation="portrait"
217-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:225:13-49
218            android:theme="@style/Theme.LeapIQ" />
218-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:224:13-48
219        <activity
219-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:227:9-231:52
220            android:name="com.leapiq.braintraining.ui.tests.StressResponseActivity"
220-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:228:13-60
221            android:exported="false"
221-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:229:13-37
222            android:screenOrientation="portrait"
222-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:231:13-49
223            android:theme="@style/Theme.LeapIQ" />
223-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:230:13-48
224        <activity
224-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:233:9-237:52
225            android:name="com.leapiq.braintraining.ui.tests.results.TestResultsActivity"
225-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:234:13-65
226            android:exported="false"
226-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:235:13-37
227            android:screenOrientation="portrait"
227-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:237:13-49
228            android:theme="@style/Theme.LeapIQ" />
228-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:236:13-48
229        <activity
229-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:239:9-243:52
230            android:name="com.leapiq.braintraining.ui.tests.results.AllResultsActivity"
230-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:240:13-64
231            android:exported="false"
231-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:241:13-37
232            android:screenOrientation="portrait"
232-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:243:13-49
233            android:theme="@style/Theme.LeapIQ" />
233-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:242:13-48
234        <activity
234-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:245:9-249:52
235            android:name="com.leapiq.braintraining.ui.tests.results.MyResultsActivity"
235-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:246:13-63
236            android:exported="false"
236-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:247:13-37
237            android:screenOrientation="portrait"
237-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:249:13-49
238            android:theme="@style/Theme.LeapIQ" />
238-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:248:13-48
239
240        <!-- Notification Receiver -->
241        <receiver
241-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:252:9-254:40
242            android:name="com.leapiq.braintraining.notifications.NotificationReceiver"
242-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:253:13-63
243            android:exported="false" />
243-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:254:13-37
244
245        <provider
245-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
246            android:name="androidx.startup.InitializationProvider"
246-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
247            android:authorities="com.leapiq.braintraining.androidx-startup"
247-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
248            android:exported="false" >
248-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
249            <meta-data
249-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
250                android:name="androidx.emoji2.text.EmojiCompatInitializer"
250-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
251                android:value="androidx.startup" />
251-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
252            <meta-data
252-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
253                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
253-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
254                android:value="androidx.startup" />
254-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
255            <meta-data
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
256                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
257                android:value="androidx.startup" />
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
258        </provider>
259
260        <uses-library
260-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
261            android:name="androidx.window.extensions"
261-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
262            android:required="false" />
262-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
263        <uses-library
263-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
264            android:name="androidx.window.sidecar"
264-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
265            android:required="false" />
265-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
266
267        <receiver
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
268            android:name="androidx.profileinstaller.ProfileInstallReceiver"
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
269            android:directBootAware="false"
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
270            android:enabled="true"
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
271            android:exported="true"
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
272            android:permission="android.permission.DUMP" >
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
273            <intent-filter>
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
274                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
275            </intent-filter>
276            <intent-filter>
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
277                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
278            </intent-filter>
279            <intent-filter>
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
280                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
281            </intent-filter>
282            <intent-filter>
282-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
283                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
283-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
283-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
284            </intent-filter>
285        </receiver>
286    </application>
287
288</manifest>
