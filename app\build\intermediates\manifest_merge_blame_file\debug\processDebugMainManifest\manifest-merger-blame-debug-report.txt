1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.leapiq.braintraining"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Notification permissions -->
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:6:5-77
12-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
14-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:8:5-74
14-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:8:22-71
15
16    <permission
16-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
17        android:name="com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
21
22    <application
22-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:10:5-256:19
23        android:allowBackup="true"
23-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:12:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:17:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.LeapIQ" >
34-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:18:9-44
35
36        <!-- Main Activity (Launcher with Splash Screen) -->
37        <activity
37-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:22:9-30:20
38            android:name="com.leapiq.braintraining.MainActivity"
38-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:23:13-41
39            android:exported="true"
39-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:24:13-36
40            android:theme="@style/Theme.LeapIQ.Splash" >
40-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:25:13-55
41            <intent-filter>
41-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:26:13-29:29
42                <action android:name="android.intent.action.MAIN" />
42-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:27:17-69
42-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:27:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:28:17-77
44-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:28:27-74
45            </intent-filter>
46        </activity>
47
48        <!-- Onboarding Activities -->
49        <activity
49-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:33:9-37:52
50            android:name="com.leapiq.braintraining.WelcomeActivity"
50-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:34:13-44
51            android:exported="false"
51-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:35:13-37
52            android:screenOrientation="portrait"
52-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:37:13-49
53            android:theme="@style/Theme.LeapIQ" />
53-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:36:13-48
54        <activity
54-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:39:9-43:52
55            android:name="com.leapiq.braintraining.NameInputActivity"
55-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:40:13-46
56            android:exported="false"
56-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:41:13-37
57            android:screenOrientation="portrait"
57-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:43:13-49
58            android:theme="@style/Theme.LeapIQ" />
58-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:42:13-48
59        <activity
59-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:45:9-49:52
60            android:name="com.leapiq.braintraining.NotificationPermissionActivity"
60-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:46:13-59
61            android:exported="false"
61-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:47:13-37
62            android:screenOrientation="portrait"
62-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:49:13-49
63            android:theme="@style/Theme.LeapIQ" />
63-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:48:13-48
64
65        <!-- Game Activities -->
66        <activity
66-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:52:9-56:52
67            android:name="com.leapiq.braintraining.ui.games.CardMatchingActivity"
67-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:53:13-58
68            android:exported="false"
68-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:54:13-37
69            android:screenOrientation="portrait"
69-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:56:13-49
70            android:theme="@style/Theme.LeapIQ" />
70-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:55:13-48
71        <activity
71-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:58:9-62:52
72            android:name="com.leapiq.braintraining.ui.games.SpeedMathActivity"
72-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:59:13-55
73            android:exported="false"
73-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:60:13-37
74            android:screenOrientation="portrait"
74-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:62:13-49
75            android:theme="@style/Theme.LeapIQ" />
75-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:61:13-48
76        <activity
76-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:64:9-68:52
77            android:name="com.leapiq.braintraining.ui.games.StroopTestActivity"
77-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:65:13-56
78            android:exported="false"
78-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:66:13-37
79            android:screenOrientation="portrait"
79-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:68:13-49
80            android:theme="@style/Theme.LeapIQ" />
80-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:67:13-48
81        <activity
81-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:70:9-74:52
82            android:name="com.leapiq.braintraining.ui.games.VocabularyActivity"
82-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:71:13-56
83            android:exported="false"
83-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:72:13-37
84            android:screenOrientation="portrait"
84-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:74:13-49
85            android:theme="@style/Theme.LeapIQ" />
85-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:73:13-48
86        <activity
86-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:76:9-80:52
87            android:name="com.leapiq.braintraining.ui.games.WordSearchActivity"
87-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:77:13-56
88            android:exported="false"
88-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:78:13-37
89            android:screenOrientation="portrait"
89-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:80:13-49
90            android:theme="@style/Theme.LeapIQ" />
90-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:79:13-48
91
92        <!-- Memory Games -->
93        <activity
93-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:83:9-87:52
94            android:name="com.leapiq.braintraining.ui.games.SequenceRecallActivity"
94-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:84:13-60
95            android:exported="false"
95-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:85:13-37
96            android:screenOrientation="portrait"
96-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:87:13-49
97            android:theme="@style/Theme.LeapIQ" />
97-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:86:13-48
98        <activity
98-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:89:9-93:52
99            android:name="com.leapiq.braintraining.ui.games.PatternMemoryActivity"
99-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:90:13-59
100            android:exported="false"
100-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:91:13-37
101            android:screenOrientation="portrait"
101-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:93:13-49
102            android:theme="@style/Theme.LeapIQ" />
102-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:92:13-48
103        <activity
103-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:95:9-99:52
104            android:name="com.leapiq.braintraining.ui.games.NumberMemoryActivity"
104-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:96:13-58
105            android:exported="false"
105-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:97:13-37
106            android:screenOrientation="portrait"
106-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:99:13-49
107            android:theme="@style/Theme.LeapIQ" />
107-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:98:13-48
108
109        <!-- Attention Games -->
110        <activity
110-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:102:9-106:52
111            android:name="com.leapiq.braintraining.ui.games.ReactionTimeActivity"
111-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:103:13-58
112            android:exported="false"
112-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:104:13-37
113            android:screenOrientation="portrait"
113-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:106:13-49
114            android:theme="@style/Theme.LeapIQ" />
114-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:105:13-48
115        <activity
115-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:108:9-112:52
116            android:name="com.leapiq.braintraining.ui.games.VisualSearchActivity"
116-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:109:13-58
117            android:exported="false"
117-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:110:13-37
118            android:screenOrientation="portrait"
118-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:112:13-49
119            android:theme="@style/Theme.LeapIQ" />
119-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:111:13-48
120        <activity
120-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:114:9-118:52
121            android:name="com.leapiq.braintraining.ui.games.FocusChallengeActivity"
121-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:115:13-60
122            android:exported="false"
122-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:116:13-37
123            android:screenOrientation="portrait"
123-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:118:13-49
124            android:theme="@style/Theme.LeapIQ" />
124-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:117:13-48
125
126        <!-- Math Games -->
127        <activity
127-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:121:9-125:52
128            android:name="com.leapiq.braintraining.ui.games.MentalArithmeticActivity"
128-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:122:13-62
129            android:exported="false"
129-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:123:13-37
130            android:screenOrientation="portrait"
130-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:125:13-49
131            android:theme="@style/Theme.LeapIQ" />
131-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:124:13-48
132        <activity
132-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:127:9-131:52
133            android:name="com.leapiq.braintraining.ui.games.NumberSequencesActivity"
133-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:128:13-61
134            android:exported="false"
134-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:129:13-37
135            android:screenOrientation="portrait"
135-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:131:13-49
136            android:theme="@style/Theme.LeapIQ" />
136-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:130:13-48
137        <activity
137-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:133:9-137:52
138            android:name="com.leapiq.braintraining.ui.games.EstimationActivity"
138-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:134:13-56
139            android:exported="false"
139-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:135:13-37
140            android:screenOrientation="portrait"
140-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:137:13-49
141            android:theme="@style/Theme.LeapIQ" />
141-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:136:13-48
142
143        <!-- Logic Games -->
144        <activity
144-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:140:9-144:52
145            android:name="com.leapiq.braintraining.ui.games.TubeSortActivity"
145-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:141:13-54
146            android:exported="false"
146-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:142:13-37
147            android:screenOrientation="portrait"
147-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:144:13-49
148            android:theme="@style/Theme.LeapIQ" />
148-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:143:13-48
149        <activity
149-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:146:9-150:52
150            android:name="com.leapiq.braintraining.ui.games.LogicalReasoningActivity"
150-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:147:13-62
151            android:exported="false"
151-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:148:13-37
152            android:screenOrientation="portrait"
152-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:150:13-49
153            android:theme="@style/Theme.LeapIQ" />
153-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:149:13-48
154        <activity
154-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:152:9-156:52
155            android:name="com.leapiq.braintraining.ui.games.SpatialRotationActivity"
155-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:153:13-61
156            android:exported="false"
156-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:154:13-37
157            android:screenOrientation="portrait"
157-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:156:13-49
158            android:theme="@style/Theme.LeapIQ" />
158-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:155:13-48
159        <activity
159-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:158:9-162:52
160            android:name="com.leapiq.braintraining.ui.games.TowerOfHanoiActivity"
160-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:159:13-58
161            android:exported="false"
161-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:160:13-37
162            android:screenOrientation="portrait"
162-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:162:13-49
163            android:theme="@style/Theme.LeapIQ" />
163-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:161:13-48
164        <activity
164-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:164:9-168:52
165            android:name="com.leapiq.braintraining.ui.games.PatternCompletionActivity"
165-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:165:13-63
166            android:exported="false"
166-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:166:13-37
167            android:screenOrientation="portrait"
167-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:168:13-49
168            android:theme="@style/Theme.LeapIQ" />
168-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:167:13-48
169
170        <!-- Language Games -->
171        <activity
171-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:171:9-175:52
172            android:name="com.leapiq.braintraining.ui.games.WordAssociationActivity"
172-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:172:13-61
173            android:exported="false"
173-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:173:13-37
174            android:screenOrientation="portrait"
174-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:175:13-49
175            android:theme="@style/Theme.LeapIQ" />
175-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:174:13-48
176        <activity
176-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:177:9-181:52
177            android:name="com.leapiq.braintraining.ui.games.AnagramsActivity"
177-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:178:13-54
178            android:exported="false"
178-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:179:13-37
179            android:screenOrientation="portrait"
179-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:181:13-49
180            android:theme="@style/Theme.LeapIQ" />
180-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:180:13-48
181        <activity
181-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:183:9-187:52
182            android:name="com.leapiq.braintraining.ui.games.LogicPuzzlesActivity"
182-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:184:13-58
183            android:exported="false"
183-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:185:13-37
184            android:screenOrientation="portrait"
184-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:187:13-49
185            android:theme="@style/Theme.LeapIQ" />
185-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:186:13-48
186        <activity
186-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:189:9-193:52
187            android:name="com.leapiq.braintraining.ui.games.GameResultActivity"
187-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:190:13-56
188            android:exported="false"
188-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:191:13-37
189            android:screenOrientation="portrait"
189-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:193:13-49
190            android:theme="@style/Theme.LeapIQ" />
190-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:192:13-48
191
192        <!-- Test Info Activities -->
193        <activity
193-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:196:9-200:52
194            android:name="com.leapiq.braintraining.ui.tests.LearningStyleInfoActivity"
194-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:197:13-63
195            android:exported="false"
195-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:198:13-37
196            android:screenOrientation="portrait"
196-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:200:13-49
197            android:theme="@style/Theme.LeapIQ" />
197-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:199:13-48
198        <activity
198-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:202:9-206:52
199            android:name="com.leapiq.braintraining.ui.tests.StressResponseInfoActivity"
199-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:203:13-64
200            android:exported="false"
200-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:204:13-37
201            android:screenOrientation="portrait"
201-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:206:13-49
202            android:theme="@style/Theme.LeapIQ" />
202-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:205:13-48
203        <activity
203-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:208:9-212:52
204            android:name="com.leapiq.braintraining.ui.tests.ProblemSolvingStyleInfoActivity"
204-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:209:13-69
205            android:exported="false"
205-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:210:13-37
206            android:screenOrientation="portrait"
206-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:212:13-49
207            android:theme="@style/Theme.LeapIQ" />
207-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:211:13-48
208
209        <!-- Test Activities -->
210        <activity
210-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:215:9-219:52
211            android:name="com.leapiq.braintraining.ui.tests.LearningStyleActivity"
211-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:216:13-59
212            android:exported="false"
212-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:217:13-37
213            android:screenOrientation="portrait"
213-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:219:13-49
214            android:theme="@style/Theme.LeapIQ" />
214-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:218:13-48
215        <activity
215-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:221:9-225:52
216            android:name="com.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivity"
216-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:222:13-65
217            android:exported="false"
217-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:223:13-37
218            android:screenOrientation="portrait"
218-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:225:13-49
219            android:theme="@style/Theme.LeapIQ" />
219-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:224:13-48
220        <activity
220-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:227:9-231:52
221            android:name="com.leapiq.braintraining.ui.tests.StressResponseActivity"
221-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:228:13-60
222            android:exported="false"
222-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:229:13-37
223            android:screenOrientation="portrait"
223-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:231:13-49
224            android:theme="@style/Theme.LeapIQ" />
224-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:230:13-48
225        <activity
225-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:233:9-237:52
226            android:name="com.leapiq.braintraining.ui.tests.results.TestResultsActivity"
226-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:234:13-65
227            android:exported="false"
227-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:235:13-37
228            android:screenOrientation="portrait"
228-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:237:13-49
229            android:theme="@style/Theme.LeapIQ" />
229-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:236:13-48
230        <activity
230-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:239:9-243:52
231            android:name="com.leapiq.braintraining.ui.tests.results.AllResultsActivity"
231-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:240:13-64
232            android:exported="false"
232-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:241:13-37
233            android:screenOrientation="portrait"
233-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:243:13-49
234            android:theme="@style/Theme.LeapIQ" />
234-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:242:13-48
235        <activity
235-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:245:9-249:52
236            android:name="com.leapiq.braintraining.ui.tests.results.MyResultsActivity"
236-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:246:13-63
237            android:exported="false"
237-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:247:13-37
238            android:screenOrientation="portrait"
238-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:249:13-49
239            android:theme="@style/Theme.LeapIQ" />
239-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:248:13-48
240
241        <!-- Notification Receiver -->
242        <receiver
242-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:252:9-254:40
243            android:name="com.leapiq.braintraining.notifications.NotificationReceiver"
243-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:253:13-63
244            android:exported="false" />
244-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:254:13-37
245
246        <provider
246-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
247            android:name="androidx.startup.InitializationProvider"
247-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
248            android:authorities="com.leapiq.braintraining.androidx-startup"
248-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
249            android:exported="false" >
249-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
250            <meta-data
250-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
251                android:name="androidx.emoji2.text.EmojiCompatInitializer"
251-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
252                android:value="androidx.startup" />
252-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
253            <meta-data
253-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
254                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
254-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
255                android:value="androidx.startup" />
255-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
256            <meta-data
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
257                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
258                android:value="androidx.startup" />
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
259        </provider>
260
261        <uses-library
261-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
262            android:name="androidx.window.extensions"
262-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
263            android:required="false" />
263-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
264        <uses-library
264-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
265            android:name="androidx.window.sidecar"
265-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
266            android:required="false" />
266-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
267
268        <receiver
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
269            android:name="androidx.profileinstaller.ProfileInstallReceiver"
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
270            android:directBootAware="false"
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
271            android:enabled="true"
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
272            android:exported="true"
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
273            android:permission="android.permission.DUMP" >
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
274            <intent-filter>
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
275                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
276            </intent-filter>
277            <intent-filter>
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
278                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
279            </intent-filter>
280            <intent-filter>
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
281                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
281-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
281-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
282            </intent-filter>
283            <intent-filter>
283-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
284                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
285            </intent-filter>
286        </receiver>
287    </application>
288
289</manifest>
