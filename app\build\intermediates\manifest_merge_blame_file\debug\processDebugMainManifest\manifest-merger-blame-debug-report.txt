1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.leapiq.braintraining"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Notification permissions -->
12    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
12-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:6:5-77
12-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:6:22-74
13    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
13-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:7:22-76
14    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
14-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:8:5-74
14-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:8:22-71
15
16    <permission
16-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
17        android:name="com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
17-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
18        android:protectionLevel="signature" />
18-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
19
20    <uses-permission android:name="com.leapiq.braintraining.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
20-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
21
22    <application
22-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:10:5-262:19
23        android:allowBackup="true"
23-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:11:9-35
24        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
24-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\c95e9bdb43d4dfa17dc0c6aa1e95084d\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
25        android:dataExtractionRules="@xml/data_extraction_rules"
25-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:12:9-65
26        android:debuggable="true"
27        android:extractNativeLibs="false"
28        android:fullBackupContent="@xml/backup_rules"
28-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:13:9-54
29        android:icon="@mipmap/ic_launcher"
29-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:14:9-43
30        android:label="@string/app_name"
30-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:15:9-41
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:16:9-54
32        android:supportsRtl="true"
32-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:17:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.LeapIQ" >
34-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:18:9-44
35
36        <!-- Splash Screen Activity (Launcher) -->
37        <activity
37-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:22:9-30:20
38            android:name="com.leapiq.braintraining.SplashActivity"
38-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:23:13-43
39            android:exported="true"
39-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:24:13-36
40            android:theme="@style/Theme.LeapIQ.Splash" >
40-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:25:13-55
41            <intent-filter>
41-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:26:13-29:29
42                <action android:name="android.intent.action.MAIN" />
42-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:27:17-69
42-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:27:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:28:17-77
44-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:28:27-74
45            </intent-filter>
46        </activity>
47
48        <!-- Main Activity -->
49        <activity
49-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:33:9-36:51
50            android:name="com.leapiq.braintraining.MainActivity"
50-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:34:13-41
51            android:exported="false"
51-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:35:13-37
52            android:theme="@style/Theme.LeapIQ" />
52-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:36:13-48
53
54        <!-- Onboarding Activities -->
55        <activity
55-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:39:9-43:52
56            android:name="com.leapiq.braintraining.WelcomeActivity"
56-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:40:13-44
57            android:exported="false"
57-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:41:13-37
58            android:screenOrientation="portrait"
58-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:43:13-49
59            android:theme="@style/Theme.LeapIQ" />
59-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:42:13-48
60        <activity
60-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:45:9-49:52
61            android:name="com.leapiq.braintraining.NameInputActivity"
61-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:46:13-46
62            android:exported="false"
62-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:47:13-37
63            android:screenOrientation="portrait"
63-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:49:13-49
64            android:theme="@style/Theme.LeapIQ" />
64-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:48:13-48
65        <activity
65-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:51:9-55:52
66            android:name="com.leapiq.braintraining.NotificationPermissionActivity"
66-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:52:13-59
67            android:exported="false"
67-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:53:13-37
68            android:screenOrientation="portrait"
68-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:55:13-49
69            android:theme="@style/Theme.LeapIQ" />
69-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:54:13-48
70
71        <!-- Game Activities -->
72        <activity
72-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:58:9-62:52
73            android:name="com.leapiq.braintraining.ui.games.CardMatchingActivity"
73-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:59:13-58
74            android:exported="false"
74-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:60:13-37
75            android:screenOrientation="portrait"
75-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:62:13-49
76            android:theme="@style/Theme.LeapIQ" />
76-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:61:13-48
77        <activity
77-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:64:9-68:52
78            android:name="com.leapiq.braintraining.ui.games.SpeedMathActivity"
78-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:65:13-55
79            android:exported="false"
79-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:66:13-37
80            android:screenOrientation="portrait"
80-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:68:13-49
81            android:theme="@style/Theme.LeapIQ" />
81-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:67:13-48
82        <activity
82-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:70:9-74:52
83            android:name="com.leapiq.braintraining.ui.games.StroopTestActivity"
83-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:71:13-56
84            android:exported="false"
84-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:72:13-37
85            android:screenOrientation="portrait"
85-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:74:13-49
86            android:theme="@style/Theme.LeapIQ" />
86-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:73:13-48
87        <activity
87-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:76:9-80:52
88            android:name="com.leapiq.braintraining.ui.games.VocabularyActivity"
88-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:77:13-56
89            android:exported="false"
89-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:78:13-37
90            android:screenOrientation="portrait"
90-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:80:13-49
91            android:theme="@style/Theme.LeapIQ" />
91-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:79:13-48
92        <activity
92-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:82:9-86:52
93            android:name="com.leapiq.braintraining.ui.games.WordSearchActivity"
93-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:83:13-56
94            android:exported="false"
94-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:84:13-37
95            android:screenOrientation="portrait"
95-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:86:13-49
96            android:theme="@style/Theme.LeapIQ" />
96-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:85:13-48
97
98        <!-- Memory Games -->
99        <activity
99-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:89:9-93:52
100            android:name="com.leapiq.braintraining.ui.games.SequenceRecallActivity"
100-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:90:13-60
101            android:exported="false"
101-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:91:13-37
102            android:screenOrientation="portrait"
102-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:93:13-49
103            android:theme="@style/Theme.LeapIQ" />
103-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:92:13-48
104        <activity
104-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:95:9-99:52
105            android:name="com.leapiq.braintraining.ui.games.PatternMemoryActivity"
105-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:96:13-59
106            android:exported="false"
106-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:97:13-37
107            android:screenOrientation="portrait"
107-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:99:13-49
108            android:theme="@style/Theme.LeapIQ" />
108-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:98:13-48
109        <activity
109-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:101:9-105:52
110            android:name="com.leapiq.braintraining.ui.games.NumberMemoryActivity"
110-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:102:13-58
111            android:exported="false"
111-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:103:13-37
112            android:screenOrientation="portrait"
112-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:105:13-49
113            android:theme="@style/Theme.LeapIQ" />
113-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:104:13-48
114
115        <!-- Attention Games -->
116        <activity
116-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:108:9-112:52
117            android:name="com.leapiq.braintraining.ui.games.ReactionTimeActivity"
117-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:109:13-58
118            android:exported="false"
118-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:110:13-37
119            android:screenOrientation="portrait"
119-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:112:13-49
120            android:theme="@style/Theme.LeapIQ" />
120-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:111:13-48
121        <activity
121-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:114:9-118:52
122            android:name="com.leapiq.braintraining.ui.games.VisualSearchActivity"
122-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:115:13-58
123            android:exported="false"
123-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:116:13-37
124            android:screenOrientation="portrait"
124-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:118:13-49
125            android:theme="@style/Theme.LeapIQ" />
125-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:117:13-48
126        <activity
126-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:120:9-124:52
127            android:name="com.leapiq.braintraining.ui.games.FocusChallengeActivity"
127-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:121:13-60
128            android:exported="false"
128-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:122:13-37
129            android:screenOrientation="portrait"
129-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:124:13-49
130            android:theme="@style/Theme.LeapIQ" />
130-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:123:13-48
131
132        <!-- Math Games -->
133        <activity
133-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:127:9-131:52
134            android:name="com.leapiq.braintraining.ui.games.MentalArithmeticActivity"
134-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:128:13-62
135            android:exported="false"
135-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:129:13-37
136            android:screenOrientation="portrait"
136-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:131:13-49
137            android:theme="@style/Theme.LeapIQ" />
137-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:130:13-48
138        <activity
138-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:133:9-137:52
139            android:name="com.leapiq.braintraining.ui.games.NumberSequencesActivity"
139-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:134:13-61
140            android:exported="false"
140-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:135:13-37
141            android:screenOrientation="portrait"
141-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:137:13-49
142            android:theme="@style/Theme.LeapIQ" />
142-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:136:13-48
143        <activity
143-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:139:9-143:52
144            android:name="com.leapiq.braintraining.ui.games.EstimationActivity"
144-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:140:13-56
145            android:exported="false"
145-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:141:13-37
146            android:screenOrientation="portrait"
146-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:143:13-49
147            android:theme="@style/Theme.LeapIQ" />
147-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:142:13-48
148
149        <!-- Logic Games -->
150        <activity
150-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:146:9-150:52
151            android:name="com.leapiq.braintraining.ui.games.TubeSortActivity"
151-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:147:13-54
152            android:exported="false"
152-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:148:13-37
153            android:screenOrientation="portrait"
153-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:150:13-49
154            android:theme="@style/Theme.LeapIQ" />
154-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:149:13-48
155        <activity
155-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:152:9-156:52
156            android:name="com.leapiq.braintraining.ui.games.LogicalReasoningActivity"
156-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:153:13-62
157            android:exported="false"
157-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:154:13-37
158            android:screenOrientation="portrait"
158-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:156:13-49
159            android:theme="@style/Theme.LeapIQ" />
159-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:155:13-48
160        <activity
160-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:158:9-162:52
161            android:name="com.leapiq.braintraining.ui.games.SpatialRotationActivity"
161-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:159:13-61
162            android:exported="false"
162-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:160:13-37
163            android:screenOrientation="portrait"
163-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:162:13-49
164            android:theme="@style/Theme.LeapIQ" />
164-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:161:13-48
165        <activity
165-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:164:9-168:52
166            android:name="com.leapiq.braintraining.ui.games.TowerOfHanoiActivity"
166-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:165:13-58
167            android:exported="false"
167-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:166:13-37
168            android:screenOrientation="portrait"
168-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:168:13-49
169            android:theme="@style/Theme.LeapIQ" />
169-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:167:13-48
170        <activity
170-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:170:9-174:52
171            android:name="com.leapiq.braintraining.ui.games.PatternCompletionActivity"
171-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:171:13-63
172            android:exported="false"
172-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:172:13-37
173            android:screenOrientation="portrait"
173-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:174:13-49
174            android:theme="@style/Theme.LeapIQ" />
174-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:173:13-48
175
176        <!-- Language Games -->
177        <activity
177-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:177:9-181:52
178            android:name="com.leapiq.braintraining.ui.games.WordAssociationActivity"
178-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:178:13-61
179            android:exported="false"
179-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:179:13-37
180            android:screenOrientation="portrait"
180-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:181:13-49
181            android:theme="@style/Theme.LeapIQ" />
181-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:180:13-48
182        <activity
182-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:183:9-187:52
183            android:name="com.leapiq.braintraining.ui.games.AnagramsActivity"
183-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:184:13-54
184            android:exported="false"
184-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:185:13-37
185            android:screenOrientation="portrait"
185-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:187:13-49
186            android:theme="@style/Theme.LeapIQ" />
186-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:186:13-48
187        <activity
187-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:189:9-193:52
188            android:name="com.leapiq.braintraining.ui.games.LogicPuzzlesActivity"
188-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:190:13-58
189            android:exported="false"
189-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:191:13-37
190            android:screenOrientation="portrait"
190-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:193:13-49
191            android:theme="@style/Theme.LeapIQ" />
191-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:192:13-48
192        <activity
192-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:195:9-199:52
193            android:name="com.leapiq.braintraining.ui.games.GameResultActivity"
193-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:196:13-56
194            android:exported="false"
194-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:197:13-37
195            android:screenOrientation="portrait"
195-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:199:13-49
196            android:theme="@style/Theme.LeapIQ" />
196-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:198:13-48
197
198        <!-- Test Info Activities -->
199        <activity
199-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:202:9-206:52
200            android:name="com.leapiq.braintraining.ui.tests.LearningStyleInfoActivity"
200-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:203:13-63
201            android:exported="false"
201-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:204:13-37
202            android:screenOrientation="portrait"
202-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:206:13-49
203            android:theme="@style/Theme.LeapIQ" />
203-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:205:13-48
204        <activity
204-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:208:9-212:52
205            android:name="com.leapiq.braintraining.ui.tests.StressResponseInfoActivity"
205-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:209:13-64
206            android:exported="false"
206-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:210:13-37
207            android:screenOrientation="portrait"
207-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:212:13-49
208            android:theme="@style/Theme.LeapIQ" />
208-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:211:13-48
209        <activity
209-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:214:9-218:52
210            android:name="com.leapiq.braintraining.ui.tests.ProblemSolvingStyleInfoActivity"
210-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:215:13-69
211            android:exported="false"
211-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:216:13-37
212            android:screenOrientation="portrait"
212-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:218:13-49
213            android:theme="@style/Theme.LeapIQ" />
213-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:217:13-48
214
215        <!-- Test Activities -->
216        <activity
216-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:221:9-225:52
217            android:name="com.leapiq.braintraining.ui.tests.LearningStyleActivity"
217-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:222:13-59
218            android:exported="false"
218-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:223:13-37
219            android:screenOrientation="portrait"
219-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:225:13-49
220            android:theme="@style/Theme.LeapIQ" />
220-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:224:13-48
221        <activity
221-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:227:9-231:52
222            android:name="com.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivity"
222-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:228:13-65
223            android:exported="false"
223-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:229:13-37
224            android:screenOrientation="portrait"
224-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:231:13-49
225            android:theme="@style/Theme.LeapIQ" />
225-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:230:13-48
226        <activity
226-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:233:9-237:52
227            android:name="com.leapiq.braintraining.ui.tests.StressResponseActivity"
227-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:234:13-60
228            android:exported="false"
228-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:235:13-37
229            android:screenOrientation="portrait"
229-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:237:13-49
230            android:theme="@style/Theme.LeapIQ" />
230-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:236:13-48
231        <activity
231-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:239:9-243:52
232            android:name="com.leapiq.braintraining.ui.tests.results.TestResultsActivity"
232-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:240:13-65
233            android:exported="false"
233-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:241:13-37
234            android:screenOrientation="portrait"
234-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:243:13-49
235            android:theme="@style/Theme.LeapIQ" />
235-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:242:13-48
236        <activity
236-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:245:9-249:52
237            android:name="com.leapiq.braintraining.ui.tests.results.AllResultsActivity"
237-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:246:13-64
238            android:exported="false"
238-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:247:13-37
239            android:screenOrientation="portrait"
239-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:249:13-49
240            android:theme="@style/Theme.LeapIQ" />
240-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:248:13-48
241        <activity
241-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:251:9-255:52
242            android:name="com.leapiq.braintraining.ui.tests.results.MyResultsActivity"
242-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:252:13-63
243            android:exported="false"
243-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:253:13-37
244            android:screenOrientation="portrait"
244-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:255:13-49
245            android:theme="@style/Theme.LeapIQ" />
245-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:254:13-48
246
247        <!-- Notification Receiver -->
248        <receiver
248-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:258:9-260:40
249            android:name="com.leapiq.braintraining.notifications.NotificationReceiver"
249-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:259:13-63
250            android:exported="false" />
250-->C:\Projects\LeapIQ\app\src\main\AndroidManifest.xml:260:13-37
251
252        <provider
252-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
253            android:name="androidx.startup.InitializationProvider"
253-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
254            android:authorities="com.leapiq.braintraining.androidx-startup"
254-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
255            android:exported="false" >
255-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
256            <meta-data
256-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
257                android:name="androidx.emoji2.text.EmojiCompatInitializer"
257-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
258                android:value="androidx.startup" />
258-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4bdc159810cefcba38f546fc9a17b66b\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
259            <meta-data
259-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
260                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
260-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
261                android:value="androidx.startup" />
261-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.13\transforms\38dc205524c2f1fdda2aa4425a5f45a3\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
262            <meta-data
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
263                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
264                android:value="androidx.startup" />
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
265        </provider>
266
267        <uses-library
267-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
268            android:name="androidx.window.extensions"
268-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
269            android:required="false" />
269-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
270        <uses-library
270-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
271            android:name="androidx.window.sidecar"
271-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
272            android:required="false" />
272-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\da172330d234cbb9e86d8b88d28479fb\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
273
274        <receiver
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
275            android:name="androidx.profileinstaller.ProfileInstallReceiver"
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
276            android:directBootAware="false"
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
277            android:enabled="true"
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
278            android:exported="true"
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
279            android:permission="android.permission.DUMP" >
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
280            <intent-filter>
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
281                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
281-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
281-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
282            </intent-filter>
283            <intent-filter>
283-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
284                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
285            </intent-filter>
286            <intent-filter>
286-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
287                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
287-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
287-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
288            </intent-filter>
289            <intent-filter>
289-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
290                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
290-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
290-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\70da2d0a973f59aac233f9bed3955b78\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
291            </intent-filter>
292        </receiver>
293    </application>
294
295</manifest>
