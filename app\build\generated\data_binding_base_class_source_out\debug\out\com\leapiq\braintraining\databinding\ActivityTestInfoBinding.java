// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTestInfoBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnStartTest;

  @NonNull
  public final TextView estimatedTime;

  @NonNull
  public final LinearLayout heroSection;

  @NonNull
  public final LinearLayout keyPointsList;

  @NonNull
  public final TextView testDescription;

  @NonNull
  public final ImageView testIcon;

  @NonNull
  public final TextView testSubtitle;

  @NonNull
  public final TextView testTitle;

  @NonNull
  public final Toolbar toolbar;

  private ActivityTestInfoBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton btnStartTest, @NonNull TextView estimatedTime,
      @NonNull LinearLayout heroSection, @NonNull LinearLayout keyPointsList,
      @NonNull TextView testDescription, @NonNull ImageView testIcon,
      @NonNull TextView testSubtitle, @NonNull TextView testTitle, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.btnStartTest = btnStartTest;
    this.estimatedTime = estimatedTime;
    this.heroSection = heroSection;
    this.keyPointsList = keyPointsList;
    this.testDescription = testDescription;
    this.testIcon = testIcon;
    this.testSubtitle = testSubtitle;
    this.testTitle = testTitle;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTestInfoBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTestInfoBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_test_info, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTestInfoBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_start_test;
      MaterialButton btnStartTest = ViewBindings.findChildViewById(rootView, id);
      if (btnStartTest == null) {
        break missingId;
      }

      id = R.id.estimated_time;
      TextView estimatedTime = ViewBindings.findChildViewById(rootView, id);
      if (estimatedTime == null) {
        break missingId;
      }

      id = R.id.hero_section;
      LinearLayout heroSection = ViewBindings.findChildViewById(rootView, id);
      if (heroSection == null) {
        break missingId;
      }

      id = R.id.key_points_list;
      LinearLayout keyPointsList = ViewBindings.findChildViewById(rootView, id);
      if (keyPointsList == null) {
        break missingId;
      }

      id = R.id.test_description;
      TextView testDescription = ViewBindings.findChildViewById(rootView, id);
      if (testDescription == null) {
        break missingId;
      }

      id = R.id.test_icon;
      ImageView testIcon = ViewBindings.findChildViewById(rootView, id);
      if (testIcon == null) {
        break missingId;
      }

      id = R.id.test_subtitle;
      TextView testSubtitle = ViewBindings.findChildViewById(rootView, id);
      if (testSubtitle == null) {
        break missingId;
      }

      id = R.id.test_title;
      TextView testTitle = ViewBindings.findChildViewById(rootView, id);
      if (testTitle == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityTestInfoBinding((LinearLayout) rootView, btnStartTest, estimatedTime,
          heroSection, keyPointsList, testDescription, testIcon, testSubtitle, testTitle, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
