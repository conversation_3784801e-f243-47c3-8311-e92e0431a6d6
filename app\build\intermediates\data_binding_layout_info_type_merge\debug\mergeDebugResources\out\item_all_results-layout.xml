<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_all_results" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\item_all_results.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_all_results_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="85" endOffset="51"/></Target><Target id="@+id/test_type_icon" view="TextView"><Expressions/><location startLine="20" startOffset="8" endLine="26" endOffset="45"/></Target><Target id="@+id/test_name_text" view="TextView"><Expressions/><location startLine="35" startOffset="12" endLine="43" endOffset="51"/></Target><Target id="@+id/date_text" view="TextView"><Expressions/><location startLine="45" startOffset="12" endLine="52" endOffset="51"/></Target><Target id="@+id/accuracy_text" view="TextView"><Expressions/><location startLine="54" startOffset="12" endLine="60" endOffset="41"/></Target><Target id="@+id/time_text" view="TextView"><Expressions/><location startLine="62" startOffset="12" endLine="68" endOffset="41"/></Target><Target id="@+id/score_text" view="TextView"><Expressions/><location startLine="73" startOffset="8" endLine="81" endOffset="47"/></Target></Targets></Layout>