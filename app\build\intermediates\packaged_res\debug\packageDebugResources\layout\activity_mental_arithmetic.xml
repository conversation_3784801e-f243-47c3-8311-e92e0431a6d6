<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.games.MentalArithmeticActivity">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/primary_light_blue"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Quit Button -->
        <ImageButton
            android:id="@+id/btn_quit"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="Quit game"
            app:tint="@color/text_white" />

        <!-- Game Title -->
        <TextView
            android:id="@+id/game_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Mental Arithmetic"
            android:textColor="@color/text_white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Menu Button -->
        <ImageButton
            android:id="@+id/btn_menu"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_menu"
            android:contentDescription="Game menu"
            app:tint="@color/text_white" />

    </LinearLayout>

    <!-- Game Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:elevation="2dp"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Level -->
        <TextView
            android:id="@+id/level_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Level 1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Round -->
        <TextView
            android:id="@+id/round_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Round 1/3"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Trial -->
        <TextView
            android:id="@+id/trial_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Problem 1/10"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Instructions -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:padding="16dp"
        android:text="Solve the math problems mentally!"
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />

    <!-- Problem Display -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@color/surface_white"
        android:padding="16dp">

        <!-- Math Problem -->
        <TextView
            android:id="@+id/problem_display"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/math_problem_background"
            android:gravity="center"
            android:text="25 + 17 = ?"
            android:textColor="@color/text_primary"
            android:textSize="32sp"
            android:textStyle="bold"
            tools:text="125 × 8 = ?" />

    </FrameLayout>

    <!-- Answer Input -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="16dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Answer: "
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/answer_input"
            android:layout_width="120dp"
            android:layout_height="48dp"
            android:background="@drawable/math_answer_background"
            android:gravity="center"
            android:hint="?"
            android:textColor="@color/text_primary"
            android:textColorHint="@color/text_secondary"
            android:textSize="24sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Number Pad -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Number Pad Grid -->
        <GridLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:columnCount="3"
            android:rowCount="5">

            <!-- Row 1: 1, 2, 3 -->
            <Button
                android:id="@+id/btn_1"
                android:layout_width="80dp"
                android:layout_height="60dp"
                android:layout_margin="4dp"
                android:text="1"
                android:textSize="18sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp" />

            <Button
                android:id="@+id/btn_2"
                android:layout_width="80dp"
                android:layout_height="60dp"
                android:layout_margin="4dp"
                android:text="2"
                android:textSize="18sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp" />

            <Button
                android:id="@+id/btn_3"
                android:layout_width="80dp"
                android:layout_height="60dp"
                android:layout_margin="4dp"
                android:text="3"
                android:textSize="18sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp" />

            <!-- Row 2: 4, 5, 6 -->
            <Button
                android:id="@+id/btn_4"
                android:layout_width="80dp"
                android:layout_height="60dp"
                android:layout_margin="4dp"
                android:text="4"
                android:textSize="18sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp" />

            <Button
                android:id="@+id/btn_5"
                android:layout_width="80dp"
                android:layout_height="60dp"
                android:layout_margin="4dp"
                android:text="5"
                android:textSize="18sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp" />

            <Button
                android:id="@+id/btn_6"
                android:layout_width="80dp"
                android:layout_height="60dp"
                android:layout_margin="4dp"
                android:text="6"
                android:textSize="18sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp" />

            <!-- Row 3: 7, 8, 9 -->
            <Button
                android:id="@+id/btn_7"
                android:layout_width="80dp"
                android:layout_height="60dp"
                android:layout_margin="4dp"
                android:text="7"
                android:textSize="18sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp" />

            <Button
                android:id="@+id/btn_8"
                android:layout_width="80dp"
                android:layout_height="60dp"
                android:layout_margin="4dp"
                android:text="8"
                android:textSize="18sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp" />

            <Button
                android:id="@+id/btn_9"
                android:layout_width="80dp"
                android:layout_height="60dp"
                android:layout_margin="4dp"
                android:text="9"
                android:textSize="18sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp" />

            <!-- Row 4: +/-, 0, Clear -->
            <Button
                android:id="@+id/btn_negative"
                android:layout_width="80dp"
                android:layout_height="60dp"
                android:layout_margin="4dp"
                android:text="+/-"
                android:textSize="14sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp" />

            <Button
                android:id="@+id/btn_0"
                android:layout_width="80dp"
                android:layout_height="60dp"
                android:layout_margin="4dp"
                android:text="0"
                android:textSize="18sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp" />

            <Button
                android:id="@+id/btn_clear"
                android:layout_width="80dp"
                android:layout_height="60dp"
                android:layout_margin="4dp"
                android:text="CLR"
                android:textSize="14sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp" />

            <!-- Row 5: Submit (spans 3 columns) -->
            <Button
                android:id="@+id/btn_submit"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_columnSpan="3"
                android:layout_columnWeight="1"
                android:layout_margin="4dp"
                android:text="SUBMIT"
                android:textSize="16sp"
                android:textStyle="bold"
                android:backgroundTint="@color/primary_light_blue"
                android:textColor="@color/text_white"
                app:cornerRadius="8dp" />

        </GridLayout>

    </LinearLayout>

</LinearLayout>
