<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_welcome" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_welcome.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_welcome_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="68" endOffset="14"/></Target><Target id="@+id/viewPager" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="9" startOffset="4" endLine="13" endOffset="35"/></Target><Target id="@+id/tabLayout" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="16" startOffset="4" endLine="26" endOffset="32"/></Target><Target id="@+id/btnPrevious" view="Button"><Expressions/><location startLine="37" startOffset="8" endLine="44" endOffset="44"/></Target><Target id="@+id/btnSkip" view="Button"><Expressions/><location startLine="47" startOffset="8" endLine="55" endOffset="38"/></Target><Target id="@+id/btnNext" view="Button"><Expressions/><location startLine="58" startOffset="8" endLine="64" endOffset="51"/></Target></Targets></Layout>