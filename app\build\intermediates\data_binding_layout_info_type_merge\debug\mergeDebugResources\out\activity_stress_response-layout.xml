<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_stress_response" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_stress_response.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_stress_response_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="379" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="50"/></Target><Target id="@+id/progress_text" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="42"/></Target><Target id="@+id/timer_container" view="LinearLayout"><Expressions/><location startLine="45" startOffset="12" endLine="71" endOffset="26"/></Target><Target id="@+id/timer_text" view="TextView"><Expressions/><location startLine="62" startOffset="16" endLine="69" endOffset="46"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="75" startOffset="8" endLine="81" endOffset="62"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="86" startOffset="4" endLine="96" endOffset="32"/></Target><Target id="@+id/question_number_text" view="TextView"><Expressions/><location startLine="124" startOffset="20" endLine="132" endOffset="60"/></Target><Target id="@+id/scenario_text" view="TextView"><Expressions/><location startLine="144" startOffset="20" endLine="154" endOffset="48"/></Target><Target id="@+id/question_text" view="TextView"><Expressions/><location startLine="157" startOffset="20" endLine="165" endOffset="56"/></Target><Target id="@+id/options_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="193" startOffset="20" endLine="196" endOffset="62"/></Target><Target id="@+id/btn_next" view="Button"><Expressions/><location startLine="355" startOffset="8" endLine="364" endOffset="39"/></Target><Target id="@+id/btn_submit" view="Button"><Expressions/><location startLine="366" startOffset="8" endLine="375" endOffset="39"/></Target></Targets></Layout>