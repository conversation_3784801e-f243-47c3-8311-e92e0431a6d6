package com.leapiq.braintraining.data.model

data class Game(
    val id: String,
    val name: String,
    val category: GameCategory,
    val description: String,
    val imageResource: String,
    val totalLevels: Int = 10,
    val completedLevels: Int = 0,
    val gameType: GameType = GameType.FREE,
    val isUnlocked: Boolean = true
) {
    val progressPercentage: Int
        get() = if (totalLevels > 0) (completedLevels * 100) / totalLevels else 0
}

enum class GameCategory(val displayName: String) {
    ALL("All"),
    MEMORY("Memory"),
    ATTENTION("Attention"),
    MATH("Math"),
    LOGIC("Logic"),
    LANGUAGE("Language")
}

enum class GameType {
    FREE,           // Free with ads
    UNLOCKABLE,     // Unlocked by reaching certain levels
    PREMIUM         // Premium only
}
