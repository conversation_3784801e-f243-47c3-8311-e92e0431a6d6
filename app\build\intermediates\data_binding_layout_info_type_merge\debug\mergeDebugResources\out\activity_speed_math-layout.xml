<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_speed_math" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_speed_math.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_speed_math_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="422" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/timer_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/score_text" view="TextView"><Expressions/><location startLine="101" startOffset="8" endLine="110" endOffset="38"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="115" startOffset="4" endLine="124" endOffset="33"/></Target><Target id="@+id/problem_display" view="TextView"><Expressions/><location startLine="134" startOffset="8" endLine="144" endOffset="36"/></Target><Target id="@+id/multiple_choice_container" view="LinearLayout"><Expressions/><location startLine="149" startOffset="4" endLine="232" endOffset="18"/></Target><Target id="@+id/btn_option1" view="Button"><Expressions/><location startLine="165" startOffset="12" endLine="177" endOffset="41"/></Target><Target id="@+id/btn_option2" view="Button"><Expressions/><location startLine="179" startOffset="12" endLine="191" endOffset="41"/></Target><Target id="@+id/btn_option3" view="Button"><Expressions/><location startLine="202" startOffset="12" endLine="214" endOffset="41"/></Target><Target id="@+id/btn_option4" view="Button"><Expressions/><location startLine="216" startOffset="12" endLine="228" endOffset="41"/></Target><Target id="@+id/number_pad_container" view="LinearLayout"><Expressions/><location startLine="235" startOffset="4" endLine="420" endOffset="18"/></Target><Target id="@+id/answer_input" view="TextView"><Expressions/><location startLine="260" startOffset="12" endLine="270" endOffset="42"/></Target><Target id="@+id/btn_1" view="Button"><Expressions/><location startLine="283" startOffset="12" endLine="292" endOffset="40"/></Target><Target id="@+id/btn_2" view="Button"><Expressions/><location startLine="294" startOffset="12" endLine="303" endOffset="40"/></Target><Target id="@+id/btn_3" view="Button"><Expressions/><location startLine="305" startOffset="12" endLine="314" endOffset="40"/></Target><Target id="@+id/btn_4" view="Button"><Expressions/><location startLine="317" startOffset="12" endLine="326" endOffset="40"/></Target><Target id="@+id/btn_5" view="Button"><Expressions/><location startLine="328" startOffset="12" endLine="337" endOffset="40"/></Target><Target id="@+id/btn_6" view="Button"><Expressions/><location startLine="339" startOffset="12" endLine="348" endOffset="40"/></Target><Target id="@+id/btn_7" view="Button"><Expressions/><location startLine="351" startOffset="12" endLine="360" endOffset="40"/></Target><Target id="@+id/btn_8" view="Button"><Expressions/><location startLine="362" startOffset="12" endLine="371" endOffset="40"/></Target><Target id="@+id/btn_9" view="Button"><Expressions/><location startLine="373" startOffset="12" endLine="382" endOffset="40"/></Target><Target id="@+id/btn_clear" view="Button"><Expressions/><location startLine="385" startOffset="12" endLine="394" endOffset="40"/></Target><Target id="@+id/btn_0" view="Button"><Expressions/><location startLine="396" startOffset="12" endLine="405" endOffset="40"/></Target><Target id="@+id/btn_submit" view="Button"><Expressions/><location startLine="407" startOffset="12" endLine="416" endOffset="40"/></Target></Targets></Layout>