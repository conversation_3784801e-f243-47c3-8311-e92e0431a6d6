package com.leapiq.braintraining.ui.games.memory

import android.animation.ObjectAnimator
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R

class MemoryCardAdapter(
    private val cards: List<MemoryCard>,
    private val onCardClicked: (Int) -> Unit
) : RecyclerView.Adapter<MemoryCardAdapter.CardViewHolder>() {

    class CardViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val cardImage: ImageView = itemView.findViewById(R.id.card_image)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): CardViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_memory_card, parent, false)
        return CardViewHolder(view)
    }

    override fun onBindViewHolder(holder: <PERSON>ViewHolder, position: Int) {
        val card = cards[position]

        // Set the image based on card state
        if (card.isFlipped || card.isMatched) {
            holder.cardImage.setImageResource(card.imageRes)
        } else {
            holder.cardImage.setImageResource(R.drawable.ic_card_back)
        }

        // Set alpha for matched cards
        holder.cardImage.alpha = if (card.isMatched) 0.6f else 1.0f

        // Handle click
        holder.itemView.setOnClickListener {
            if (!card.isFlipped && !card.isMatched) {
                onCardClicked(position)
            }
        }
    }

    override fun getItemCount() = cards.size

    fun flipCard(position: Int, showFront: Boolean) {
        if (position in cards.indices) {
            cards[position].isFlipped = showFront
            notifyItemChanged(position)
        }
    }

    fun flipCardWithAnimation(position: Int, showFront: Boolean, onComplete: (() -> Unit)? = null) {
        val holder = findViewHolderForAdapterPosition(position) as? CardViewHolder
        val card = cards[position]

        holder?.let {
            val flipOut = ObjectAnimator.ofFloat(it.cardImage, "rotationY", 0f, 90f)
            flipOut.duration = 150

            flipOut.addListener(object : android.animation.AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: android.animation.Animator) {
                    // Change image at halfway point
                    if (showFront) {
                        card.isFlipped = true
                        it.cardImage.setImageResource(card.imageRes)
                    } else {
                        card.isFlipped = false
                        it.cardImage.setImageResource(R.drawable.ic_card_back)
                    }

                    // Flip back in
                    val flipIn = ObjectAnimator.ofFloat(it.cardImage, "rotationY", -90f, 0f)
                    flipIn.duration = 150
                    flipIn.addListener(object : android.animation.AnimatorListenerAdapter() {
                        override fun onAnimationEnd(animation: android.animation.Animator) {
                            onComplete?.invoke()
                        }
                    })
                    flipIn.start()
                }
            })

            flipOut.start()
        } ?: run {
            // Fallback if no holder found
            if (showFront) {
                card.isFlipped = true
            } else {
                card.isFlipped = false
            }
            notifyItemChanged(position)
            onComplete?.invoke()
        }
    }

    private var recyclerView: RecyclerView? = null

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
    }

    private fun findViewHolderForAdapterPosition(position: Int): RecyclerView.ViewHolder? {
        return recyclerView?.findViewHolderForAdapterPosition(position)
    }
}
