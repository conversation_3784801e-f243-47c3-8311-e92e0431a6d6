package com.leapiq.braintraining.ui.games

import android.content.Intent
import android.os.Bundle
import android.os.SystemClock
import androidx.appcompat.app.AppCompatActivity
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date

/**
 * Base class for all brain training games
 * Provides common functionality for 5-round levels, timing, scoring, and progress tracking
 */
abstract class BaseGameActivity : AppCompatActivity() {
    
    protected lateinit var progressManager: GameProgressManager
    protected lateinit var gameId: String
    protected var currentLevel: Int = 1
    protected var currentRound: Int = 1
    protected val maxRounds: Int = 5
    
    // Timing
    protected var levelStartTime: Long = 0
    protected var roundStartTime: Long = 0
    
    // Results tracking
    protected val roundResults = mutableListOf<RoundResult>()
    
    companion object {
        const val EXTRA_GAME_ID = "game_id"
        const val EXTRA_LEVEL = "level"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        progressManager = GameProgressManager.getInstance(this)
        
        // Get game parameters from intent
        gameId = intent.getStringExtra(EXTRA_GAME_ID) ?: ""
        currentLevel = intent.getIntExtra(EXTRA_LEVEL, 1)
        
        if (gameId.isEmpty()) {
            finish()
            return
        }
        
        // Verify level is unlocked
        if (!progressManager.isLevelUnlocked(gameId, currentLevel)) {
            finish()
            return
        }
        
        initializeGame()
        startLevel()
    }
    
    /**
     * Initialize game-specific UI and logic
     */
    protected abstract fun initializeGame()
    
    /**
     * Start a new round of the game
     */
    protected abstract fun startRound()
    
    /**
     * Get the difficulty parameters for the current level
     */
    protected abstract fun getDifficultyForLevel(level: Int): GameDifficulty
    
    /**
     * Show game instructions or tutorial
     */
    protected abstract fun showInstructions()
    
    /**
     * Start the level (5 rounds)
     */
    protected fun startLevel() {
        levelStartTime = SystemClock.elapsedRealtime()
        currentRound = 1
        roundResults.clear()
        
        showInstructions()
        startRound()
    }
    
    /**
     * Start a new round
     */
    protected fun startNewRound() {
        roundStartTime = SystemClock.elapsedRealtime()
        startRound()
    }
    
    /**
     * Complete the current round with result
     */
    protected fun completeRound(isCorrect: Boolean, attempts: Int = 1) {
        val roundTime = SystemClock.elapsedRealtime() - roundStartTime
        
        val roundResult = RoundResult(
            roundNumber = currentRound,
            isCorrect = isCorrect,
            timeSpentMs = roundTime,
            attempts = attempts
        )
        
        roundResults.add(roundResult)
        
        if (currentRound < maxRounds) {
            currentRound++
            startNewRound()
        } else {
            completeLevel()
        }
    }
    
    /**
     * Complete the level and calculate results
     */
    private fun completeLevel() {
        val totalTime = SystemClock.elapsedRealtime() - levelStartTime
        val correctRounds = roundResults.count { it.isCorrect }
        val accuracy = correctRounds.toDouble() / maxRounds
        val score = (accuracy * 100).toInt()
        
        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults.toList(),
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = score,
            completedAt = Date()
        )
        
        // Save progress
        progressManager.saveLevelResult(levelResult)
        
        // Show results
        showLevelResults(levelResult)
    }
    
    /**
     * Show level completion results
     */
    private fun showLevelResults(levelResult: LevelResult) {
        val intent = Intent(this, GameResultActivity::class.java).apply {
            putExtra(GameResultActivity.EXTRA_GAME_ID, gameId)
            putExtra(GameResultActivity.EXTRA_LEVEL, currentLevel)
            putExtra(GameResultActivity.EXTRA_ACCURACY, levelResult.accuracyPercentage)
            putExtra(GameResultActivity.EXTRA_TIME_MS, levelResult.totalTimeMs)
            putExtra(GameResultActivity.EXTRA_SCORE, levelResult.score)
            putExtra(GameResultActivity.EXTRA_CORRECT_ROUNDS, levelResult.correctRounds)
            putExtra(GameResultActivity.EXTRA_TOTAL_ROUNDS, levelResult.totalRounds)
        }
        startActivity(intent)
        finish()
    }
    
    /**
     * Get current difficulty settings
     */
    protected fun getCurrentDifficulty(): GameDifficulty {
        return getDifficultyForLevel(currentLevel)
    }
    
    /**
     * Format time for display
     */
    protected fun formatTime(timeMs: Long): String {
        val seconds = timeMs / 1000
        val minutes = seconds / 60
        val remainingSeconds = seconds % 60
        
        return if (minutes > 0) {
            String.format("%d:%02d", minutes, remainingSeconds)
        } else {
            String.format("%d sec", remainingSeconds)
        }
    }
    
    /**
     * Show round feedback (correct/incorrect)
     */
    protected fun showRoundFeedback(isCorrect: Boolean, onComplete: () -> Unit) {
        // Default feedback implementation - can be overridden by specific games
        val feedbackText = if (isCorrect) "Correct!" else "Try again!"

        // Show brief feedback using Toast (games can override for custom UI)
        android.widget.Toast.makeText(this, feedbackText, android.widget.Toast.LENGTH_SHORT).show()

        // Proceed after brief delay
        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
            onComplete()
        }, 1000)
    }
}

/**
 * Represents difficulty settings for a game level
 */
data class GameDifficulty(
    val complexity: Int,        // Game-specific complexity parameter
    val timeLimit: Long? = null, // Time limit in ms (null for untimed games)
    val itemCount: Int,         // Number of items/cards/questions
    val speed: Float = 1.0f     // Speed multiplier for animations
)
