<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_learning_style" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_learning_style.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_learning_style_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="341" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="50"/></Target><Target id="@+id/progress_text" view="TextView"><Expressions/><location startLine="34" startOffset="12" endLine="42" endOffset="42"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="53" startOffset="8" endLine="59" endOffset="62"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="64" startOffset="4" endLine="74" endOffset="32"/></Target><Target id="@+id/question_number_text" view="TextView"><Expressions/><location startLine="102" startOffset="20" endLine="110" endOffset="59"/></Target><Target id="@+id/question_text" view="TextView"><Expressions/><location startLine="112" startOffset="20" endLine="119" endOffset="56"/></Target><Target id="@+id/options_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="147" startOffset="20" endLine="150" endOffset="62"/></Target><Target id="@+id/btn_previous" view="Button"><Expressions/><location startLine="305" startOffset="8" endLine="315" endOffset="39"/></Target><Target id="@+id/btn_next" view="Button"><Expressions/><location startLine="317" startOffset="8" endLine="326" endOffset="39"/></Target><Target id="@+id/btn_submit" view="Button"><Expressions/><location startLine="328" startOffset="8" endLine="337" endOffset="39"/></Target></Targets></Layout>