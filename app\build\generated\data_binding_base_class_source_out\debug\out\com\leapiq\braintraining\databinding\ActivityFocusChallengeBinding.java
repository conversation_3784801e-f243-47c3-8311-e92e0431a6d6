// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityFocusChallengeBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnMenu;

  @NonNull
  public final ImageButton btnQuit;

  @NonNull
  public final TextView feedbackText;

  @NonNull
  public final TextView gameTitle;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final TextView levelText;

  @NonNull
  public final TextView progressText;

  @NonNull
  public final FrameLayout responseArea;

  @NonNull
  public final TextView roundText;

  @NonNull
  public final TextView stimulusDisplay;

  @NonNull
  public final TextView targetDisplay;

  private ActivityFocusChallengeBinding(@NonNull LinearLayout rootView,
      @NonNull ImageButton btnMenu, @NonNull ImageButton btnQuit, @NonNull TextView feedbackText,
      @NonNull TextView gameTitle, @NonNull TextView instructionText, @NonNull TextView levelText,
      @NonNull TextView progressText, @NonNull FrameLayout responseArea,
      @NonNull TextView roundText, @NonNull TextView stimulusDisplay,
      @NonNull TextView targetDisplay) {
    this.rootView = rootView;
    this.btnMenu = btnMenu;
    this.btnQuit = btnQuit;
    this.feedbackText = feedbackText;
    this.gameTitle = gameTitle;
    this.instructionText = instructionText;
    this.levelText = levelText;
    this.progressText = progressText;
    this.responseArea = responseArea;
    this.roundText = roundText;
    this.stimulusDisplay = stimulusDisplay;
    this.targetDisplay = targetDisplay;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityFocusChallengeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityFocusChallengeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_focus_challenge, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityFocusChallengeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_menu;
      ImageButton btnMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnMenu == null) {
        break missingId;
      }

      id = R.id.btn_quit;
      ImageButton btnQuit = ViewBindings.findChildViewById(rootView, id);
      if (btnQuit == null) {
        break missingId;
      }

      id = R.id.feedback_text;
      TextView feedbackText = ViewBindings.findChildViewById(rootView, id);
      if (feedbackText == null) {
        break missingId;
      }

      id = R.id.game_title;
      TextView gameTitle = ViewBindings.findChildViewById(rootView, id);
      if (gameTitle == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.level_text;
      TextView levelText = ViewBindings.findChildViewById(rootView, id);
      if (levelText == null) {
        break missingId;
      }

      id = R.id.progress_text;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      id = R.id.response_area;
      FrameLayout responseArea = ViewBindings.findChildViewById(rootView, id);
      if (responseArea == null) {
        break missingId;
      }

      id = R.id.round_text;
      TextView roundText = ViewBindings.findChildViewById(rootView, id);
      if (roundText == null) {
        break missingId;
      }

      id = R.id.stimulus_display;
      TextView stimulusDisplay = ViewBindings.findChildViewById(rootView, id);
      if (stimulusDisplay == null) {
        break missingId;
      }

      id = R.id.target_display;
      TextView targetDisplay = ViewBindings.findChildViewById(rootView, id);
      if (targetDisplay == null) {
        break missingId;
      }

      return new ActivityFocusChallengeBinding((LinearLayout) rootView, btnMenu, btnQuit,
          feedbackText, gameTitle, instructionText, levelText, progressText, responseArea,
          roundText, stimulusDisplay, targetDisplay);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
