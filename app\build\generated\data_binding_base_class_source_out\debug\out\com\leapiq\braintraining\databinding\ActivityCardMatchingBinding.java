// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityCardMatchingBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnMenu;

  @NonNull
  public final ImageButton btnQuit;

  @NonNull
  public final RecyclerView cardGrid;

  @NonNull
  public final TextView gameTitle;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final TextView levelText;

  @NonNull
  public final TextView roundText;

  private ActivityCardMatchingBinding(@NonNull LinearLayout rootView, @NonNull ImageButton btnMenu,
      @NonNull ImageButton btnQuit, @NonNull RecyclerView cardGrid, @NonNull TextView gameTitle,
      @NonNull TextView instructionText, @NonNull TextView levelText, @NonNull TextView roundText) {
    this.rootView = rootView;
    this.btnMenu = btnMenu;
    this.btnQuit = btnQuit;
    this.cardGrid = cardGrid;
    this.gameTitle = gameTitle;
    this.instructionText = instructionText;
    this.levelText = levelText;
    this.roundText = roundText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityCardMatchingBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityCardMatchingBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_card_matching, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityCardMatchingBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_menu;
      ImageButton btnMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnMenu == null) {
        break missingId;
      }

      id = R.id.btn_quit;
      ImageButton btnQuit = ViewBindings.findChildViewById(rootView, id);
      if (btnQuit == null) {
        break missingId;
      }

      id = R.id.card_grid;
      RecyclerView cardGrid = ViewBindings.findChildViewById(rootView, id);
      if (cardGrid == null) {
        break missingId;
      }

      id = R.id.game_title;
      TextView gameTitle = ViewBindings.findChildViewById(rootView, id);
      if (gameTitle == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.level_text;
      TextView levelText = ViewBindings.findChildViewById(rootView, id);
      if (levelText == null) {
        break missingId;
      }

      id = R.id.round_text;
      TextView roundText = ViewBindings.findChildViewById(rootView, id);
      if (roundText == null) {
        break missingId;
      }

      return new ActivityCardMatchingBinding((LinearLayout) rootView, btnMenu, btnQuit, cardGrid,
          gameTitle, instructionText, levelText, roundText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
