// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityGameResultBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ProgressBar accuracyProgress;

  @NonNull
  public final TextView accuracyText;

  @NonNull
  public final MaterialButton backToGamesButton;

  @NonNull
  public final TextView gameTitle;

  @NonNull
  public final TextView levelText;

  @NonNull
  public final MaterialButton nextLevelButton;

  @NonNull
  public final TextView performanceMessage;

  @NonNull
  public final MaterialButton replayButton;

  @NonNull
  public final TextView roundsText;

  @NonNull
  public final TextView scoreText;

  @NonNull
  public final TextView timeText;

  private ActivityGameResultBinding(@NonNull LinearLayout rootView,
      @NonNull ProgressBar accuracyProgress, @NonNull TextView accuracyText,
      @NonNull MaterialButton backToGamesButton, @NonNull TextView gameTitle,
      @NonNull TextView levelText, @NonNull MaterialButton nextLevelButton,
      @NonNull TextView performanceMessage, @NonNull MaterialButton replayButton,
      @NonNull TextView roundsText, @NonNull TextView scoreText, @NonNull TextView timeText) {
    this.rootView = rootView;
    this.accuracyProgress = accuracyProgress;
    this.accuracyText = accuracyText;
    this.backToGamesButton = backToGamesButton;
    this.gameTitle = gameTitle;
    this.levelText = levelText;
    this.nextLevelButton = nextLevelButton;
    this.performanceMessage = performanceMessage;
    this.replayButton = replayButton;
    this.roundsText = roundsText;
    this.scoreText = scoreText;
    this.timeText = timeText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityGameResultBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityGameResultBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_game_result, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityGameResultBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.accuracy_progress;
      ProgressBar accuracyProgress = ViewBindings.findChildViewById(rootView, id);
      if (accuracyProgress == null) {
        break missingId;
      }

      id = R.id.accuracy_text;
      TextView accuracyText = ViewBindings.findChildViewById(rootView, id);
      if (accuracyText == null) {
        break missingId;
      }

      id = R.id.back_to_games_button;
      MaterialButton backToGamesButton = ViewBindings.findChildViewById(rootView, id);
      if (backToGamesButton == null) {
        break missingId;
      }

      id = R.id.game_title;
      TextView gameTitle = ViewBindings.findChildViewById(rootView, id);
      if (gameTitle == null) {
        break missingId;
      }

      id = R.id.level_text;
      TextView levelText = ViewBindings.findChildViewById(rootView, id);
      if (levelText == null) {
        break missingId;
      }

      id = R.id.next_level_button;
      MaterialButton nextLevelButton = ViewBindings.findChildViewById(rootView, id);
      if (nextLevelButton == null) {
        break missingId;
      }

      id = R.id.performance_message;
      TextView performanceMessage = ViewBindings.findChildViewById(rootView, id);
      if (performanceMessage == null) {
        break missingId;
      }

      id = R.id.replay_button;
      MaterialButton replayButton = ViewBindings.findChildViewById(rootView, id);
      if (replayButton == null) {
        break missingId;
      }

      id = R.id.rounds_text;
      TextView roundsText = ViewBindings.findChildViewById(rootView, id);
      if (roundsText == null) {
        break missingId;
      }

      id = R.id.score_text;
      TextView scoreText = ViewBindings.findChildViewById(rootView, id);
      if (scoreText == null) {
        break missingId;
      }

      id = R.id.time_text;
      TextView timeText = ViewBindings.findChildViewById(rootView, id);
      if (timeText == null) {
        break missingId;
      }

      return new ActivityGameResultBinding((LinearLayout) rootView, accuracyProgress, accuracyText,
          backToGamesButton, gameTitle, levelText, nextLevelButton, performanceMessage,
          replayButton, roundsText, scoreText, timeText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
