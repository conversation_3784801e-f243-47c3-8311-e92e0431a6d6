package com.leapiq.braintraining.notifications

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import com.leapiq.braintraining.MainActivity
import com.leapiq.braintraining.R

class NotificationService(private val context: Context) {
    
    companion object {
        private const val CHANNEL_ID = "leapiq_reminders"
        private const val CHANNEL_NAME = "Brain Training Reminders"
        private const val CHANNEL_DESCRIPTION = "Daily reminders and motivational messages"
        
        // Notification IDs
        const val DAILY_REMINDER_ID = 1001
        const val CHALLENGE_REMINDER_ID = 1002
        const val PROGRESS_CELEBRATION_ID = 1003
        const val MOTIVATIONAL_TIP_ID = 1004
    }
    
    init {
        createNotificationChannel()
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = CHANNEL_DESCRIPTION
                enableVibration(true)
                setShowBadge(true)
            }
            
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    fun sendDailyReminder(userName: String) {
        val title = "Good morning, $userName! 🌅"
        val message = "Ready for today's brain training challenge? Let's keep your mind sharp!"
        
        sendNotification(
            id = DAILY_REMINDER_ID,
            title = title,
            message = message,
            targetPage = "today"
        )
    }
    
    fun sendChallengeReminder(userName: String) {
        val title = "Challenge time, $userName! 🧠"
        val message = "You haven't completed today's challenge yet. A few minutes can make a big difference!"
        
        sendNotification(
            id = CHALLENGE_REMINDER_ID,
            title = title,
            message = message,
            targetPage = "today"
        )
    }
    
    fun sendProgressCelebration(userName: String, achievement: String) {
        val title = "Congratulations, $userName! 🎉"
        val message = "You've achieved: $achievement. Keep up the great work!"
        
        sendNotification(
            id = PROGRESS_CELEBRATION_ID,
            title = title,
            message = message,
            targetPage = "progress"
        )
    }
    
    fun sendMotivationalTip(tip: String) {
        val title = "Brain Training Tip 💡"
        val message = tip
        
        sendNotification(
            id = MOTIVATIONAL_TIP_ID,
            title = title,
            message = message,
            targetPage = "today"
        )
    }
    
    private fun sendNotification(id: Int, title: String, message: String, targetPage: String) {
        val intent = Intent(context, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("navigate_to", targetPage)
        }
        
        val pendingIntent = PendingIntent.getActivity(
            context,
            id,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notifications)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()
        
        try {
            NotificationManagerCompat.from(context).notify(id, notification)
        } catch (e: SecurityException) {
            // Permission not granted, ignore silently
        }
    }
    
    fun getMotivationalTips(): List<String> {
        return listOf(
            "Consistency beats intensity. Train your brain for just 10 minutes daily!",
            "Challenge yourself with new games to build different cognitive skills.",
            "Take breaks between training sessions to let your brain consolidate learning.",
            "Sleep well tonight - your brain processes and strengthens memories during sleep.",
            "Stay hydrated! Even mild dehydration can affect cognitive performance.",
            "Mix up your training routine to keep your brain engaged and growing.",
            "Celebrate small wins - every completed challenge is progress!",
            "Focus on accuracy first, then work on improving your speed.",
            "Regular brain training can help improve focus and memory in daily life.",
            "Remember: your brain is like a muscle - the more you use it, the stronger it gets!"
        )
    }
}
