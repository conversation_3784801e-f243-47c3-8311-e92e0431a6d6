// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivitySpatialRotationBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnMatch;

  @NonNull
  public final ImageButton btnMenu;

  @NonNull
  public final Button btnNoMatch;

  @NonNull
  public final ImageButton btnQuit;

  @NonNull
  public final TextView gameTitle;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final TextView levelText;

  @NonNull
  public final TextView originalShapeDisplay;

  @NonNull
  public final TextView rotatedShapeDisplay;

  @NonNull
  public final TextView rotationInfoText;

  @NonNull
  public final TextView roundText;

  @NonNull
  public final TextView shapeTypeText;

  @NonNull
  public final TextView trialText;

  private ActivitySpatialRotationBinding(@NonNull LinearLayout rootView, @NonNull Button btnMatch,
      @NonNull ImageButton btnMenu, @NonNull Button btnNoMatch, @NonNull ImageButton btnQuit,
      @NonNull TextView gameTitle, @NonNull TextView instructionText, @NonNull TextView levelText,
      @NonNull TextView originalShapeDisplay, @NonNull TextView rotatedShapeDisplay,
      @NonNull TextView rotationInfoText, @NonNull TextView roundText,
      @NonNull TextView shapeTypeText, @NonNull TextView trialText) {
    this.rootView = rootView;
    this.btnMatch = btnMatch;
    this.btnMenu = btnMenu;
    this.btnNoMatch = btnNoMatch;
    this.btnQuit = btnQuit;
    this.gameTitle = gameTitle;
    this.instructionText = instructionText;
    this.levelText = levelText;
    this.originalShapeDisplay = originalShapeDisplay;
    this.rotatedShapeDisplay = rotatedShapeDisplay;
    this.rotationInfoText = rotationInfoText;
    this.roundText = roundText;
    this.shapeTypeText = shapeTypeText;
    this.trialText = trialText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivitySpatialRotationBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivitySpatialRotationBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_spatial_rotation, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivitySpatialRotationBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_match;
      Button btnMatch = ViewBindings.findChildViewById(rootView, id);
      if (btnMatch == null) {
        break missingId;
      }

      id = R.id.btn_menu;
      ImageButton btnMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnMenu == null) {
        break missingId;
      }

      id = R.id.btn_no_match;
      Button btnNoMatch = ViewBindings.findChildViewById(rootView, id);
      if (btnNoMatch == null) {
        break missingId;
      }

      id = R.id.btn_quit;
      ImageButton btnQuit = ViewBindings.findChildViewById(rootView, id);
      if (btnQuit == null) {
        break missingId;
      }

      id = R.id.game_title;
      TextView gameTitle = ViewBindings.findChildViewById(rootView, id);
      if (gameTitle == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.level_text;
      TextView levelText = ViewBindings.findChildViewById(rootView, id);
      if (levelText == null) {
        break missingId;
      }

      id = R.id.original_shape_display;
      TextView originalShapeDisplay = ViewBindings.findChildViewById(rootView, id);
      if (originalShapeDisplay == null) {
        break missingId;
      }

      id = R.id.rotated_shape_display;
      TextView rotatedShapeDisplay = ViewBindings.findChildViewById(rootView, id);
      if (rotatedShapeDisplay == null) {
        break missingId;
      }

      id = R.id.rotation_info_text;
      TextView rotationInfoText = ViewBindings.findChildViewById(rootView, id);
      if (rotationInfoText == null) {
        break missingId;
      }

      id = R.id.round_text;
      TextView roundText = ViewBindings.findChildViewById(rootView, id);
      if (roundText == null) {
        break missingId;
      }

      id = R.id.shape_type_text;
      TextView shapeTypeText = ViewBindings.findChildViewById(rootView, id);
      if (shapeTypeText == null) {
        break missingId;
      }

      id = R.id.trial_text;
      TextView trialText = ViewBindings.findChildViewById(rootView, id);
      if (trialText == null) {
        break missingId;
      }

      return new ActivitySpatialRotationBinding((LinearLayout) rootView, btnMatch, btnMenu,
          btnNoMatch, btnQuit, gameTitle, instructionText, levelText, originalShapeDisplay,
          rotatedShapeDisplay, rotationInfoText, roundText, shapeTypeText, trialText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
