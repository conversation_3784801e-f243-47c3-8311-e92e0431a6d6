// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentProgressBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RecyclerView achievementsRecycler;

  @NonNull
  public final RecyclerView categoryAccuracyRecycler;

  @NonNull
  public final TextView currentStreak;

  @NonNull
  public final TextView gamesPlayedWeek;

  @NonNull
  public final TextView maxStreak;

  @NonNull
  public final TextView overallAccuracy;

  @NonNull
  public final ImageView profilePicture;

  @NonNull
  public final TextView totalScore;

  @NonNull
  public final TextView userLevel;

  @NonNull
  public final TextView username;

  private FragmentProgressBinding(@NonNull LinearLayout rootView,
      @NonNull RecyclerView achievementsRecycler, @NonNull RecyclerView categoryAccuracyRecycler,
      @NonNull TextView currentStreak, @NonNull TextView gamesPlayedWeek,
      @NonNull TextView maxStreak, @NonNull TextView overallAccuracy,
      @NonNull ImageView profilePicture, @NonNull TextView totalScore, @NonNull TextView userLevel,
      @NonNull TextView username) {
    this.rootView = rootView;
    this.achievementsRecycler = achievementsRecycler;
    this.categoryAccuracyRecycler = categoryAccuracyRecycler;
    this.currentStreak = currentStreak;
    this.gamesPlayedWeek = gamesPlayedWeek;
    this.maxStreak = maxStreak;
    this.overallAccuracy = overallAccuracy;
    this.profilePicture = profilePicture;
    this.totalScore = totalScore;
    this.userLevel = userLevel;
    this.username = username;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentProgressBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentProgressBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_progress, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentProgressBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.achievements_recycler;
      RecyclerView achievementsRecycler = ViewBindings.findChildViewById(rootView, id);
      if (achievementsRecycler == null) {
        break missingId;
      }

      id = R.id.category_accuracy_recycler;
      RecyclerView categoryAccuracyRecycler = ViewBindings.findChildViewById(rootView, id);
      if (categoryAccuracyRecycler == null) {
        break missingId;
      }

      id = R.id.current_streak;
      TextView currentStreak = ViewBindings.findChildViewById(rootView, id);
      if (currentStreak == null) {
        break missingId;
      }

      id = R.id.games_played_week;
      TextView gamesPlayedWeek = ViewBindings.findChildViewById(rootView, id);
      if (gamesPlayedWeek == null) {
        break missingId;
      }

      id = R.id.max_streak;
      TextView maxStreak = ViewBindings.findChildViewById(rootView, id);
      if (maxStreak == null) {
        break missingId;
      }

      id = R.id.overall_accuracy;
      TextView overallAccuracy = ViewBindings.findChildViewById(rootView, id);
      if (overallAccuracy == null) {
        break missingId;
      }

      id = R.id.profile_picture;
      ImageView profilePicture = ViewBindings.findChildViewById(rootView, id);
      if (profilePicture == null) {
        break missingId;
      }

      id = R.id.total_score;
      TextView totalScore = ViewBindings.findChildViewById(rootView, id);
      if (totalScore == null) {
        break missingId;
      }

      id = R.id.user_level;
      TextView userLevel = ViewBindings.findChildViewById(rootView, id);
      if (userLevel == null) {
        break missingId;
      }

      id = R.id.username;
      TextView username = ViewBindings.findChildViewById(rootView, id);
      if (username == null) {
        break missingId;
      }

      return new FragmentProgressBinding((LinearLayout) rootView, achievementsRecycler,
          categoryAccuracyRecycler, currentStreak, gamesPlayedWeek, maxStreak, overallAccuracy,
          profilePicture, totalScore, userLevel, username);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
