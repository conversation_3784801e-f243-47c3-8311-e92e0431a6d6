// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentTodayBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RecyclerView dailyChallengesRecycler;

  @NonNull
  public final MaterialButton startChallengeButton;

  private FragmentTodayBinding(@NonNull LinearLayout rootView,
      @NonNull RecyclerView dailyChallengesRecycler, @NonNull MaterialButton startChallengeButton) {
    this.rootView = rootView;
    this.dailyChallengesRecycler = dailyChallengesRecycler;
    this.startChallengeButton = startChallengeButton;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTodayBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTodayBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_today, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTodayBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.daily_challenges_recycler;
      RecyclerView dailyChallengesRecycler = ViewBindings.findChildViewById(rootView, id);
      if (dailyChallengesRecycler == null) {
        break missingId;
      }

      id = R.id.start_challenge_button;
      MaterialButton startChallengeButton = ViewBindings.findChildViewById(rootView, id);
      if (startChallengeButton == null) {
        break missingId;
      }

      return new FragmentTodayBinding((LinearLayout) rootView, dailyChallengesRecycler,
          startChallengeButton);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
