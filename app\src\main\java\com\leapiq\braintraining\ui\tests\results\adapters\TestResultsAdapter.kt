package com.leapiq.braintraining.ui.tests.results.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ItemTestResultBinding
import com.leapiq.braintraining.data.model.TestResult
import com.leapiq.braintraining.data.model.TestType
import java.text.SimpleDateFormat
import java.util.*

/**
 * Adapter for displaying individual test results
 */
class TestResultsAdapter(
    private val onItemClick: (TestResult) -> Unit
) : ListAdapter<TestResult, TestResultsAdapter.TestResultViewHolder>(DiffCallback()) {
    
    private val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
    private val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): TestResultViewHolder {
        val binding = ItemTestResultBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return TestResultViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: TestResultViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class TestResultViewHolder(
        private val binding: ItemTestResultBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(result: TestResult) {
            binding.apply {
                // Date and time
                dateText.text = dateFormat.format(result.completedAt)
                timeText.text = timeFormat.format(result.completedAt)
                
                // Score
                scoreText.text = "${result.score}%"
                
                // Set score color and icon
                val (scoreColor, scoreIconText) = when {
                    result.score >= 90 -> R.color.success_green to "🏆"
                    result.score >= 80 -> R.color.success_green to "⭐"
                    result.score >= 70 -> R.color.warning_orange to "👍"
                    result.score >= 60 -> R.color.warning_orange to "👌"
                    else -> R.color.error_red to "📈"
                }

                scoreText.setTextColor(ContextCompat.getColor(root.context, scoreColor))
                scoreIcon.text = scoreIconText
                
                // Test-specific metrics
                if (result.testType == TestType.COGNITIVE) {
                    // Show accuracy and response time for cognitive tests
                    accuracyText.text = "Accuracy: ${result.accuracyPercentage}%"
                    responseTimeText.text = "Avg Response: ${result.averageResponseTime / 1000}s"
                    
                    accuracyText.visibility = android.view.View.VISIBLE
                    responseTimeText.visibility = android.view.View.VISIBLE
                } else {
                    // Hide cognitive-specific metrics for personality tests
                    accuracyText.visibility = android.view.View.GONE
                    responseTimeText.visibility = android.view.View.GONE
                }
                
                // Total time
                val totalSeconds = result.totalTimeMs / 1000
                val minutes = totalSeconds / 60
                val seconds = totalSeconds % 60
                
                totalTimeText.text = if (minutes > 0) {
                    "Total: ${minutes}m ${seconds}s"
                } else {
                    "Total: ${seconds}s"
                }
                
                // Questions count
                questionsText.text = "${result.questions.size} questions"
                
                // Show insights if available
                if (result.insights.isNotEmpty()) {
                    insightText.text = result.insights.first()
                    insightText.visibility = android.view.View.VISIBLE
                } else {
                    insightText.visibility = android.view.View.GONE
                }
                
                // Set click listener
                root.setOnClickListener {
                    onItemClick(result)
                }
            }
        }
    }
    
    private class DiffCallback : DiffUtil.ItemCallback<TestResult>() {
        override fun areItemsTheSame(oldItem: TestResult, newItem: TestResult): Boolean {
            return oldItem.completedAt == newItem.completedAt && oldItem.testId == newItem.testId
        }
        
        override fun areContentsTheSame(oldItem: TestResult, newItem: TestResult): Boolean {
            return oldItem == newItem
        }
    }
}
