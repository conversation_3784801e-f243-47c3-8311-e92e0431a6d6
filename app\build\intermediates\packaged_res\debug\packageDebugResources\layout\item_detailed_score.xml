<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/categoryText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Category"
            android:textSize="16sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/scoreText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="85%"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <ProgressBar
        android:id="@+id/scoreProgress"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="8dp"
        android:layout_marginTop="8dp"
        android:max="100"
        android:progress="85" />

</LinearLayout>
