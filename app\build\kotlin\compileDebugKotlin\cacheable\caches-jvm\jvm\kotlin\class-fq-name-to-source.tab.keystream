%com.leapiq.braintraining.MainActivity4com.leapiq.braintraining.analysis.TestAnalysisEngine2com.leapiq.braintraining.analysis.DetailedAnalysis4com.leapiq.braintraining.analysis.OverallPerformance5com.leapiq.braintraining.analysis.PerformanceCategory0com.leapiq.braintraining.analysis.TimeEfficiency5com.leapiq.braintraining.analysis.TimeEfficiencyLevel2com.leapiq.braintraining.analysis.ConsistencyScore2com.leapiq.braintraining.analysis.ConsistencyLevel2com.leapiq.braintraining.analysis.CognitiveProfile4com.leapiq.braintraining.analysis.PersonalityProfile=com.leapiq.braintraining.analysis.StrengthsWeaknessesAnalysis*com.leapiq.braintraining.analysis.Strength0com.leapiq.braintraining.analysis.StrengthImpact*com.leapiq.braintraining.analysis.Weakness2com.leapiq.braintraining.analysis.WeaknessPriority6com.leapiq.braintraining.analysis.ImprovementPotential1com.leapiq.braintraining.analysis.ResponsePattern5com.leapiq.braintraining.analysis.ResponsePatternType=com.leapiq.braintraining.analysis.ResponsePatternSignificance0com.leapiq.braintraining.analysis.OverallBalance;com.leapiq.braintraining.analysis.ImprovementRecommendation8com.leapiq.braintraining.analysis.RecommendationCategory8com.leapiq.braintraining.analysis.RecommendationPriority3com.leapiq.braintraining.analysis.ImprovementImpact;com.leapiq.braintraining.analysis.ComparativeAnalysisResult4com.leapiq.braintraining.analysis.PersonalComparison6com.leapiq.braintraining.analysis.PopulationComparison2com.leapiq.braintraining.analysis.ImprovementTrend0com.leapiq.braintraining.analysis.TrendDirection*com.leapiq.braintraining.analysis.NextStep2com.leapiq.braintraining.analysis.NextStepCategory2com.leapiq.braintraining.analysis.NextStepPriority1com.leapiq.braintraining.data.GameProgressManager;com.leapiq.braintraining.data.GameProgressManager.Companion3com.leapiq.braintraining.data.TestDataBackupManager=com.leapiq.braintraining.data.TestDataBackupManager.Companion(com.leapiq.braintraining.data.BackupData,com.leapiq.braintraining.data.BackupMetadata(com.leapiq.braintraining.data.BackupInfo*com.leapiq.braintraining.data.BackupResult2com.leapiq.braintraining.data.BackupResult.Success0com.leapiq.braintraining.data.BackupResult.Error+com.leapiq.braintraining.data.RestoreResult3com.leapiq.braintraining.data.RestoreResult.Success1com.leapiq.braintraining.data.RestoreResult.Error*com.leapiq.braintraining.data.ExportResult2com.leapiq.braintraining.data.ExportResult.Success0com.leapiq.braintraining.data.ExportResult.Error1com.leapiq.braintraining.data.TestProgressManager;com.leapiq.braintraining.data.TestProgressManager.Companion.com.leapiq.braintraining.data.PerformanceTrend,com.leapiq.braintraining.data.TrendDataPoint,com.leapiq.braintraining.data.TrendDirection,com.leapiq.braintraining.data.TestStatistics3com.leapiq.braintraining.data.TestResultsRepository=com.leapiq.braintraining.data.TestResultsRepository.Companion/com.leapiq.braintraining.data.TestDashboardData*com.leapiq.braintraining.data.ActivityItem*com.leapiq.braintraining.data.ActivityType*com.leapiq.braintraining.data.StrengthArea-com.leapiq.braintraining.data.ImprovementArea-com.leapiq.braintraining.data.OverallProgress1com.leapiq.braintraining.data.ComparativeAnalysis<com.leapiq.braintraining.data.CognitivePersonalityComparison)com.leapiq.braintraining.data.TestRanking-com.leapiq.braintraining.data.TestCorrelation5com.leapiq.braintraining.data.CorrelationSignificance1com.leapiq.braintraining.data.PersonalizedInsight)com.leapiq.braintraining.data.InsightType-com.leapiq.braintraining.data.InsightPriority/com.leapiq.braintraining.data.model.Achievement3com.leapiq.braintraining.data.model.AchievementType4com.leapiq.braintraining.data.model.CategoryAccuracy2com.leapiq.braintraining.data.model.DailyChallenge(com.leapiq.braintraining.data.model.Game0com.leapiq.braintraining.data.model.GameCategory,com.leapiq.braintraining.data.model.GameType/com.leapiq.braintraining.data.model.RoundResult/com.leapiq.braintraining.data.model.LevelResult0com.leapiq.braintraining.data.model.GameProgress0com.leapiq.braintraining.data.model.UserProgress(com.leapiq.braintraining.data.model.Test0com.leapiq.braintraining.data.model.TestCategory6com.leapiq.braintraining.data.model.TestQuestionResult.com.leapiq.braintraining.data.model.TestResult0com.leapiq.braintraining.data.model.TestProgress,com.leapiq.braintraining.data.model.TestType2com.leapiq.braintraining.ui.games.AnagramsActivity2com.leapiq.braintraining.ui.games.BaseGameActivity<com.leapiq.braintraining.ui.games.BaseGameActivity.Companion0com.leapiq.braintraining.ui.games.GameDifficulty6com.leapiq.braintraining.ui.games.CardMatchingActivityBcom.leapiq.braintraining.ui.games.CardMatchingActivity.LevelConfig4com.leapiq.braintraining.ui.games.EstimationActivityCcom.leapiq.braintraining.ui.games.EstimationActivity.EstimationType8com.leapiq.braintraining.ui.games.FocusChallengeActivity.com.leapiq.braintraining.ui.games.FocusMetrics4com.leapiq.braintraining.ui.games.GameResultActivity>com.leapiq.braintraining.ui.games.GameResultActivity.Companion/com.leapiq.braintraining.ui.games.GamesFragment6com.leapiq.braintraining.ui.games.LogicPuzzlesActivity:com.leapiq.braintraining.ui.games.LogicalReasoningActivityEcom.leapiq.braintraining.ui.games.LogicalReasoningActivity.PuzzleType:com.leapiq.braintraining.ui.games.MentalArithmeticActivityDcom.leapiq.braintraining.ui.games.MentalArithmeticActivity.Operation6com.leapiq.braintraining.ui.games.NumberMemoryActivity9com.leapiq.braintraining.ui.games.NumberSequencesActivityEcom.leapiq.braintraining.ui.games.NumberSequencesActivity.PatternType;com.leapiq.braintraining.ui.games.PatternCompletionActivityGcom.leapiq.braintraining.ui.games.PatternCompletionActivity.PatternType7com.leapiq.braintraining.ui.games.PatternMemoryActivity6com.leapiq.braintraining.ui.games.ReactionTimeActivity8com.leapiq.braintraining.ui.games.SequenceRecallActivity9com.leapiq.braintraining.ui.games.SpatialRotationActivity?com.leapiq.braintraining.ui.games.SpatialRotationActivity.ShapeBcom.leapiq.braintraining.ui.games.SpatialRotationActivity.Rotation3com.leapiq.braintraining.ui.games.SpeedMathActivity=com.leapiq.braintraining.ui.games.SpeedMathActivity.Operation4com.leapiq.braintraining.ui.games.StroopTestActivity6com.leapiq.braintraining.ui.games.TowerOfHanoiActivity2com.leapiq.braintraining.ui.games.TubeSortActivity6com.leapiq.braintraining.ui.games.VisualSearchActivityGcom.leapiq.braintraining.ui.games.VisualSearchActivity.SearchDifficulty4com.leapiq.braintraining.ui.games.VocabularyActivityCcom.leapiq.braintraining.ui.games.VocabularyActivity.VocabularyMode9com.leapiq.braintraining.ui.games.WordAssociationActivity4com.leapiq.braintraining.ui.games.WordSearchActivity6com.leapiq.braintraining.ui.games.adapter.CluesAdapterEcom.leapiq.braintraining.ui.games.adapter.CluesAdapter.ClueViewHolder5com.leapiq.braintraining.ui.games.adapter.GameAdapterDcom.leapiq.braintraining.ui.games.adapter.GameAdapter.GameViewHolder?com.leapiq.braintraining.ui.games.adapter.GameAdapter.Companion;com.leapiq.braintraining.ui.games.adapter.HanoiTowerAdapterKcom.leapiq.braintraining.ui.games.adapter.HanoiTowerAdapter.TowerViewHolder:com.leapiq.braintraining.ui.games.adapter.LogicGridAdapterLcom.leapiq.braintraining.ui.games.adapter.LogicGridAdapter.GridRowViewHolder<com.leapiq.braintraining.ui.games.adapter.PatternGridAdapterRcom.leapiq.braintraining.ui.games.adapter.PatternGridAdapter.PatternCellViewHolder5com.leapiq.braintraining.ui.games.adapter.TubeAdapterDcom.leapiq.braintraining.ui.games.adapter.TubeAdapter.TubeViewHolder=com.leapiq.braintraining.ui.games.adapter.VisualSearchAdapterRcom.leapiq.braintraining.ui.games.adapter.VisualSearchAdapter.SearchItemViewHolder9com.leapiq.braintraining.ui.games.adapter.WordListAdapterHcom.leapiq.braintraining.ui.games.adapter.WordListAdapter.WordViewHolder?com.leapiq.braintraining.ui.games.adapter.WordSearchGridAdapterRcom.leapiq.braintraining.ui.games.adapter.WordSearchGridAdapter.GridCellViewHolder3com.leapiq.braintraining.ui.games.memory.MemoryCard:com.leapiq.braintraining.ui.games.memory.MemoryCardAdapterIcom.leapiq.braintraining.ui.games.memory.MemoryCardAdapter.CardViewHolder,com.leapiq.braintraining.ui.games.model.Ball2com.leapiq.braintraining.ui.games.model.Ball.Color1com.leapiq.braintraining.ui.games.model.HanoiDisk;com.leapiq.braintraining.ui.games.model.HanoiDisk.DiskColor2com.leapiq.braintraining.ui.games.model.HanoiTower3com.leapiq.braintraining.ui.games.model.LogicPuzzle0com.leapiq.braintraining.ui.games.model.Category,com.leapiq.braintraining.ui.games.model.Clue0com.leapiq.braintraining.ui.games.model.ClueType1com.leapiq.braintraining.ui.games.model.CellState4com.leapiq.braintraining.ui.games.model.GridPosition1com.leapiq.braintraining.ui.games.model.GridState2com.leapiq.braintraining.ui.games.model.Constraint6com.leapiq.braintraining.ui.games.model.ConstraintType,com.leapiq.braintraining.ui.games.model.Hint0com.leapiq.braintraining.ui.games.model.HintType2com.leapiq.braintraining.ui.games.model.SearchItem,com.leapiq.braintraining.ui.games.model.Tube6com.leapiq.braintraining.ui.games.model.WordSearchGrid6com.leapiq.braintraining.ui.games.model.WordSearchWord3com.leapiq.braintraining.ui.profile.ProfileFragment5com.leapiq.braintraining.ui.progress.ProgressFragment?com.leapiq.braintraining.ui.progress.adapter.AchievementAdapterUcom.leapiq.braintraining.ui.progress.adapter.AchievementAdapter.AchievementViewHolderIcom.leapiq.braintraining.ui.progress.adapter.AchievementAdapter.CompanionDcom.leapiq.braintraining.ui.progress.adapter.CategoryAccuracyAdapterWcom.leapiq.braintraining.ui.progress.adapter.CategoryAccuracyAdapter.AccuracyViewHolderNcom.leapiq.braintraining.ui.progress.adapter.CategoryAccuracyAdapter.Companion2com.leapiq.braintraining.ui.tests.BaseTestActivity<<EMAIL>;com.leapiq.braintraining.ui.tests.LearningStyleInfoActivity4com.leapiq.braintraining.ui.tests.ProblemSolvingType8com.leapiq.braintraining.ui.tests.ProblemSolvingQuestion6com.leapiq.braintraining.ui.tests.ProblemSolvingOption.com.leapiq.braintraining.ui.tests.QuestionType=com.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivityAcom.leapiq.braintraining.ui.tests.ProblemSolvingStyleInfoActivity4com.leapiq.braintraining.ui.tests.StressResponseType8com.leapiq.braintraining.ui.tests.StressResponseQuestion6com.leapiq.braintraining.ui.tests.StressResponseOption8com.leapiq.braintraining.ui.tests.StressResponseActivity<com.leapiq.braintraining.ui.tests.StressResponseInfoActivity/com.leapiq.braintraining.ui.tests.TestsFragment5com.leapiq.braintraining.ui.tests.adapter.TestAdapterDcom.leapiq.braintraining.ui.tests.adapter.TestAdapter.TestViewHolder?com.leapiq.braintraining.ui.tests.adapter.TestAdapter.CompanionGcom.leapiq.braintraining.ui.tests.adapters.LearningStyleQuestionAdapterXcom.leapiq.braintraining.ui.tests.adapters.LearningStyleQuestionAdapter.OptionViewHolder<com.leapiq.braintraining.ui.tests.adapters.MemoryGridAdapterKcom.leapiq.braintraining.ui.tests.adapters.MemoryGridAdapter.GridViewHolderHcom.leapiq.braintraining.ui.tests.adapters.ProblemSolvingQuestionAdapterYcom.leapiq.braintraining.ui.tests.adapters.ProblemSolvingQuestionAdapter.OptionViewHolderHcom.leapiq.braintraining.ui.tests.adapters.StressResponseQuestionAdapterYcom.leapiq.braintraining.ui.tests.adapters.StressResponseQuestionAdapter.OptionViewHolder<com.leapiq.braintraining.ui.tests.results.AllResultsActivity8com.leapiq.braintraining.ui.tests.results.TestResultItem;com.leapiq.braintraining.ui.tests.results.AnalyticsActivity;com.leapiq.braintraining.ui.tests.results.MyResultsActivity:com.leapiq.braintraining.ui.tests.results.TestProgressItem>com.leapiq.braintraining.ui.tests.results.TestProgressActivityHcom.leapiq.braintraining.ui.tests.results.TestProgressActivity.Companion=com.leapiq.braintraining.ui.tests.results.TestResultsActivityGcom.leapiq.braintraining.ui.tests.results.TestResultsActivity.CompanionDcom.leapiq.braintraining.ui.tests.results.adapters.AllResultsAdapterYcom.leapiq.braintraining.ui.tests.results.adapters.AllResultsAdapter.AllResultsViewHolderQcom.leapiq.braintraining.ui.tests.results.adapters.AllResultsAdapter.DiffCallbackHcom.leapiq.braintraining.ui.tests.results.adapters.DetailedScoresAdapter`com.leapiq.braintraining.ui.tests.results.adapters.DetailedScoresAdapter.DetailedScoreViewHolderHcom.leapiq.braintraining.ui.tests.results.adapters.RecentActivityAdapteracom.leapiq.braintraining.ui.tests.results.adapters.RecentActivityAdapter.RecentActivityViewHolderUcom.leapiq.braintraining.ui.tests.results.adapters.RecentActivityAdapter.DiffCallbackFcom.leapiq.braintraining.ui.tests.results.adapters.TestProgressAdapter]com.leapiq.braintraining.ui.tests.results.adapters.TestProgressAdapter.TestProgressViewHolderScom.leapiq.braintraining.ui.tests.results.adapters.TestProgressAdapter.DiffCallbackEcom.leapiq.braintraining.ui.tests.results.adapters.TestResultsAdapterZcom.leapiq.braintraining.ui.tests.results.adapters.TestResultsAdapter.TestResultViewHolderRcom.leapiq.braintraining.ui.tests.results.adapters.TestResultsAdapter.DiffCallback/com.leapiq.braintraining.ui.today.TodayFragment?com.leapiq.braintraining.ui.today.adapter.DailyChallengeAdapterScom.leapiq.braintraining.ui.today.adapter.DailyChallengeAdapter.ChallengeViewHolderIcom.leapiq.braintraining.ui.today.adapter.DailyChallengeAdapter.CompanionDcom.leapiq.braintraining.databinding.ItemStressResponseOptionBinding=com.leapiq.braintraining.databinding.ActivitySpeedMathBinding=com.leapiq.braintraining.databinding.ActivityAnalyticsBinding?<EMAIL>><EMAIL><<EMAIL>>com.leapiq.braintraining.databinding.ActivityAllResultsBinding8com.leapiq.braintraining.databinding.ItemGameCardBinding>com.leapiq.braintraining.databinding.ItemDailyChallengeBinding=com.leapiq.braintraining.databinding.ItemDetailedScoreBindingDcom.leapiq.braintraining.databinding.ActivityMentalArithmeticBinding;<EMAIL>;com.leapiq.braintraining.databinding.FragmentProfileBinding>com.leapiq.braintraining.databinding.ActivityGameResultBindingCcom.leapiq.braintraining.databinding.ItemLearningStyleOptionBinding8com.leapiq.braintraining.databinding.ItemTestCardBinding>com.leapiq.braintraining.databinding.ActivityWordSearchBinding<com.leapiq.braintraining.databinding.ActivityTestInfoBindingBcom.leapiq.braintraining.databinding.ActivityFocusChallengeBindingDcom.leapiq.braintraining.databinding.ItemProblemSolvingOptionBinding>com.leapiq.braintraining.databinding.ItemRecentActivityBinding=com.leapiq.braintraining.databinding.ActivityMyResultsBindingCcom.leapiq.braintraining.databinding.ActivityNumberSequencesBindingAcom.leapiq.braintraining.databinding.ActivityPatternMemoryBindingEcom.leapiq.braintraining.databinding.ActivityPatternCompletionBindingCcom.leapiq.braintraining.databinding.ActivitySpatialRotationBinding><EMAIL>>com.leapiq.braintraining.databinding.ActivityEstimationBinding9com.leapiq.braintraining.databinding.FragmentTodayBinding<com.leapiq.braintraining.databinding.ActivityAnagramsBinding:<EMAIL>@<EMAIL>:com.leapiq.braintraining.databinding.ItemTestResultBinding8com.leapiq.braintraining.databinding.ActivityMainBindingBcom.leapiq.braintraining.databinding.ActivitySequenceRecallBinding:com.leapiq.braintraining.databinding.ItemAllResultsBindingDcom.leapiq.braintraining.databinding.ActivityLogicalReasoningBinding<com.leapiq.braintraining.databinding.ItemTestProgressBinding<com.leapiq.braintraining.databinding.ActivityTubeSortBinding8com.leapiq.braintraining.databinding.ItemKeyPointBinding'com.leapiq.braintraining.SplashActivity:com.leapiq.braintraining.databinding.ActivitySplashBinding*com.leapiq.braintraining.NameInputActivity7com.leapiq.braintraining.NotificationPermissionActivity1com.leapiq.braintraining.SplashActivity.Companion(com.leapiq.braintraining.WelcomeActivity<com.leapiq.braintraining.WelcomeActivity.WelcomePagerAdapter;com.leapiq.braintraining.notifications.NotificationReceiver<com.leapiq.braintraining.notifications.NotificationSchedulerFcom.leapiq.braintraining.notifications.NotificationScheduler.Companion:com.leapiq.braintraining.notifications.NotificationServiceDcom.leapiq.braintraining.notifications.NotificationService.Companion7com.leapiq.braintraining.ui.welcome.WelcomePageFragmentAcom.leapiq.braintraining.ui.welcome.WelcomePageFragment.Companion;com.leapiq.braintraining.databinding.ActivityWelcomeBinding=com.leapiq.braintraining.databinding.ActivityNameInputBinding?com.leapiq.braintraining.databinding.FragmentWelcomePageBindingJcom.leapiq.braintraining.databinding.ActivityNotificationPermissionBinding                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   