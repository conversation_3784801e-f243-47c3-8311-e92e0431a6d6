package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivitySpatialRotationBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.math.abs
import kotlin.random.Random

/**
 * Spatial Rotation Game
 * Players determine if rotated 3D objects match the original
 * Tests spatial visualization and mental manipulation skills
 */
class SpatialRotationActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySpatialRotationBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "logic_3"

    // Spatial rotation specific
    private var currentShape = Shape.CUBE
    private var originalRotation = Rotation(0, 0, 0)
    private var testRotation = Rotation(0, 0, 0)
    private var isMatch = false
    private var problemStartTime = 0L
    private var currentTrial = 0
    private var trialsPerRound = 10
    private var reactionTimes = mutableListOf<Long>()

    // 3D Shapes
    private enum class Shape {
        CUBE, L_SHAPE, T_SHAPE, PYRAMID, STAIRS, CROSS, ZIGZAG, COMPLEX
    }

    // Rotation angles
    private data class Rotation(val x: Int, val y: Int, val z: Int)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySpatialRotationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.spatial_rotation)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Do these shapes match when rotated?"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup answer buttons
            btnMatch.setOnClickListener { selectAnswer(true) }
            btnNoMatch.setOnClickListener { selectAnswer(false) }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentTrial = 0
        reactionTimes.clear()
        trialsPerRound = getTrialsPerRound(currentLevel)
        
        binding.trialText.text = "Shape 1/$trialsPerRound"
        
        generateNextRotation()
    }

    private fun getTrialsPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 10      // 10 rotations per round
            in 6..10 -> 12     // 12 rotations per round
            in 11..15 -> 15    // 15 rotations per round
            in 16..20 -> 18    // 18 rotations per round
            else -> 20         // 20 rotations per round
        }
    }

    private fun generateNextRotation() {
        if (currentTrial >= trialsPerRound) {
            roundComplete()
            return
        }

        currentTrial++
        binding.trialText.text = "Shape $currentTrial/$trialsPerRound"
        
        val availableShapes = getAvailableShapes(currentLevel)
        currentShape = availableShapes.random()
        
        generateRotationProblem()
        problemStartTime = System.currentTimeMillis()
    }

    private fun getAvailableShapes(level: Int): List<Shape> {
        return when (level) {
            in 1..3 -> listOf(Shape.CUBE, Shape.L_SHAPE)
            in 4..6 -> listOf(Shape.CUBE, Shape.L_SHAPE, Shape.T_SHAPE)
            in 7..10 -> listOf(Shape.CUBE, Shape.L_SHAPE, Shape.T_SHAPE, Shape.PYRAMID)
            in 11..15 -> listOf(Shape.CUBE, Shape.L_SHAPE, Shape.T_SHAPE, Shape.PYRAMID, Shape.STAIRS)
            in 16..20 -> listOf(Shape.CUBE, Shape.L_SHAPE, Shape.T_SHAPE, Shape.PYRAMID, Shape.STAIRS, Shape.CROSS)
            else -> Shape.values().toList()
        }
    }

    private fun generateRotationProblem() {
        // Generate original rotation (usually 0,0,0 for reference)
        originalRotation = Rotation(0, 0, 0)
        
        // Generate test rotation
        val difficulty = getDifficulty(currentLevel)
        val rotationAngles = getRotationAngles(difficulty)
        
        // Decide if it's a match or not (50/50 chance)
        isMatch = Random.nextBoolean()
        
        if (isMatch) {
            // Generate a rotation that results in the same shape
            testRotation = generateMatchingRotation(rotationAngles)
        } else {
            // Generate a rotation that results in a different orientation
            testRotation = generateNonMatchingRotation(rotationAngles)
        }
        
        displayShapes()
    }

    private fun getDifficulty(level: Int): Int {
        return when (level) {
            in 1..5 -> 1       // Simple rotations
            in 6..10 -> 2      // Medium rotations
            in 11..15 -> 3     // Complex rotations
            in 16..20 -> 4     // Very complex rotations
            else -> 5          // Expert rotations
        }
    }

    private fun getRotationAngles(difficulty: Int): List<Int> {
        return when (difficulty) {
            1 -> listOf(0, 90, 180, 270)           // 90-degree increments
            2 -> listOf(0, 45, 90, 135, 180, 225, 270, 315) // 45-degree increments
            3 -> listOf(0, 30, 60, 90, 120, 150, 180, 210, 240, 270, 300, 330) // 30-degree increments
            4 -> (0..359 step 15).toList()         // 15-degree increments
            else -> (0..359 step 10).toList()      // 10-degree increments
        }
    }

    private fun generateMatchingRotation(angles: List<Int>): Rotation {
        // For matching rotations, we need rotations that result in the same final orientation
        // This depends on the shape's symmetry
        val symmetryAngles = getShapeSymmetryAngles(currentShape)
        val validAngles = angles.filter { it in symmetryAngles }
        
        return if (validAngles.isNotEmpty()) {
            val angle = validAngles.random()
            Rotation(angle, 0, 0) // Simplify to single-axis rotation for now
        } else {
            Rotation(0, 0, 0) // Identity rotation
        }
    }

    private fun generateNonMatchingRotation(angles: List<Int>): Rotation {
        // For non-matching rotations, avoid symmetry angles
        val symmetryAngles = getShapeSymmetryAngles(currentShape)
        val validAngles = angles.filter { it !in symmetryAngles }
        
        return if (validAngles.isNotEmpty()) {
            val angle = validAngles.random()
            Rotation(angle, 0, 0) // Simplify to single-axis rotation for now
        } else {
            Rotation(45, 0, 0) // Default non-symmetric rotation
        }
    }

    private fun getShapeSymmetryAngles(shape: Shape): List<Int> {
        return when (shape) {
            Shape.CUBE -> listOf(0, 90, 180, 270) // 4-fold rotational symmetry
            Shape.L_SHAPE -> listOf(0) // No rotational symmetry
            Shape.T_SHAPE -> listOf(0) // No rotational symmetry
            Shape.PYRAMID -> listOf(0, 90, 180, 270) // 4-fold symmetry (square base)
            Shape.STAIRS -> listOf(0) // No rotational symmetry
            Shape.CROSS -> listOf(0, 90, 180, 270) // 4-fold symmetry
            Shape.ZIGZAG -> listOf(0, 180) // 2-fold symmetry
            Shape.COMPLEX -> listOf(0) // No symmetry
        }
    }

    private fun displayShapes() {
        binding.apply {
            shapeTypeText.text = getShapeDisplayName(currentShape)
            
            // Display original shape
            originalShapeDisplay.text = generateShapeVisualization(currentShape, originalRotation)
            
            // Display rotated shape
            rotatedShapeDisplay.text = generateShapeVisualization(currentShape, testRotation)
            
            // Update rotation info
            rotationInfoText.text = "Rotation: ${testRotation.x}° X-axis"
        }
    }

    private fun getShapeDisplayName(shape: Shape): String {
        return when (shape) {
            Shape.CUBE -> "CUBE"
            Shape.L_SHAPE -> "L-SHAPE"
            Shape.T_SHAPE -> "T-SHAPE"
            Shape.PYRAMID -> "PYRAMID"
            Shape.STAIRS -> "STAIRS"
            Shape.CROSS -> "CROSS"
            Shape.ZIGZAG -> "ZIGZAG"
            Shape.COMPLEX -> "COMPLEX"
        }
    }

    private fun generateShapeVisualization(shape: Shape, rotation: Rotation): String {
        // Generate ASCII art representation of 3D shapes
        // This is a simplified 2D projection for display purposes
        return when (shape) {
            Shape.CUBE -> generateCubeVisualization(rotation)
            Shape.L_SHAPE -> generateLShapeVisualization(rotation)
            Shape.T_SHAPE -> generateTShapeVisualization(rotation)
            Shape.PYRAMID -> generatePyramidVisualization(rotation)
            Shape.STAIRS -> generateStairsVisualization(rotation)
            Shape.CROSS -> generateCrossVisualization(rotation)
            Shape.ZIGZAG -> generateZigzagVisualization(rotation)
            Shape.COMPLEX -> generateComplexVisualization(rotation)
        }
    }

    private fun generateCubeVisualization(rotation: Rotation): String {
        return when (rotation.x % 360) {
            0 -> "┌─────┐\n│     │\n│     │\n│     │\n└─────┘"
            90 -> "┌─┐\n│ │\n│ │\n│ │\n│ │\n└─┘"
            180 -> "┌─────┐\n│     │\n│     │\n│     │\n└─────┘"
            270 -> "┌─┐\n│ │\n│ │\n│ │\n│ │\n└─┘"
            else -> "┌───┐\n│   │\n│   │\n└───┘"
        }
    }

    private fun generateLShapeVisualization(rotation: Rotation): String {
        return when (rotation.x % 360) {
            0 -> "██\n██\n██\n██████"
            90 -> "████████\n      ██"
            180 -> "██████\n    ██\n    ██\n    ██"
            270 -> "██\n████████"
            else -> "██\n██\n██████"
        }
    }

    private fun generateTShapeVisualization(rotation: Rotation): String {
        return when (rotation.x % 360) {
            0 -> "████████\n   ██\n   ██\n   ██"
            90 -> "██\n██\n██████\n██\n██"
            180 -> "   ██\n   ██\n   ██\n████████"
            270 -> "    ██\n    ██\n██████\n    ██\n    ██"
            else -> "████████\n   ██\n   ██"
        }
    }

    private fun generatePyramidVisualization(rotation: Rotation): String {
        return when (rotation.x % 360) {
            0 -> "    ▲\n   ███\n  █████\n ███████\n█████████"
            90 -> "█\n██\n███\n████\n███\n██\n█"
            180 -> "█████████\n ███████\n  █████\n   ███\n    ▲"
            270 -> "    █\n   ██\n  ███\n ████\n  ███\n   ██\n    █"
            else -> "   ▲\n  ███\n █████\n███████"
        }
    }

    private fun generateStairsVisualization(rotation: Rotation): String {
        return when (rotation.x % 360) {
            0 -> "██\n████\n██████\n████████"
            90 -> "████████\n██████\n████\n██"
            180 -> "████████\n  ██████\n    ████\n      ██"
            270 -> "      ██\n    ████\n  ██████\n████████"
            else -> "██\n████\n██████"
        }
    }

    private fun generateCrossVisualization(rotation: Rotation): String {
        return when (rotation.x % 360) {
            0, 180 -> "   ██\n   ██\n███████\n███████\n   ██\n   ██"
            90, 270 -> "      \n██████\n██████\n██████\n██████"
            else -> "  ██\n██████\n██████\n  ██"
        }
    }

    private fun generateZigzagVisualization(rotation: Rotation): String {
        return when (rotation.x % 360) {
            0 -> "██████\n    ██████\n        ██████"
            90 -> "██\n██\n  ██\n  ██\n    ██\n    ██"
            180 -> "        ██████\n    ██████\n██████"
            270 -> "    ██\n    ██\n  ██\n  ██\n██\n██"
            else -> "██████\n  ██████\n    ██████"
        }
    }

    private fun generateComplexVisualization(rotation: Rotation): String {
        return when (rotation.x % 360) {
            0 -> "██  ██\n██████\n  ██\n██████\n██  ██"
            90 -> "█ █ █\n█████\n █ █ \n█████\n █ █ "
            180 -> "██  ██\n██████\n  ██\n██████\n██  ██"
            270 -> " █ █ \n█████\n █ █ \n█████\n█ █ █"
            else -> "██ ██\n█████\n ██ \n█████\n██ ██"
        }
    }

    private fun selectAnswer(userAnswer: Boolean) {
        val reactionTime = System.currentTimeMillis() - problemStartTime
        
        totalAttempts++
        val isCorrect = userAnswer == isMatch
        
        if (isCorrect) {
            totalCorrect++
            reactionTimes.add(reactionTime)
        }
        
        showFeedback(userAnswer, isCorrect, reactionTime)
        
        // Continue to next rotation after delay
        Handler(Looper.getMainLooper()).postDelayed({
            generateNextRotation()
        }, 2000)
    }

    private fun showFeedback(userAnswer: Boolean, isCorrect: Boolean, reactionTime: Long) {
        val correctText = if (isMatch) "MATCH" else "NO MATCH"
        val userText = if (userAnswer) "MATCH" else "NO MATCH"
        
        val feedback = if (isCorrect) {
            "Correct! $correctText (${reactionTime}ms)"
        } else {
            "Wrong! You said $userText, correct answer: $correctText"
        }
        
        binding.instructionText.text = feedback
        binding.instructionText.setTextColor(
            ContextCompat.getColor(this, if (isCorrect) R.color.stroop_green else R.color.stroop_red)
        )
        
        // Reset color after delay
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.text = "Do these shapes match when rotated?"
            binding.instructionText.setTextColor(
                ContextCompat.getColor(this, R.color.text_secondary)
            )
        }, 2000)
    }

    private fun roundComplete() {
        val avgReactionTime = if (reactionTimes.isNotEmpty()) {
            reactionTimes.average().toLong()
        } else 0L
        
        val roundAccuracy = if (trialsPerRound > 0) {
            (totalCorrect.toDouble() / trialsPerRound)
        } else 1.0
        
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Accuracy: ${(roundAccuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentTrial = 0
            reactionTimes.clear()
            
            Handler(Looper.getMainLooper()).postDelayed({
                generateNextRotation()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0
        val avgReactionTime = if (reactionTimes.isNotEmpty()) reactionTimes.average().toLong() else 0L

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7,
                timeSpentMs = totalTime / maxRounds,
                attempts = trialsPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
                Rotations: $totalAttempts

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Total Rotations: $totalAttempts

                Spatial Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Determine if rotated 3D shapes match the original

                📋 RULES:
                • Look at the original shape on the left
                • Compare it to the rotated shape on the right
                • Decide if they are the same shape, just rotated
                • Tap MATCH if they're the same, NO MATCH if different

                💡 TIPS:
                • Mentally rotate the shapes in your mind
                • Look for distinctive features and edges
                • Consider symmetry - some shapes look the same when rotated
                • Practice improves spatial visualization skills

                🏆 SCORING:
                • Accuracy = correct identifications / total shapes
                • Speed = average mental rotation time
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
