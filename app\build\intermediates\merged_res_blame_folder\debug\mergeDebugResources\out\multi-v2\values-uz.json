{"logs": [{"outputFile": "com.leapiq.braintraining.app-mergeDebugResources-2:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\99c81b9c5de38cf196c956f561c1361c\\transformed\\navigation-ui-2.8.4\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,121", "endOffsets": "151,273"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "9497,9598", "endColumns": "100,121", "endOffsets": "9593,9715"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\483818d0c523612987cfb44bb19710ad\\transformed\\material-1.12.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,360,440,523,617,704,799,926,1010,1070,1134,1237,1307,1374,1483,1546,1613,1672,1746,1809,1863,1978,2036,2098,2152,2227,2356,2446,2526,2619,2703,2792,2933,3015,3097,3236,3322,3406,3466,3517,3583,3656,3734,3805,3886,3958,4035,4110,4181,4282,4376,4455,4551,4645,4719,4795,4881,4934,5021,5087,5172,5263,5325,5389,5452,5521,5623,5724,5820,5921,5985,6040,6123,6209,6286", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,76,79,82,93,86,94,126,83,59,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,79,92,83,88,140,81,81,138,85,83,59,50,65,72,77,70,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82,85,76,73", "endOffsets": "278,355,435,518,612,699,794,921,1005,1065,1129,1232,1302,1369,1478,1541,1608,1667,1741,1804,1858,1973,2031,2093,2147,2222,2351,2441,2521,2614,2698,2787,2928,3010,3092,3231,3317,3401,3461,3512,3578,3651,3729,3800,3881,3953,4030,4105,4176,4277,4371,4450,4546,4640,4714,4790,4876,4929,5016,5082,5167,5258,5320,5384,5447,5516,5618,5719,5815,5916,5980,6035,6118,6204,6281,6355"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3004,3081,3161,3244,3338,4161,4256,4383,4467,4527,4591,4694,4764,4831,4940,5003,5070,5129,5203,5266,5320,5435,5493,5555,5609,5684,5813,5903,5983,6076,6160,6249,6390,6472,6554,6693,6779,6863,6923,6974,7040,7113,7191,7262,7343,7415,7492,7567,7638,7739,7833,7912,8008,8102,8176,8252,8338,8391,8478,8544,8629,8720,8782,8846,8909,8978,9080,9181,9277,9378,9442,9720,9887,9973,10050", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,113,115,116,117", "endColumns": "12,76,79,82,93,86,94,126,83,59,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,79,92,83,88,140,81,81,138,85,83,59,50,65,72,77,70,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82,85,76,73", "endOffsets": "328,3076,3156,3239,3333,3420,4251,4378,4462,4522,4586,4689,4759,4826,4935,4998,5065,5124,5198,5261,5315,5430,5488,5550,5604,5679,5808,5898,5978,6071,6155,6244,6385,6467,6549,6688,6774,6858,6918,6969,7035,7108,7186,7257,7338,7410,7487,7562,7633,7734,7828,7907,8003,8097,8171,8247,8333,8386,8473,8539,8624,8715,8777,8841,8904,8973,9075,9176,9272,9373,9437,9492,9798,9968,10045,10119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5eef04db0f25a1c94c01cf88ad6b7047\\transformed\\appcompat-1.7.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,533,633,715,815,932,1017,1095,1186,1279,1374,1468,1562,1655,1750,1845,1936,2028,2112,2222,2328,2428,2536,2642,2744,2905,9803", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "433,528,628,710,810,927,1012,1090,1181,1274,1369,1463,1557,1650,1745,1840,1931,2023,2107,2217,2323,2423,2531,2637,2739,2900,2999,9882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c95e9bdb43d4dfa17dc0c6aa1e95084d\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "38,39,40,41,42,43,44,118", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3425,3527,3629,3730,3830,3938,4042,10124", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "3522,3624,3725,3825,3933,4037,4156,10220"}}]}]}