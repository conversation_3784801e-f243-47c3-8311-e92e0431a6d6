package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityVisualSearchBinding
import com.leapiq.braintraining.ui.games.adapter.VisualSearchAdapter
import com.leapiq.braintraining.ui.games.model.SearchItem
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Visual Search Game
 * Players find specific targets among distractors in a grid
 * Tests selective attention and visual processing speed
 */
class VisualSearchActivity : AppCompatActivity() {

    private lateinit var binding: ActivityVisualSearchBinding
    private lateinit var adapter: VisualSearchAdapter
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "attention_3"

    // Visual search specific
    private var searchItems = mutableListOf<SearchItem>()
    private var targetShape = ""
    private var targetColor = ""
    private var trialStartTime = 0L
    private var currentTrial = 0
    private var trialsPerRound = 8
    private var gridSize = 4

    // Available shapes and colors
    private val shapes = listOf("circle", "square", "triangle", "diamond")
    private val colors = listOf("red", "blue", "green", "yellow", "purple", "orange")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityVisualSearchBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.visual_search)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Find the target among the distractors!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentTrial = 0
        trialsPerRound = getTrialsPerRound(currentLevel)
        gridSize = getGridSize(currentLevel)
        
        binding.trialText.text = "Trial 1/$trialsPerRound"
        
        setupGrid()
        startNextTrial()
    }

    private fun getTrialsPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 8       // 8 trials per round
            in 6..10 -> 10     // 10 trials per round
            in 11..15 -> 12    // 12 trials per round
            in 16..20 -> 15    // 15 trials per round
            else -> 18         // 18 trials per round
        }
    }

    private fun getGridSize(level: Int): Int {
        return when (level) {
            in 1..3 -> 4       // 4x4 grid (16 items)
            in 4..6 -> 5       // 5x5 grid (25 items)
            in 7..10 -> 6      // 6x6 grid (36 items)
            in 11..15 -> 7     // 7x7 grid (49 items)
            in 16..20 -> 8     // 8x8 grid (64 items)
            else -> 9          // 9x9 grid (81 items)
        }
    }

    private fun setupGrid() {
        adapter = VisualSearchAdapter(searchItems) { position ->
            onItemClicked(position)
        }

        binding.searchGrid.apply {
            layoutManager = GridLayoutManager(this@VisualSearchActivity, gridSize)
            adapter = <EMAIL>
        }
    }

    private fun startNextTrial() {
        if (currentTrial >= trialsPerRound) {
            roundComplete()
            return
        }

        currentTrial++
        binding.trialText.text = "Trial $currentTrial/$trialsPerRound"
        
        generateSearchArray()
        displayTarget()
        trialStartTime = System.currentTimeMillis()
    }

    private fun generateSearchArray() {
        searchItems.clear()
        val totalItems = gridSize * gridSize
        
        // Generate target
        targetShape = shapes.random()
        targetColor = colors.random()
        
        // Determine search difficulty
        val difficulty = getSearchDifficulty(currentLevel)
        
        // Generate distractors
        val distractors = generateDistractors(totalItems - 1, difficulty)
        
        // Add target at random position
        val targetPosition = Random.nextInt(totalItems)
        
        for (i in 0 until totalItems) {
            if (i == targetPosition) {
                searchItems.add(SearchItem(targetShape, targetColor, true))
            } else {
                val distractorIndex = if (i < targetPosition) i else i - 1
                searchItems.add(distractors[distractorIndex])
            }
        }
        
        adapter.notifyDataSetChanged()
    }

    private fun getSearchDifficulty(level: Int): SearchDifficulty {
        return when (level) {
            in 1..5 -> SearchDifficulty.EASY      // Different shape and color
            in 6..10 -> SearchDifficulty.MEDIUM   // Same shape OR same color
            in 11..15 -> SearchDifficulty.HARD    // Same shape AND different color
            in 16..20 -> SearchDifficulty.VERY_HARD // Same shape AND similar color
            else -> SearchDifficulty.EXTREME      // Very similar items
        }
    }

    private fun generateDistractors(count: Int, difficulty: SearchDifficulty): List<SearchItem> {
        val distractors = mutableListOf<SearchItem>()
        
        repeat(count) {
            val distractor = when (difficulty) {
                SearchDifficulty.EASY -> {
                    // Different shape and color
                    val shape = shapes.filter { it != targetShape }.random()
                    val color = colors.filter { it != targetColor }.random()
                    SearchItem(shape, color, false)
                }
                SearchDifficulty.MEDIUM -> {
                    // Same shape OR same color, but not both
                    if (Random.nextBoolean()) {
                        // Same shape, different color
                        val color = colors.filter { it != targetColor }.random()
                        SearchItem(targetShape, color, false)
                    } else {
                        // Different shape, same color
                        val shape = shapes.filter { it != targetShape }.random()
                        SearchItem(shape, targetColor, false)
                    }
                }
                SearchDifficulty.HARD -> {
                    // Same shape, different color
                    val color = colors.filter { it != targetColor }.random()
                    SearchItem(targetShape, color, false)
                }
                SearchDifficulty.VERY_HARD -> {
                    // Same shape, similar color (adjacent in list)
                    val colorIndex = colors.indexOf(targetColor)
                    val similarColors = listOf(
                        colors.getOrNull(colorIndex - 1),
                        colors.getOrNull(colorIndex + 1)
                    ).filterNotNull().ifEmpty { colors.filter { it != targetColor } }
                    SearchItem(targetShape, similarColors.random(), false)
                }
                SearchDifficulty.EXTREME -> {
                    // Mix of very similar items
                    if (Random.nextFloat() < 0.7f) {
                        // Same shape, similar color
                        val colorIndex = colors.indexOf(targetColor)
                        val similarColors = listOf(
                            colors.getOrNull(colorIndex - 1),
                            colors.getOrNull(colorIndex + 1)
                        ).filterNotNull().ifEmpty { colors.filter { it != targetColor } }
                        SearchItem(targetShape, similarColors.random(), false)
                    } else {
                        // Different shape, same color
                        val shape = shapes.filter { it != targetShape }.random()
                        SearchItem(shape, targetColor, false)
                    }
                }
            }
            distractors.add(distractor)
        }
        
        return distractors
    }

    private fun displayTarget() {
        binding.targetDisplay.text = "Find: $targetColor $targetShape"
        binding.instructionText.text = "Tap the $targetColor $targetShape as quickly as possible!"
    }

    private fun onItemClicked(position: Int) {
        val searchTime = System.currentTimeMillis() - trialStartTime
        val clickedItem = searchItems[position]
        totalAttempts++
        
        val isCorrect = clickedItem.isTarget
        if (isCorrect) {
            totalCorrect++
            showTrialFeedback(true, searchTime)
        } else {
            showTrialFeedback(false, searchTime, "Wrong item! Look for $targetColor $targetShape")
        }
        
        // Continue to next trial after delay
        Handler(Looper.getMainLooper()).postDelayed({
            startNextTrial()
        }, 1500)
    }

    private fun showTrialFeedback(isCorrect: Boolean, searchTime: Long, customMessage: String? = null) {
        val feedback = customMessage ?: if (isCorrect) {
            "Correct! Found in ${searchTime}ms"
        } else {
            "Wrong! Time: ${searchTime}ms"
        }
        binding.instructionText.text = feedback
    }

    private fun roundComplete() {
        val roundAccuracy = if (trialsPerRound > 0) {
            (totalCorrect.toDouble() / trialsPerRound)
        } else 1.0
        
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Accuracy: ${(roundAccuracy * 100).toInt()}%
                Grid size: ${gridSize}x${gridSize}
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentTrial = 0
            
            Handler(Looper.getMainLooper()).postDelayed({
                startNextTrial()
            }, 2000)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7, // 70% threshold
                timeSpentMs = totalTime / maxRounds,
                attempts = trialsPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Avg Search Time: ${totalTime / totalAttempts}ms
                Grid Size: ${gridSize}x${gridSize}

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Total Searches: $totalAttempts

                Visual Search Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Find the target item among distractors

                📋 RULES:
                • Look for the specified target (color + shape)
                • Tap the target as quickly as possible
                • Ignore all distractor items
                • Complete trials to advance levels

                💡 TIPS:
                • Focus on both color AND shape
                • Scan systematically across the grid
                • Higher levels have more similar distractors

                🏆 SCORING:
                • Accuracy = correct targets found / total trials
                • Speed = average search time
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    enum class SearchDifficulty {
        EASY, MEDIUM, HARD, VERY_HARD, EXTREME
    }
}
