package com.leapiq.braintraining.ui.progress.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ItemAchievementBinding
import com.leapiq.braintraining.data.model.Achievement

class AchievementAdapter : ListAdapter<Achievement, AchievementAdapter.AchievementViewHolder>(DiffCallback) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AchievementViewHolder {
        val binding = ItemAchievementBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return AchievementViewHolder(binding)
    }

    override fun onBindViewHolder(holder: AchievementViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class AchievementViewHolder(
        private val binding: ItemAchievementBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(achievement: Achievement) {
            binding.apply {
                achievementName.text = achievement.name
                achievementDescription.text = achievement.description
                
                // Set icon based on achievement type
                // TODO: Set appropriate icon based on achievement.iconResource
                
                // Change appearance based on unlock status
                val alpha = if (achievement.isUnlocked) 1.0f else 0.5f
                achievementIcon.alpha = alpha
                achievementName.alpha = alpha
                achievementDescription.alpha = alpha
                
                // Set icon tint based on unlock status
                val tintColor = if (achievement.isUnlocked) {
                    itemView.context.getColor(R.color.premium_gold_dark)
                } else {
                    itemView.context.getColor(R.color.text_secondary)
                }
                achievementIcon.setColorFilter(tintColor)
            }
        }
    }

    companion object {
        private val DiffCallback = object : DiffUtil.ItemCallback<Achievement>() {
            override fun areItemsTheSame(oldItem: Achievement, newItem: Achievement): Boolean {
                return oldItem.id == newItem.id
            }

            override fun areContentsTheSame(oldItem: Achievement, newItem: Achievement): Boolean {
                return oldItem == newItem
            }
        }
    }
}
