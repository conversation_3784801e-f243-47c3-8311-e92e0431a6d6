package com.leapiq.braintraining.ui.games.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.ui.games.model.WordSearchWord

/**
 * Adapter for the word list in word search game
 * Shows words to find with found/unfound status
 */
class WordListAdapter(
    private var words: List<WordSearchWord>
) : RecyclerView.Adapter<WordListAdapter.WordViewHolder>() {

    class WordViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val wordText: TextView = itemView.findViewById(R.id.word_text)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): WordViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_word_search_word, parent, false)
        return WordViewHolder(view)
    }

    override fun onBindViewHolder(holder: WordViewHolder, position: Int) {
        val word = words[position]
        
        holder.wordText.text = word.word
        
        // Set appearance based on found status
        if (word.isFound) {
            holder.wordText.setTextColor(
                ContextCompat.getColor(holder.itemView.context, R.color.stroop_green)
            )
            holder.wordText.paintFlags = holder.wordText.paintFlags or android.graphics.Paint.STRIKE_THRU_TEXT_FLAG
        } else {
            holder.wordText.setTextColor(
                ContextCompat.getColor(holder.itemView.context, R.color.text_primary)
            )
            holder.wordText.paintFlags = holder.wordText.paintFlags and android.graphics.Paint.STRIKE_THRU_TEXT_FLAG.inv()
        }
    }

    override fun getItemCount() = words.size

    fun updateWords(newWords: List<WordSearchWord>) {
        words = newWords
        notifyDataSetChanged()
    }
}
