<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_white">

    <!-- ViewPager for welcome pages -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Tab Layout (dots indicator) -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="32dp"
        android:layout_marginHorizontal="32dp"
        app:tabBackground="@drawable/tab_selector"
        app:tabGravity="center"
        app:tabIndicatorHeight="0dp"
        app:tabMaxWidth="24dp"
        app:tabMinWidth="24dp"
        app:tabPaddingStart="8dp"
        app:tabPaddingEnd="8dp" />

    <!-- Navigation buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="32dp"
        android:paddingBottom="40dp"
        android:gravity="center_vertical">

        <!-- Previous button -->
        <Button
            android:id="@+id/btnPrevious"
            android:layout_width="100dp"
            android:layout_height="48dp"
            android:text="Previous"
            android:textSize="14sp"
            android:textStyle="bold"
            android:background="@drawable/button_secondary"
            android:textColor="@color/primary_light_blue"
            android:elevation="2dp"
            android:visibility="invisible" />

        <!-- Skip button -->
        <Button
            android:id="@+id/btnSkip"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginHorizontal="16dp"
            android:text="Skip"
            android:textSize="14sp"
            android:textStyle="bold"
            android:background="@android:color/transparent"
            android:textColor="@color/text_secondary"
            android:gravity="center" />

        <!-- Next/Get Started button -->
        <Button
            android:id="@+id/btnNext"
            android:layout_width="120dp"
            android:layout_height="48dp"
            android:text="Next"
            android:textSize="14sp"
            android:textStyle="bold"
            android:background="@drawable/button_primary"
            android:textColor="@color/text_white"
            android:elevation="4dp" />

    </LinearLayout>

</LinearLayout>
