// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityLogicPuzzlesBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnCheckSolution;

  @NonNull
  public final Button btnHint;

  @NonNull
  public final ImageButton btnMenu;

  @NonNull
  public final ImageButton btnQuit;

  @NonNull
  public final Button btnReset;

  @NonNull
  public final RecyclerView cluesRecyclerView;

  @NonNull
  public final TextView gameTitle;

  @NonNull
  public final RecyclerView gridRecyclerView;

  @NonNull
  public final TextView hintsText;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final TextView levelText;

  @NonNull
  public final TextView puzzleText;

  @NonNull
  public final TextView roundText;

  private ActivityLogicPuzzlesBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnCheckSolution, @NonNull Button btnHint, @NonNull ImageButton btnMenu,
      @NonNull ImageButton btnQuit, @NonNull Button btnReset,
      @NonNull RecyclerView cluesRecyclerView, @NonNull TextView gameTitle,
      @NonNull RecyclerView gridRecyclerView, @NonNull TextView hintsText,
      @NonNull TextView instructionText, @NonNull TextView levelText, @NonNull TextView puzzleText,
      @NonNull TextView roundText) {
    this.rootView = rootView;
    this.btnCheckSolution = btnCheckSolution;
    this.btnHint = btnHint;
    this.btnMenu = btnMenu;
    this.btnQuit = btnQuit;
    this.btnReset = btnReset;
    this.cluesRecyclerView = cluesRecyclerView;
    this.gameTitle = gameTitle;
    this.gridRecyclerView = gridRecyclerView;
    this.hintsText = hintsText;
    this.instructionText = instructionText;
    this.levelText = levelText;
    this.puzzleText = puzzleText;
    this.roundText = roundText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityLogicPuzzlesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityLogicPuzzlesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_logic_puzzles, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityLogicPuzzlesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_check_solution;
      Button btnCheckSolution = ViewBindings.findChildViewById(rootView, id);
      if (btnCheckSolution == null) {
        break missingId;
      }

      id = R.id.btn_hint;
      Button btnHint = ViewBindings.findChildViewById(rootView, id);
      if (btnHint == null) {
        break missingId;
      }

      id = R.id.btn_menu;
      ImageButton btnMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnMenu == null) {
        break missingId;
      }

      id = R.id.btn_quit;
      ImageButton btnQuit = ViewBindings.findChildViewById(rootView, id);
      if (btnQuit == null) {
        break missingId;
      }

      id = R.id.btn_reset;
      Button btnReset = ViewBindings.findChildViewById(rootView, id);
      if (btnReset == null) {
        break missingId;
      }

      id = R.id.clues_recycler_view;
      RecyclerView cluesRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (cluesRecyclerView == null) {
        break missingId;
      }

      id = R.id.game_title;
      TextView gameTitle = ViewBindings.findChildViewById(rootView, id);
      if (gameTitle == null) {
        break missingId;
      }

      id = R.id.grid_recycler_view;
      RecyclerView gridRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (gridRecyclerView == null) {
        break missingId;
      }

      id = R.id.hints_text;
      TextView hintsText = ViewBindings.findChildViewById(rootView, id);
      if (hintsText == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.level_text;
      TextView levelText = ViewBindings.findChildViewById(rootView, id);
      if (levelText == null) {
        break missingId;
      }

      id = R.id.puzzle_text;
      TextView puzzleText = ViewBindings.findChildViewById(rootView, id);
      if (puzzleText == null) {
        break missingId;
      }

      id = R.id.round_text;
      TextView roundText = ViewBindings.findChildViewById(rootView, id);
      if (roundText == null) {
        break missingId;
      }

      return new ActivityLogicPuzzlesBinding((LinearLayout) rootView, btnCheckSolution, btnHint,
          btnMenu, btnQuit, btnReset, cluesRecyclerView, gameTitle, gridRecyclerView, hintsText,
          instructionText, levelText, puzzleText, roundText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
