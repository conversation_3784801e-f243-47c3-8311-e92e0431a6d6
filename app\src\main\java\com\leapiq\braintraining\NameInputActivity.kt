package com.leapiq.braintraining

import android.content.Intent
import android.content.SharedPreferences
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.leapiq.braintraining.databinding.ActivityNameInputBinding

class NameInputActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityNameInputBinding
    private lateinit var sharedPreferences: SharedPreferences
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityNameInputBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        sharedPreferences = getSharedPreferences("LeapIQ_Prefs", MODE_PRIVATE)
        
        setupUI()
    }
    
    private fun setupUI() {
        binding.btnContinue.setOnClickListener {
            val name = binding.etName.text.toString().trim()
            
            if (name.isEmpty()) {
                Toast.makeText(this, "Please enter your name", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            if (name.length < 2) {
                Toast.makeText(this, "Name must be at least 2 characters", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            // Save name and mark onboarding as complete
            saveName(name)
            
            // Request notification permission
            startActivity(Intent(this, NotificationPermissionActivity::class.java))
            finish()
        }
        
        binding.btnSkip.setOnClickListener {
            // Save default name and mark onboarding as complete
            saveName("User")
            
            // Request notification permission
            startActivity(Intent(this, NotificationPermissionActivity::class.java))
            finish()
        }
    }
    
    private fun saveName(name: String) {
        sharedPreferences.edit()
            .putString("user_name", name)
            .putBoolean("onboarding_complete", true)
            .putLong("first_launch_time", System.currentTimeMillis())
            .apply()
    }
}
