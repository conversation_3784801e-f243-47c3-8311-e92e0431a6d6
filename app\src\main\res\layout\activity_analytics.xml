<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/surface_white"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="Analytics"
        app:titleTextColor="@color/text_primary" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Overall Progress -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Overall Progress"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <ProgressBar
                        android:id="@+id/overall_progress_bar"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="match_parent"
                        android:layout_height="12dp"
                        android:layout_marginBottom="8dp"
                        android:progressTint="@color/primary_light_blue"
                        android:max="100" />

                    <TextView
                        android:id="@+id/overall_progress_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="0% Complete"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Top Performing Test -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🏆 Top Performing Test"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/top_performing_test_text"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:text="Memory Assessment"
                                android:textColor="@color/text_primary"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <TextView
                            android:id="@+id/top_performing_score_text"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="85%"
                            android:textColor="@color/success_green"
                            android:textSize="24sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Cognitive vs Personality -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="🧠 vs 👤 Performance Comparison"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="2">

                        <!-- Cognitive -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="🧠"
                                android:textSize="32sp"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Cognitive"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:id="@+id/cognitive_avg_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0%"
                                android:textColor="@color/success_green"
                                android:textSize="20sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                        <!-- Personality -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="👤"
                                android:textSize="32sp"
                                android:layout_marginBottom="8dp" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Personality"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:layout_marginBottom="4dp" />

                            <TextView
                                android:id="@+id/personality_avg_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0%"
                                android:textColor="@color/warning_orange"
                                android:textSize="20sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </LinearLayout>

                    <TextView
                        android:id="@+id/preferred_type_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="You tend to perform better on cognitive tests"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:gravity="center"
                        android:layout_marginTop="12dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Key Insights -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="💡 Key Insights"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <!-- Insight 1 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:id="@+id/insight1_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Your Strongest Area"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/insight1_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="You excel at memory tasks with consistent high performance"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <!-- Insight 2 -->
                    <LinearLayout
                        android:id="@+id/insight2_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginBottom="12dp"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/insight2_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Improvement Trend"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/insight2_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Your attention scores are improving consistently"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <!-- Insight 3 -->
                    <LinearLayout
                        android:id="@+id/insight3_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:visibility="gone">

                        <TextView
                            android:id="@+id/insight3_title"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Focus Area"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/insight3_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Processing speed could benefit from additional practice"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Strengths and Improvements -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:weightSum="2">

                <!-- Strengths -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginEnd="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="💪 Strength"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="12dp" />

                        <TextView
                            android:id="@+id/strength_area_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Memory Assessment"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/strength_score_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="85%"
                            android:textColor="@color/success_green"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/strength_consistency_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Consistency: 92%"
                            android:textColor="@color/text_secondary"
                            android:textSize="11sp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- Improvement Area -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    app:cardCornerRadius="12dp"
                    app:cardElevation="4dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="📈 Focus Area"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="12dp" />

                        <TextView
                            android:id="@+id/improvement_area_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Processing Speed"
                            android:textColor="@color/text_primary"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/improvement_score_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="65%"
                            android:textColor="@color/warning_orange"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:layout_marginBottom="4dp" />

                        <TextView
                            android:id="@+id/improvement_action_text"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="Practice regularly"
                            android:textColor="@color/text_secondary"
                            android:textSize="11sp" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
