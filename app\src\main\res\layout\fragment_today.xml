<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.today.TodayFragment">

    <!-- Header -->
    <include layout="@layout/layout_header" />

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Daily Challenges Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/daily_challenges"
                android:textColor="@color/text_primary"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <!-- Daily Challenges Grid -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/daily_challenges_recycler"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:nestedScrollingEnabled="false"
                app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                app:spanCount="2"
                tools:listitem="@layout/item_daily_challenge" />

            <!-- Start Challenge Button -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/start_challenge_button"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:layout_marginTop="24dp"
                android:backgroundTint="@color/primary_light_blue"
                android:text="@string/start_challenge"
                android:textColor="@color/text_white"
                android:textSize="16sp"
                android:textStyle="bold"
                app:cornerRadius="12dp" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>
