package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityPatternCompletionBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Pattern Completion Game
 * Players identify missing elements in visual patterns
 * Tests pattern recognition and logical reasoning skills
 */
class PatternCompletionActivity : AppCompatActivity() {

    private lateinit var binding: ActivityPatternCompletionBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "logic_5"

    // Pattern completion specific
    private var currentPatternType = PatternType.GEOMETRIC_SEQUENCE
    private var correctAnswer = ""
    private var answerOptions = mutableListOf<String>()
    private var problemStartTime = 0L
    private var currentTrial = 0
    private var trialsPerRound = 8
    private var reactionTimes = mutableListOf<Long>()

    // Pattern types
    private enum class PatternType {
        GEOMETRIC_SEQUENCE,    // Shapes in sequence
        COLOR_PATTERN,         // Color progressions
        SIZE_PATTERN,          // Size progressions
        ROTATION_PATTERN,      // Rotation sequences
        MATRIX_PATTERN,        // 3x3 matrix patterns
        ARITHMETIC_VISUAL      // Visual arithmetic patterns
    }

    // Visual elements
    private val shapes = listOf("●", "■", "▲", "♦", "★", "◆", "▼", "◀", "▶", "♠", "♥", "♣")
    private val colors = listOf("R", "B", "G", "Y", "P", "O") // Red, Blue, Green, Yellow, Purple, Orange
    private val sizes = listOf("S", "M", "L") // Small, Medium, Large

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityPatternCompletionBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.pattern_completion)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Find the pattern and choose what comes next!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup answer buttons
            setupAnswerButtons()
        }
    }

    private fun setupAnswerButtons() {
        binding.apply {
            btnOption1.setOnClickListener { selectAnswer(0) }
            btnOption2.setOnClickListener { selectAnswer(1) }
            btnOption3.setOnClickListener { selectAnswer(2) }
            btnOption4.setOnClickListener { selectAnswer(3) }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentTrial = 0
        reactionTimes.clear()
        trialsPerRound = getTrialsPerRound(currentLevel)
        
        binding.trialText.text = "Pattern 1/$trialsPerRound"
        
        generateNextPattern()
    }

    private fun getTrialsPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 8       // 8 patterns per round
            in 6..10 -> 10     // 10 patterns per round
            in 11..15 -> 12    // 12 patterns per round
            in 16..20 -> 15    // 15 patterns per round
            else -> 18         // 18 patterns per round
        }
    }

    private fun generateNextPattern() {
        if (currentTrial >= trialsPerRound) {
            roundComplete()
            return
        }

        currentTrial++
        binding.trialText.text = "Pattern $currentTrial/$trialsPerRound"
        
        val availableTypes = getAvailablePatternTypes(currentLevel)
        currentPatternType = availableTypes.random()
        
        generatePatternPuzzle()
        problemStartTime = System.currentTimeMillis()
    }

    private fun getAvailablePatternTypes(level: Int): List<PatternType> {
        return when (level) {
            in 1..3 -> listOf(PatternType.GEOMETRIC_SEQUENCE)
            in 4..6 -> listOf(PatternType.GEOMETRIC_SEQUENCE, PatternType.COLOR_PATTERN)
            in 7..10 -> listOf(PatternType.GEOMETRIC_SEQUENCE, PatternType.COLOR_PATTERN, PatternType.SIZE_PATTERN)
            in 11..15 -> listOf(PatternType.GEOMETRIC_SEQUENCE, PatternType.COLOR_PATTERN, PatternType.SIZE_PATTERN, PatternType.ROTATION_PATTERN)
            in 16..20 -> listOf(PatternType.GEOMETRIC_SEQUENCE, PatternType.COLOR_PATTERN, PatternType.SIZE_PATTERN, PatternType.ROTATION_PATTERN, PatternType.MATRIX_PATTERN)
            else -> PatternType.values().toList()
        }
    }

    private fun generatePatternPuzzle() {
        when (currentPatternType) {
            PatternType.GEOMETRIC_SEQUENCE -> generateGeometricSequence()
            PatternType.COLOR_PATTERN -> generateColorPattern()
            PatternType.SIZE_PATTERN -> generateSizePattern()
            PatternType.ROTATION_PATTERN -> generateRotationPattern()
            PatternType.MATRIX_PATTERN -> generateMatrixPattern()
            PatternType.ARITHMETIC_VISUAL -> generateArithmeticVisual()
        }
    }

    private fun generateGeometricSequence() {
        val sequenceLength = 4
        val selectedShapes = shapes.shuffled().take(sequenceLength)
        
        // Create a repeating pattern
        val pattern = selectedShapes + selectedShapes.take(2) // Show pattern + 2 more
        val missingIndex = pattern.size - 1
        correctAnswer = pattern[missingIndex]
        
        // Display pattern with missing element
        val displayPattern = pattern.dropLast(1).joinToString("  ") + "  ?"
        
        // Generate answer options
        answerOptions = mutableListOf(correctAnswer)
        while (answerOptions.size < 4) {
            val wrongAnswer = shapes.random()
            if (wrongAnswer !in answerOptions) {
                answerOptions.add(wrongAnswer)
            }
        }
        answerOptions.shuffle()
        
        binding.apply {
            patternTypeText.text = "GEOMETRIC SEQUENCE"
            patternDisplay.text = displayPattern
            questionText.text = "What comes next in the sequence?"
        }
        
        displayAnswerOptions()
    }

    private fun generateColorPattern() {
        val patternLength = 5
        val colorSequence = mutableListOf<String>()
        
        // Create alternating color pattern
        val color1 = colors.random()
        val color2 = colors.filter { it != color1 }.random()
        
        for (i in 0 until patternLength) {
            colorSequence.add(if (i % 2 == 0) color1 else color2)
        }
        
        correctAnswer = if (patternLength % 2 == 0) color1 else color2
        
        // Display pattern
        val displayPattern = colorSequence.joinToString("  ") + "  ?"
        
        // Generate answer options
        answerOptions = mutableListOf(correctAnswer)
        colors.filter { it != correctAnswer }.shuffled().take(3).forEach {
            answerOptions.add(it)
        }
        answerOptions.shuffle()
        
        binding.apply {
            patternTypeText.text = "COLOR PATTERN"
            patternDisplay.text = displayPattern
            questionText.text = "What color comes next?"
        }
        
        displayAnswerOptions()
    }

    private fun generateSizePattern() {
        val sizeSequence = listOf("S", "M", "L", "S", "M") // Small, Medium, Large pattern
        correctAnswer = "L"
        
        val shape = shapes.random()
        val displayPattern = sizeSequence.map { size ->
            when (size) {
                "S" -> "◦$shape"
                "M" -> "○$shape"
                "L" -> "●$shape"
                else -> shape
            }
        }.joinToString("  ") + "  ?"
        
        // Generate answer options with size indicators
        answerOptions = mutableListOf("●$shape", "○$shape", "◦$shape", "▪$shape")
        answerOptions.shuffle()
        correctAnswer = "●$shape"
        
        binding.apply {
            patternTypeText.text = "SIZE PATTERN"
            patternDisplay.text = displayPattern
            questionText.text = "What size comes next?"
        }
        
        displayAnswerOptions()
    }

    private fun generateRotationPattern() {
        val rotations = listOf("▶", "▼", "◀", "▲") // Right, Down, Left, Up
        val patternLength = 6
        val rotationSequence = mutableListOf<String>()
        
        for (i in 0 until patternLength) {
            rotationSequence.add(rotations[i % rotations.size])
        }
        
        correctAnswer = rotations[patternLength % rotations.size]
        
        val displayPattern = rotationSequence.joinToString("  ") + "  ?"
        
        // Generate answer options
        answerOptions = rotations.toMutableList()
        answerOptions.shuffle()
        
        binding.apply {
            patternTypeText.text = "ROTATION PATTERN"
            patternDisplay.text = displayPattern
            questionText.text = "What direction comes next?"
        }
        
        displayAnswerOptions()
    }

    private fun generateMatrixPattern() {
        // Simple 3x3 matrix with one missing element
        val matrix = Array(3) { Array(3) { "●" } }
        
        // Create a pattern (diagonal)
        for (i in 0..2) {
            matrix[i][i] = "■"
        }
        
        // Remove one element
        val missingRow = Random.nextInt(3)
        val missingCol = Random.nextInt(3)
        correctAnswer = matrix[missingRow][missingCol]
        matrix[missingRow][missingCol] = "?"
        
        // Display matrix
        val displayPattern = matrix.joinToString("\n") { row ->
            row.joinToString("  ")
        }
        
        // Generate answer options
        answerOptions = mutableListOf("●", "■", "▲", "♦")
        answerOptions.shuffle()
        
        binding.apply {
            patternTypeText.text = "MATRIX PATTERN"
            patternDisplay.text = displayPattern
            questionText.text = "What goes in the missing spot?"
        }
        
        displayAnswerOptions()
    }

    private fun generateArithmeticVisual() {
        val numbers = listOf(1, 2, 3, 4, 5)
        val sequence = numbers.take(4)
        correctAnswer = "5"
        
        // Display as dots
        val displayPattern = sequence.map { num ->
            "●".repeat(num)
        }.joinToString("  ") + "  ?"
        
        // Generate answer options as dot patterns
        answerOptions = mutableListOf(
            "●●●●●", // 5 dots
            "●●●●●●", // 6 dots
            "●●●", // 3 dots
            "●●●●●●●" // 7 dots
        )
        answerOptions.shuffle()
        correctAnswer = "●●●●●"
        
        binding.apply {
            patternTypeText.text = "ARITHMETIC VISUAL"
            patternDisplay.text = displayPattern
            questionText.text = "How many dots come next?"
        }
        
        displayAnswerOptions()
    }

    private fun displayAnswerOptions() {
        binding.apply {
            btnOption1.text = answerOptions[0]
            btnOption2.text = answerOptions[1]
            btnOption3.text = answerOptions[2]
            btnOption4.text = answerOptions[3]
        }
    }

    private fun selectAnswer(optionIndex: Int) {
        val selectedAnswer = answerOptions[optionIndex]
        val reactionTime = System.currentTimeMillis() - problemStartTime
        
        totalAttempts++
        val isCorrect = selectedAnswer == correctAnswer
        
        if (isCorrect) {
            totalCorrect++
            reactionTimes.add(reactionTime)
        }
        
        showFeedback(selectedAnswer, isCorrect, reactionTime)
        
        // Continue to next pattern after delay
        Handler(Looper.getMainLooper()).postDelayed({
            generateNextPattern()
        }, 2000)
    }

    private fun showFeedback(selectedAnswer: String, isCorrect: Boolean, reactionTime: Long) {
        val feedback = if (isCorrect) {
            "Correct! You selected: \"$selectedAnswer\" (${reactionTime}ms)"
        } else {
            "Wrong! You selected: \"$selectedAnswer\"\nCorrect answer: \"$correctAnswer\""
        }
        
        binding.instructionText.text = feedback
        binding.instructionText.setTextColor(
            ContextCompat.getColor(this, if (isCorrect) R.color.stroop_green else R.color.stroop_red)
        )
        
        // Reset color after delay
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.text = "Find the pattern and choose what comes next!"
            binding.instructionText.setTextColor(
                ContextCompat.getColor(this, R.color.text_secondary)
            )
        }, 2000)
    }

    private fun roundComplete() {
        val avgReactionTime = if (reactionTimes.isNotEmpty()) {
            reactionTimes.average().toLong()
        } else 0L
        
        val roundAccuracy = if (trialsPerRound > 0) {
            (totalCorrect.toDouble() / trialsPerRound)
        } else 1.0
        
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Accuracy: ${(roundAccuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentTrial = 0
            reactionTimes.clear()
            
            Handler(Looper.getMainLooper()).postDelayed({
                generateNextPattern()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0
        val avgReactionTime = if (reactionTimes.isNotEmpty()) reactionTimes.average().toLong() else 0L

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7,
                timeSpentMs = totalTime / maxRounds,
                attempts = trialsPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
                Patterns: $totalAttempts

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Total Patterns: $totalAttempts

                Pattern Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Identify patterns and choose what comes next

                📋 PATTERN TYPES:
                • Geometric: Shape sequences and repetitions
                • Color: Color progressions and alternations
                • Size: Size progressions (small → medium → large)
                • Rotation: Directional rotations and cycles
                • Matrix: 3x3 grid patterns with missing elements
                • Arithmetic: Visual number sequences

                💡 TIPS:
                • Look for repeating sequences
                • Notice changes in size, color, or direction
                • Consider mathematical progressions
                • Think about symmetry and balance

                🏆 SCORING:
                • Accuracy = correct patterns / total patterns
                • Speed = average pattern recognition time
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
