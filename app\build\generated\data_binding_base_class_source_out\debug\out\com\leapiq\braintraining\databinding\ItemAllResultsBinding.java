// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAllResultsBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView accuracyText;

  @NonNull
  public final TextView dateText;

  @NonNull
  public final TextView scoreText;

  @NonNull
  public final TextView testNameText;

  @NonNull
  public final TextView testTypeIcon;

  @NonNull
  public final TextView timeText;

  private ItemAllResultsBinding(@NonNull MaterialCardView rootView, @NonNull TextView accuracyText,
      @NonNull TextView dateText, @NonNull TextView scoreText, @NonNull TextView testNameText,
      @NonNull TextView testTypeIcon, @NonNull TextView timeText) {
    this.rootView = rootView;
    this.accuracyText = accuracyText;
    this.dateText = dateText;
    this.scoreText = scoreText;
    this.testNameText = testNameText;
    this.testTypeIcon = testTypeIcon;
    this.timeText = timeText;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAllResultsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAllResultsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_all_results, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAllResultsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.accuracy_text;
      TextView accuracyText = ViewBindings.findChildViewById(rootView, id);
      if (accuracyText == null) {
        break missingId;
      }

      id = R.id.date_text;
      TextView dateText = ViewBindings.findChildViewById(rootView, id);
      if (dateText == null) {
        break missingId;
      }

      id = R.id.score_text;
      TextView scoreText = ViewBindings.findChildViewById(rootView, id);
      if (scoreText == null) {
        break missingId;
      }

      id = R.id.test_name_text;
      TextView testNameText = ViewBindings.findChildViewById(rootView, id);
      if (testNameText == null) {
        break missingId;
      }

      id = R.id.test_type_icon;
      TextView testTypeIcon = ViewBindings.findChildViewById(rootView, id);
      if (testTypeIcon == null) {
        break missingId;
      }

      id = R.id.time_text;
      TextView timeText = ViewBindings.findChildViewById(rootView, id);
      if (timeText == null) {
        break missingId;
      }

      return new ItemAllResultsBinding((MaterialCardView) rootView, accuracyText, dateText,
          scoreText, testNameText, testTypeIcon, timeText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
