// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityStroopTestBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnBlue;

  @NonNull
  public final Button btnGreen;

  @NonNull
  public final ImageButton btnMenu;

  @NonNull
  public final ImageButton btnQuit;

  @NonNull
  public final Button btnRed;

  @NonNull
  public final Button btnYellow;

  @NonNull
  public final LinearLayout colorButtonsContainer;

  @NonNull
  public final TextView gameTitle;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final TextView levelText;

  @NonNull
  public final TextView roundText;

  @NonNull
  public final TextView trialText;

  @NonNull
  public final TextView wordDisplay;

  private ActivityStroopTestBinding(@NonNull LinearLayout rootView, @NonNull Button btnBlue,
      @NonNull Button btnGreen, @NonNull ImageButton btnMenu, @NonNull ImageButton btnQuit,
      @NonNull Button btnRed, @NonNull Button btnYellow,
      @NonNull LinearLayout colorButtonsContainer, @NonNull TextView gameTitle,
      @NonNull TextView instructionText, @NonNull TextView levelText, @NonNull TextView roundText,
      @NonNull TextView trialText, @NonNull TextView wordDisplay) {
    this.rootView = rootView;
    this.btnBlue = btnBlue;
    this.btnGreen = btnGreen;
    this.btnMenu = btnMenu;
    this.btnQuit = btnQuit;
    this.btnRed = btnRed;
    this.btnYellow = btnYellow;
    this.colorButtonsContainer = colorButtonsContainer;
    this.gameTitle = gameTitle;
    this.instructionText = instructionText;
    this.levelText = levelText;
    this.roundText = roundText;
    this.trialText = trialText;
    this.wordDisplay = wordDisplay;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityStroopTestBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityStroopTestBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_stroop_test, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityStroopTestBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_blue;
      Button btnBlue = ViewBindings.findChildViewById(rootView, id);
      if (btnBlue == null) {
        break missingId;
      }

      id = R.id.btn_green;
      Button btnGreen = ViewBindings.findChildViewById(rootView, id);
      if (btnGreen == null) {
        break missingId;
      }

      id = R.id.btn_menu;
      ImageButton btnMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnMenu == null) {
        break missingId;
      }

      id = R.id.btn_quit;
      ImageButton btnQuit = ViewBindings.findChildViewById(rootView, id);
      if (btnQuit == null) {
        break missingId;
      }

      id = R.id.btn_red;
      Button btnRed = ViewBindings.findChildViewById(rootView, id);
      if (btnRed == null) {
        break missingId;
      }

      id = R.id.btn_yellow;
      Button btnYellow = ViewBindings.findChildViewById(rootView, id);
      if (btnYellow == null) {
        break missingId;
      }

      id = R.id.color_buttons_container;
      LinearLayout colorButtonsContainer = ViewBindings.findChildViewById(rootView, id);
      if (colorButtonsContainer == null) {
        break missingId;
      }

      id = R.id.game_title;
      TextView gameTitle = ViewBindings.findChildViewById(rootView, id);
      if (gameTitle == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.level_text;
      TextView levelText = ViewBindings.findChildViewById(rootView, id);
      if (levelText == null) {
        break missingId;
      }

      id = R.id.round_text;
      TextView roundText = ViewBindings.findChildViewById(rootView, id);
      if (roundText == null) {
        break missingId;
      }

      id = R.id.trial_text;
      TextView trialText = ViewBindings.findChildViewById(rootView, id);
      if (trialText == null) {
        break missingId;
      }

      id = R.id.word_display;
      TextView wordDisplay = ViewBindings.findChildViewById(rootView, id);
      if (wordDisplay == null) {
        break missingId;
      }

      return new ActivityStroopTestBinding((LinearLayout) rootView, btnBlue, btnGreen, btnMenu,
          btnQuit, btnRed, btnYellow, colorButtonsContainer, gameTitle, instructionText, levelText,
          roundText, trialText, wordDisplay);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
