// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTestProgressBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView averageScoreText;

  @NonNull
  public final TextView bestScoreText;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView testNameText;

  @NonNull
  public final TextView timesCompletedText;

  @NonNull
  public final TextView trendIndicator;

  private ItemTestProgressBinding(@NonNull MaterialCardView rootView,
      @NonNull TextView averageScoreText, @NonNull TextView bestScoreText,
      @NonNull ProgressBar progressBar, @NonNull TextView testNameText,
      @NonNull TextView timesCompletedText, @NonNull TextView trendIndicator) {
    this.rootView = rootView;
    this.averageScoreText = averageScoreText;
    this.bestScoreText = bestScoreText;
    this.progressBar = progressBar;
    this.testNameText = testNameText;
    this.timesCompletedText = timesCompletedText;
    this.trendIndicator = trendIndicator;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTestProgressBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTestProgressBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_test_progress, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTestProgressBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.average_score_text;
      TextView averageScoreText = ViewBindings.findChildViewById(rootView, id);
      if (averageScoreText == null) {
        break missingId;
      }

      id = R.id.best_score_text;
      TextView bestScoreText = ViewBindings.findChildViewById(rootView, id);
      if (bestScoreText == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.test_name_text;
      TextView testNameText = ViewBindings.findChildViewById(rootView, id);
      if (testNameText == null) {
        break missingId;
      }

      id = R.id.times_completed_text;
      TextView timesCompletedText = ViewBindings.findChildViewById(rootView, id);
      if (timesCompletedText == null) {
        break missingId;
      }

      id = R.id.trend_indicator;
      TextView trendIndicator = ViewBindings.findChildViewById(rootView, id);
      if (trendIndicator == null) {
        break missingId;
      }

      return new ItemTestProgressBinding((MaterialCardView) rootView, averageScoreText,
          bestScoreText, progressBar, testNameText, timesCompletedText, trendIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
