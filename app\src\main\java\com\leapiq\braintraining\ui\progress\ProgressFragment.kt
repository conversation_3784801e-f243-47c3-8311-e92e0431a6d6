package com.leapiq.braintraining.ui.progress

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.FragmentProgressBinding
import com.leapiq.braintraining.ui.progress.adapter.AchievementAdapter
import com.leapiq.braintraining.ui.progress.adapter.CategoryAccuracyAdapter
import com.leapiq.braintraining.data.model.Achievement
import com.leapiq.braintraining.data.model.AchievementType
import com.leapiq.braintraining.data.model.CategoryAccuracy
import com.leapiq.braintraining.data.GameProgressManager

class ProgressFragment : Fragment() {

    private var _binding: FragmentProgressBinding? = null
    private val binding get() = _binding!!

    private lateinit var achievementAdapter: AchievementAdapter
    private lateinit var categoryAccuracyAdapter: CategoryAccuracyAdapter
    private lateinit var progressManager: GameProgressManager

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProgressBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        progressManager = GameProgressManager.getInstance(requireContext())

        setupHeader()
        setupUserProfile()
        setupCategoryAccuracy()
        setupAchievements()
        loadProgressData()
    }
    
    private fun setupHeader() {
        // Update header title to "Progress"
        binding.root.findViewById<android.widget.TextView>(R.id.header_title)?.text = 
            getString(R.string.nav_progress)
    }
    
    private fun setupUserProfile() {
        val userProgress = progressManager.getUserProgress()

        binding.apply {
            // Set real user data from progress manager
            username.text = "User"
            userLevel.text = getString(R.string.current_level, userProgress.userLevel)
            maxStreak.text = getString(R.string.max_streak, userProgress.maxStreak)
            currentStreak.text = getString(R.string.current_streak, userProgress.currentStreak)
            totalScore.text = userProgress.totalScore.toString()
            overallAccuracy.text = "Overall: ${userProgress.overallAccuracyPercentage}%"
            gamesPlayedWeek.text = getString(R.string.games_played_week, userProgress.gamesPlayedThisWeek)
        }
    }
    
    private fun setupCategoryAccuracy() {
        categoryAccuracyAdapter = CategoryAccuracyAdapter()
        
        binding.categoryAccuracyRecycler.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = categoryAccuracyAdapter
        }
    }
    
    private fun setupAchievements() {
        achievementAdapter = AchievementAdapter()
        
        binding.achievementsRecycler.apply {
            layoutManager = GridLayoutManager(context, 3)
            adapter = achievementAdapter
        }
    }
    
    private fun loadProgressData() {
        loadCategoryAccuracies()
        loadAchievements()
    }
    
    private fun loadCategoryAccuracies() {
        val userProgress = progressManager.getUserProgress()
        val accuracies = userProgress.categoryAccuracies.map { (category, accuracy) ->
            CategoryAccuracy(category.displayName, (accuracy * 100).toInt())
        }.ifEmpty {
            // Default values if no data yet
            listOf(
                CategoryAccuracy("Memory", 0),
                CategoryAccuracy("Attention", 0),
                CategoryAccuracy("Math", 0),
                CategoryAccuracy("Logic", 0),
                CategoryAccuracy("Language", 0)
            )
        }
        categoryAccuracyAdapter.submitList(accuracies)
    }
    
    private fun loadAchievements() {
        val achievements = listOf(
            Achievement("level_10", "Level 10", "Reach level 10", "ic_trophy", true, AchievementType.LEVEL),
            Achievement("streak_7", "Week Warrior", "7-day streak", "ic_streak", true, AchievementType.STREAK),
            Achievement("memory_master", "Memory Master", "Complete 10 memory games", "ic_memory", true, AchievementType.SCORE),
            Achievement("speed_demon", "Speed Demon", "90%+ in 5 focus games", "ic_speed", false, AchievementType.ACCURACY),
            Achievement("perfect_score", "Perfect Score", "Get 100% in any game", "ic_perfect", false, AchievementType.ACCURACY),
            Achievement("consistent", "Consistent Player", "Play 30 days in a row", "ic_consistent", false, AchievementType.STREAK)
        )
        achievementAdapter.submitList(achievements)
    }

    override fun onResume() {
        super.onResume()
        // Refresh progress data when returning from games
        loadProgressData()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
