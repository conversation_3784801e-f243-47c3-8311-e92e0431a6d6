package com.leapiq.braintraining.ui.tests

import android.os.Bundle
import android.view.View
import android.widget.RadioButton
import android.widget.RadioGroup
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityTestQuestionBaseBinding
import com.leapiq.braintraining.data.model.TestType

// Problem solving style categories
enum class ProblemSolvingType {
    ANALYTICAL,    // Logical, step-by-step approach
    INTUITIVE,     // Gut feeling, pattern recognition
    SYSTEMATIC,    // Methodical, structured approach
    CREATIVE       // Innovative, out-of-the-box thinking
}

// Question data classes
data class ProblemSolvingQuestion(
    val id: Int,
    val problemStatement: String,
    val questionText: String,
    val options: List<ProblemSolvingOption>,
    val questionType: QuestionType,
    val isTimedQuestion: Boolean = false,
    val timeLimitSeconds: Int = 60
)

data class ProblemSolvingOption(
    val text: String,
    val styleType: ProblemSolvingType,
    val score: Int, // 1-5 scale for style strength
    val isSelected: Boolean = false
)

enum class QuestionType {
    SCENARIO_BASED,    // Real-world problem scenarios
    LOGIC_PUZZLE,      // Logic and reasoning problems
    DECISION_MAKING,   // Choice-based decisions
    CREATIVE_CHALLENGE // Open-ended creative problems
}

/**
 * Problem Solving Style Test - Determines analytical vs intuitive approach
 * Tests systematic thinking, creative problem solving, decision making, and reasoning patterns
 * Dynamic timing based on reasoning complexity and solution paths
 */
class ProblemSolvingStyleActivity : BaseTestActivity() {

    private lateinit var binding: ActivityTestQuestionBaseBinding

    // Test configuration
    private var currentQuestionIndex = 0
    private val questions = mutableListOf<ProblemSolvingQuestion>()

    // Scoring data
    private var analyticalScore = 0
    private var intuitiveScore = 0
    private var systematicScore = 0
    private var creativeScore = 0
    

    
    override fun initializeUI() {
        binding = ActivityTestQuestionBaseBinding.inflate(layoutInflater)
        setContentView(binding.root)
        setupUI()
    }

    override fun initializeTest() {
        generateQuestions()
        totalQuestions = questions.size
        // Load first question
        if (questions.isNotEmpty()) {
            currentQuestion = 1
            loadQuestion(1)
        }
    }
    
    private fun setupUI() {
        binding.apply {
            // Setup toolbar
            toolbar.setNavigationOnClickListener { finish() }
            toolbar.title = "Problem Solving Style Test"

            // Setup navigation buttons
            btnPrevious.setOnClickListener { goToPreviousQuestion() }
            btnNext.setOnClickListener { goToNextQuestion() }
        }
    }
    

    
    private fun generateQuestions() {
        questions.addAll(listOf(
            // Scenario-based questions
            ProblemSolvingQuestion(
                1,
                "Your team needs to solve a complex technical problem with a tight deadline.",
                "What's your preferred approach to tackle this challenge?",
                listOf(
                    ProblemSolvingOption("Break down the problem into smaller, manageable parts and tackle each systematically", ProblemSolvingType.SYSTEMATIC, 5),
                    ProblemSolvingOption("Analyze similar past problems and apply proven solutions", ProblemSolvingType.ANALYTICAL, 4),
                    ProblemSolvingOption("Brainstorm creative alternatives and try unconventional approaches", ProblemSolvingType.CREATIVE, 5),
                    ProblemSolvingOption("Trust my instincts and go with the solution that feels right", ProblemSolvingType.INTUITIVE, 4)
                ),
                QuestionType.SCENARIO_BASED
            ),
            
            ProblemSolvingQuestion(
                2,
                "You're planning a vacation but have conflicting preferences from family members.",
                "How do you approach finding a solution that works for everyone?",
                listOf(
                    ProblemSolvingOption("Create a detailed comparison chart of all options and preferences", ProblemSolvingType.ANALYTICAL, 5),
                    ProblemSolvingOption("Follow a structured decision-making process with clear criteria", ProblemSolvingType.SYSTEMATIC, 4),
                    ProblemSolvingOption("Find a creative compromise that incorporates everyone's interests", ProblemSolvingType.CREATIVE, 4),
                    ProblemSolvingOption("Go with the option that feels like it will make everyone happiest", ProblemSolvingType.INTUITIVE, 3)
                ),
                QuestionType.DECISION_MAKING
            ),
            
            // Logic puzzle questions
            ProblemSolvingQuestion(
                3,
                "Logic Puzzle: If all roses are flowers, and some flowers fade quickly, can we conclude that some roses fade quickly?",
                "How do you approach this logical reasoning problem?",
                listOf(
                    ProblemSolvingOption("Draw diagrams to visualize the logical relationships", ProblemSolvingType.SYSTEMATIC, 5),
                    ProblemSolvingOption("Work through the logic step by step using formal reasoning", ProblemSolvingType.ANALYTICAL, 5),
                    ProblemSolvingOption("Think of real-world examples to test the logic", ProblemSolvingType.CREATIVE, 3),
                    ProblemSolvingOption("The answer feels obvious based on the statement structure", ProblemSolvingType.INTUITIVE, 2)
                ),
                QuestionType.LOGIC_PUZZLE,
                isTimedQuestion = true,
                timeLimitSeconds = 45
            ),
            
            ProblemSolvingQuestion(
                4,
                "You need to arrange 8 people around a circular table with specific seating constraints.",
                "What's your strategy for solving this arrangement problem?",
                listOf(
                    ProblemSolvingOption("List all constraints and work through them methodically", ProblemSolvingType.SYSTEMATIC, 5),
                    ProblemSolvingOption("Use logical deduction to eliminate impossible arrangements", ProblemSolvingType.ANALYTICAL, 4),
                    ProblemSolvingOption("Try different creative arrangements until one works", ProblemSolvingType.CREATIVE, 3),
                    ProblemSolvingOption("Start with an arrangement that feels balanced and adjust", ProblemSolvingType.INTUITIVE, 3)
                ),
                QuestionType.LOGIC_PUZZLE,
                isTimedQuestion = true,
                timeLimitSeconds = 60
            ),
            
            // Creative challenges
            ProblemSolvingQuestion(
                5,
                "Your company needs to increase customer engagement but has a limited budget.",
                "How do you approach generating solutions for this business challenge?",
                listOf(
                    ProblemSolvingOption("Research what competitors are doing and analyze their strategies", ProblemSolvingType.ANALYTICAL, 4),
                    ProblemSolvingOption("Follow a structured innovation process with defined stages", ProblemSolvingType.SYSTEMATIC, 4),
                    ProblemSolvingOption("Brainstorm unconventional ideas that haven't been tried before", ProblemSolvingType.CREATIVE, 5),
                    ProblemSolvingOption("Think about what would personally engage me as a customer", ProblemSolvingType.INTUITIVE, 4)
                ),
                QuestionType.CREATIVE_CHALLENGE
            ),
            
            ProblemSolvingQuestion(
                6,
                "You need to design a more efficient workflow for your team's daily operations.",
                "What's your approach to improving the current process?",
                listOf(
                    ProblemSolvingOption("Map out the current process and identify bottlenecks analytically", ProblemSolvingType.ANALYTICAL, 5),
                    ProblemSolvingOption("Use a systematic methodology like Lean or Six Sigma", ProblemSolvingType.SYSTEMATIC, 5),
                    ProblemSolvingOption("Experiment with completely different ways of organizing work", ProblemSolvingType.CREATIVE, 4),
                    ProblemSolvingOption("Observe the team and sense where the friction points are", ProblemSolvingType.INTUITIVE, 3)
                ),
                QuestionType.SCENARIO_BASED
            ),
            
            // Decision making under uncertainty
            ProblemSolvingQuestion(
                7,
                "You're investing in stocks but the market is highly unpredictable right now.",
                "How do you make investment decisions in this uncertain environment?",
                listOf(
                    ProblemSolvingOption("Analyze historical data, trends, and financial metrics thoroughly", ProblemSolvingType.ANALYTICAL, 5),
                    ProblemSolvingOption("Follow a disciplined investment strategy with clear rules", ProblemSolvingType.SYSTEMATIC, 4),
                    ProblemSolvingOption("Look for unconventional opportunities others might miss", ProblemSolvingType.CREATIVE, 4),
                    ProblemSolvingOption("Trust my gut feeling about which investments feel right", ProblemSolvingType.INTUITIVE, 3)
                ),
                QuestionType.DECISION_MAKING,
                isTimedQuestion = true,
                timeLimitSeconds = 40
            ),
            
            ProblemSolvingQuestion(
                8,
                "You're debugging a complex software issue that's affecting multiple systems.",
                "What's your debugging strategy?",
                listOf(
                    ProblemSolvingOption("Systematically check each component and log all findings", ProblemSolvingType.SYSTEMATIC, 5),
                    ProblemSolvingOption("Analyze error patterns and trace the logical flow", ProblemSolvingType.ANALYTICAL, 5),
                    ProblemSolvingOption("Try creative workarounds and unconventional solutions", ProblemSolvingType.CREATIVE, 3),
                    ProblemSolvingOption("Start with the area that intuitively seems most likely", ProblemSolvingType.INTUITIVE, 4)
                ),
                QuestionType.SCENARIO_BASED
            ),
            
            // Complex reasoning
            ProblemSolvingQuestion(
                9,
                "Puzzle: You have 12 balls, one of which weighs differently. Using a balance scale only 3 times, how do you find the different ball?",
                "How do you approach this classic logic puzzle?",
                listOf(
                    ProblemSolvingOption("Work backwards from the goal and plan each weighing strategically", ProblemSolvingType.ANALYTICAL, 5),
                    ProblemSolvingOption("Use a systematic elimination process with clear steps", ProblemSolvingType.SYSTEMATIC, 4),
                    ProblemSolvingOption("Try different grouping strategies until I find one that works", ProblemSolvingType.CREATIVE, 3),
                    ProblemSolvingOption("The solution pattern becomes clear once I think about it", ProblemSolvingType.INTUITIVE, 3)
                ),
                QuestionType.LOGIC_PUZZLE,
                isTimedQuestion = true,
                timeLimitSeconds = 90
            ),
            
            ProblemSolvingQuestion(
                10,
                "You're mediating a conflict between two departments with opposing goals.",
                "How do you approach finding a resolution?",
                listOf(
                    ProblemSolvingOption("Analyze each side's underlying interests and find logical compromises", ProblemSolvingType.ANALYTICAL, 4),
                    ProblemSolvingOption("Use a structured conflict resolution process", ProblemSolvingType.SYSTEMATIC, 4),
                    ProblemSolvingOption("Find creative solutions that reframe the problem entirely", ProblemSolvingType.CREATIVE, 5),
                    ProblemSolvingOption("Sense the emotional dynamics and address the real issues", ProblemSolvingType.INTUITIVE, 4)
                ),
                QuestionType.SCENARIO_BASED
            ),
            
            // Innovation and creativity
            ProblemSolvingQuestion(
                11,
                "Your organization needs to adapt to a major industry disruption.",
                "How do you approach developing an adaptation strategy?",
                listOf(
                    ProblemSolvingOption("Conduct thorough market analysis and competitive research", ProblemSolvingType.ANALYTICAL, 4),
                    ProblemSolvingOption("Follow established change management frameworks", ProblemSolvingType.SYSTEMATIC, 3),
                    ProblemSolvingOption("Explore radical new business models and approaches", ProblemSolvingType.CREATIVE, 5),
                    ProblemSolvingOption("Anticipate where the industry is heading based on emerging patterns", ProblemSolvingType.INTUITIVE, 4)
                ),
                QuestionType.CREATIVE_CHALLENGE
            ),
            
            ProblemSolvingQuestion(
                12,
                "Final Challenge: You have 30 seconds to come up with as many uses as possible for a paperclip.",
                "What's your approach to this creative thinking exercise?",
                listOf(
                    ProblemSolvingOption("Systematically consider different categories of uses", ProblemSolvingType.SYSTEMATIC, 4),
                    ProblemSolvingOption("Analyze the physical properties and derive logical applications", ProblemSolvingType.ANALYTICAL, 3),
                    ProblemSolvingOption("Let my imagination run wild with unusual and creative ideas", ProblemSolvingType.CREATIVE, 5),
                    ProblemSolvingOption("Quickly list whatever uses come to mind first", ProblemSolvingType.INTUITIVE, 4)
                ),
                QuestionType.CREATIVE_CHALLENGE,
                isTimedQuestion = true,
                timeLimitSeconds = 30
            )
        ))
    }
    
    override fun startQuestion() {
        // This method is called by BaseTestActivity but we handle question loading differently
        // in the new navigation pattern, so we can leave this empty
    }

    
    override fun getTestType(): TestType = TestType.PERSONALITY
    
    override fun calculateScore(): Int {
        // Return the dominant problem-solving style percentage
        val totalScore = analyticalScore + intuitiveScore + systematicScore + creativeScore
        val maxScore = maxOf(analyticalScore, intuitiveScore, systematicScore, creativeScore)
        return if (totalScore > 0) (maxScore * 100) / totalScore else 0
    }
    
    override fun generateDetailedScores(): Map<String, Double> {
        val totalScore = analyticalScore + intuitiveScore + systematicScore + creativeScore
        
        return if (totalScore > 0) {
            mapOf(
                "Analytical" to (analyticalScore.toDouble() / totalScore * 100),
                "Intuitive" to (intuitiveScore.toDouble() / totalScore * 100),
                "Systematic" to (systematicScore.toDouble() / totalScore * 100),
                "Creative" to (creativeScore.toDouble() / totalScore * 100)
            )
        } else {
            mapOf(
                "Analytical" to 25.0,
                "Intuitive" to 25.0,
                "Systematic" to 25.0,
                "Creative" to 25.0
            )
        }
    }
    
    override fun generateInsights(): List<String> {
        val insights = mutableListOf<String>()
        val detailedScores = generateDetailedScores()
        val dominantStyle = detailedScores.maxByOrNull { it.value }
        
        dominantStyle?.let { (style, percentage) ->
            when (style) {
                "Analytical" -> insights.add("You are primarily an Analytical problem solver (${percentage.toInt()}%) - you excel at logical reasoning and data-driven decisions")
                "Intuitive" -> insights.add("You are primarily an Intuitive problem solver (${percentage.toInt()}%) - you trust your instincts and recognize patterns quickly")
                "Systematic" -> insights.add("You are primarily a Systematic problem solver (${percentage.toInt()}%) - you prefer structured approaches and methodical processes")
                "Creative" -> insights.add("You are primarily a Creative problem solver (${percentage.toInt()}%) - you excel at innovative thinking and unconventional solutions")
                else -> insights.add("You have a balanced problem-solving approach (${percentage.toInt()}%)")
            }
        }
        
        // Check for balanced approaches
        val balancedStyles = detailedScores.filter { it.value >= 20 }
        if (balancedStyles.size > 2) {
            insights.add("You have a versatile problem-solving style, effectively combining ${balancedStyles.keys.joinToString(", ")} approaches")
        }
        
        // Analytical vs Intuitive balance
        val analyticalVsIntuitive = detailedScores["Analytical"]!! - detailedScores["Intuitive"]!!
        when {
            analyticalVsIntuitive > 20 -> insights.add("You strongly favor logical analysis over intuitive decision-making")
            analyticalVsIntuitive < -20 -> insights.add("You strongly favor intuitive insights over detailed analysis")
            else -> insights.add("You maintain a good balance between analytical thinking and intuitive insights")
        }
        
        return insights
    }
    
    override fun generateRecommendations(): List<String> {
        val recommendations = mutableListOf<String>()
        val detailedScores = generateDetailedScores()
        
        // Recommendations based on dominant style
        val dominantStyle = detailedScores.maxByOrNull { it.value }?.key
        
        when (dominantStyle) {
            "Analytical" -> {
                recommendations.add("Leverage your analytical strengths in data-heavy decisions")
                recommendations.add("Practice trusting your intuition occasionally for faster decisions")
            }
            "Intuitive" -> {
                recommendations.add("Trust your pattern recognition abilities in complex situations")
                recommendations.add("Validate important intuitive decisions with some analytical backing")
            }
            "Systematic" -> {
                recommendations.add("Use your methodical approach for complex, multi-step problems")
                recommendations.add("Allow for some flexibility and creative exploration in your processes")
            }
            "Creative" -> {
                recommendations.add("Apply your innovative thinking to breakthrough challenges")
                recommendations.add("Combine creativity with systematic implementation for best results")
            }
        }
        
        // General recommendations
        recommendations.add("Develop skills in your weaker problem-solving styles for versatility")
        recommendations.add("Match your problem-solving approach to the type of challenge you're facing")
        recommendations.add("Collaborate with people who have complementary problem-solving styles")
        
        return recommendations
    }
    
    override fun showInstructions() {
        // Instructions are now shown on the info page, not during the test
    }

    override fun loadQuestion(questionNumber: Int) {
        if (questionNumber <= questions.size) {
            currentQuestionIndex = questionNumber - 1
            val question = questions[currentQuestionIndex]

            binding.apply {
                // Update progress
                progressBar.progress = questionNumber
                progressText.text = "Question $questionNumber of ${questions.size}"

                // Set question text (clean, no labels)
                questionText.text = question.problemStatement

                // Clear previous content
                questionContent.removeAllViews()

                // Create radio group for options
                val radioGroup = RadioGroup(this@ProblemSolvingStyleActivity).apply {
                    orientation = RadioGroup.VERTICAL
                    setPadding(0, 16, 0, 16)
                }

                // Add radio buttons for each option
                question.options.forEachIndexed { index, option ->
                    val radioButton = RadioButton(this@ProblemSolvingStyleActivity).apply {
                        text = option.text
                        textSize = 16f
                        setTextColor(ContextCompat.getColor(context, R.color.text_primary))
                        setPadding(16, 12, 16, 12)
                        id = index
                    }
                    radioGroup.addView(radioButton)
                }

                // Restore saved answer if exists
                val savedAnswer = getSavedAnswer() as? Int
                if (savedAnswer != null && savedAnswer < radioGroup.childCount) {
                    (radioGroup.getChildAt(savedAnswer) as RadioButton).isChecked = true
                }

                // Set selection listener
                radioGroup.setOnCheckedChangeListener { _, checkedId ->
                    if (checkedId != -1) {
                        saveCurrentAnswer(checkedId)
                        updateNavigationButtons()
                    }
                }

                questionContent.addView(radioGroup)
                updateNavigationButtons()
            }
        }
    }

    override fun updateNavigationButtons() {
        binding.apply {
            // Previous button visibility
            btnPrevious.visibility = if (currentQuestion > 1) View.VISIBLE else View.GONE

            // Next/Finish button
            if (currentQuestion == totalQuestions) {
                btnNext.text = "Finish"
                btnNext.setOnClickListener { finishTest() }
            } else {
                btnNext.text = "Next"
                btnNext.setOnClickListener { goToNextQuestion() }
            }

            // Enable/disable based on answer
            val hasAnswer = getSavedAnswer() != null
            btnNext.isEnabled = hasAnswer
            btnNext.alpha = if (hasAnswer) 1.0f else 0.5f
        }
    }

    override fun finishTest() {
        // Calculate scores from all answers
        calculateFinalScores()
        completeTest()
    }

    private fun calculateFinalScores() {
        // Reset scores
        analyticalScore = 0
        intuitiveScore = 0
        systematicScore = 0
        creativeScore = 0

        // Calculate scores based on user answers
        userAnswers.forEach { (questionIndex, answerIndex) ->
            if (questionIndex < questions.size && answerIndex is Int) {
                val question = questions[questionIndex]
                if (answerIndex < question.options.size) {
                    val selectedOption = question.options[answerIndex]
                    when (selectedOption.styleType) {
                        ProblemSolvingType.ANALYTICAL -> analyticalScore += selectedOption.score
                        ProblemSolvingType.INTUITIVE -> intuitiveScore += selectedOption.score
                        ProblemSolvingType.SYSTEMATIC -> systematicScore += selectedOption.score
                        ProblemSolvingType.CREATIVE -> creativeScore += selectedOption.score
                    }
                }
            }
        }
    }
}
