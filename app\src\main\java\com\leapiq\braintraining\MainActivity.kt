package com.leapiq.braintraining

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.leapiq.braintraining.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Check if this is being called from onboarding flow
        val fromOnboarding = intent.getBooleanExtra("from_onboarding", false)

        // Only install splash screen if this is the initial app launch (not from onboarding)
        if (!fromOnboarding) {
            val splashScreen = installSplashScreen()
        }

        // Check if onboarding is complete
        val sharedPreferences = getSharedPreferences("leapiq_prefs", MODE_PRIVATE)
        val isOnboardingComplete = sharedPreferences.getBoolean("onboarding_complete", false)

        if (!isOnboardingComplete && !fromOnboarding) {
            // Navigate to welcome flow
            startActivity(Intent(this, WelcomeActivity::class.java))
            finish()
            return
        }

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupBottomNavigation()

        // Handle navigation from game results and notifications
        handleNavigationIntent()
    }

    private fun setupBottomNavigation() {
        val navHostFragment = supportFragmentManager
            .findFragmentById(R.id.nav_host_fragment) as NavHostFragment
        val navController = navHostFragment.navController

        val bottomNav = findViewById<BottomNavigationView>(R.id.bottom_navigation)
        bottomNav.setupWithNavController(navController)

        // Optimize navigation transitions
        navController.addOnDestinationChangedListener { _, destination, _ ->
            // Disable animation for smoother transitions
            bottomNav.itemIconTintList = null

            // Optional: Add custom transition handling here if needed
            when (destination.id) {
                R.id.navigation_today -> {
                    // Today page specific optimizations
                }
                R.id.navigation_games -> {
                    // Games page specific optimizations
                }
                R.id.navigation_tests -> {
                    // Tests page specific optimizations
                }
                R.id.navigation_progress -> {
                    // Progress page specific optimizations
                }
            }
        }
    }

    private fun handleNavigationIntent() {
        val navigateTo = intent.getStringExtra("navigate_to")
        if (navigateTo != null) {
            val navHostFragment = supportFragmentManager
                .findFragmentById(R.id.nav_host_fragment) as NavHostFragment
            val navController = navHostFragment.navController

            when (navigateTo) {
                "today" -> navController.navigate(R.id.navigation_today)
                "games" -> navController.navigate(R.id.navigation_games)
                "tests" -> navController.navigate(R.id.navigation_tests)
                "progress" -> navController.navigate(R.id.navigation_progress)
            }
        }
    }
}
