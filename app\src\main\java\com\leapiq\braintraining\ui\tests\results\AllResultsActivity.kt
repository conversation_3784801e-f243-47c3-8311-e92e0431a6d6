package com.leapiq.braintraining.ui.tests.results

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.leapiq.braintraining.databinding.ActivityAllResultsBinding
import com.leapiq.braintraining.data.TestResultsRepository
import com.leapiq.braintraining.data.TestProgressManager
import com.leapiq.braintraining.ui.tests.results.adapters.AllResultsAdapter
import com.google.gson.Gson

/**
 * Activity to display all test results in a comprehensive list
 */
class AllResultsActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityAllResultsBinding
    private lateinit var repository: TestResultsRepository
    private lateinit var progressManager: TestProgressManager
    private lateinit var allResultsAdapter: AllResultsAdapter
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAllResultsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        repository = TestResultsRepository.getInstance(this)
        progressManager = TestProgressManager.getInstance(this)
        
        setupUI()
        loadAllResults()
    }
    
    private fun setupUI() {
        binding.apply {
            // Setup toolbar
            toolbar.setNavigationOnClickListener { finish() }
            
            // Setup RecyclerView
            allResultsAdapter = AllResultsAdapter { testResult ->
                // Open detailed result view
                val intent = Intent(this@AllResultsActivity, TestResultsActivity::class.java).apply {
                    putExtra(TestResultsActivity.EXTRA_TEST_RESULT, Gson().toJson(testResult))
                }
                startActivity(intent)
            }
            
            resultsRecycler.apply {
                layoutManager = LinearLayoutManager(this@AllResultsActivity)
                adapter = allResultsAdapter
            }
        }
    }
    
    private fun loadAllResults() {
        val allResults = progressManager.getAllTestResults()
        val resultsList = mutableListOf<TestResultItem>()
        
        allResults.forEach { (testId, results) ->
            results.forEach { result ->
                resultsList.add(
                    TestResultItem(
                        testId = testId,
                        testName = getTestName(testId),
                        result = result
                    )
                )
            }
        }
        
        // Sort by completion date (most recent first)
        val sortedResults = resultsList.sortedByDescending { it.result.completedAt }
        allResultsAdapter.submitList(sortedResults)
        
        // Update empty state
        if (sortedResults.isEmpty()) {
            binding.emptyStateContainer.visibility = android.view.View.VISIBLE
            binding.resultsRecycler.visibility = android.view.View.GONE
        } else {
            binding.emptyStateContainer.visibility = android.view.View.GONE
            binding.resultsRecycler.visibility = android.view.View.VISIBLE
        }
    }
    
    private fun getTestName(testId: String): String {
        return when (testId) {
            "memory_assessment" -> "Memory Assessment"
            "attention_test" -> "Attention Test"
            "processing_speed" -> "Processing Speed"
            "learning_style" -> "Learning Style"
            "stress_response" -> "Stress Response"
            "problem_solving" -> "Problem Solving Style"
            else -> "Unknown Test"
        }
    }
}

/**
 * Data class for test result items
 */
data class TestResultItem(
    val testId: String,
    val testName: String,
    val result: com.leapiq.braintraining.data.model.TestResult
)
