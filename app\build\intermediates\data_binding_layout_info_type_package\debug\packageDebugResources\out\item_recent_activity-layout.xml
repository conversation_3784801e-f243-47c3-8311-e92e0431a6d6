<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_recent_activity" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\item_recent_activity.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_recent_activity_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="64" endOffset="51"/></Target><Target id="@+id/activity_icon" view="TextView"><Expressions/><location startLine="20" startOffset="8" endLine="26" endOffset="47"/></Target><Target id="@+id/test_name_text" view="TextView"><Expressions/><location startLine="29" startOffset="8" endLine="40" endOffset="47"/></Target><Target id="@+id/score_text" view="TextView"><Expressions/><location startLine="43" startOffset="8" endLine="51" endOffset="47"/></Target><Target id="@+id/date_text" view="TextView"><Expressions/><location startLine="54" startOffset="8" endLine="60" endOffset="37"/></Target></Targets></Layout>