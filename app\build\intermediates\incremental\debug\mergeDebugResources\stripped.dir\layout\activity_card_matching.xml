<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.games.CardMatchingActivity">

    <!-- Game Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:background="@color/primary_light_blue_light">

        <!-- Title Row with Quit Button -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <ImageButton
                android:id="@+id/btn_quit"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_close"
                android:contentDescription="Quit Game"
                android:layout_marginEnd="16dp" />

            <TextView
                android:id="@+id/game_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Card Matching"
                android:textColor="@color/text_primary"
                android:textSize="20sp"
                android:textStyle="bold"
                android:gravity="center"
                tools:text="Card Matching" />

            <!-- Menu Button -->
            <ImageButton
                android:id="@+id/btn_menu"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:src="@drawable/ic_menu"
                android:contentDescription="Game Menu"
                android:layout_marginStart="16dp" />

        </LinearLayout>

        <!-- Level and Round Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/level_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Level 1"
                android:textColor="@color/primary_light_blue"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginEnd="16dp"
                tools:text="Level 1" />

            <TextView
                android:id="@+id/round_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Round 1/5"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                tools:text="Round 1/5" />

        </LinearLayout>

        <!-- Instructions -->
        <TextView
            android:id="@+id/instruction_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Find matching pairs by tapping cards. Remember their positions!"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:gravity="center"
            android:layout_marginBottom="8dp"
            tools:text="Find matching pairs by tapping cards. Remember their positions!" />

    </LinearLayout>

    <!-- Game Area -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="16dp">

        <!-- Card Grid -->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/card_grid"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    tools:listitem="@layout/item_memory_card" />

            </FrameLayout>

        </ScrollView>

    </FrameLayout>

</LinearLayout>
