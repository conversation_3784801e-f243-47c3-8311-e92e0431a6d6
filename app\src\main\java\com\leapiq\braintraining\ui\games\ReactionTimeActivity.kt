package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityReactionTimeBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Reaction Time Game
 * Simple mode: Click when stimulus appears
 * Choice mode: Respond only to specific stimuli (colors/shapes)
 * Measures millisecond precision reaction times
 */
class ReactionTimeActivity : AppCompatActivity() {

    private lateinit var binding: ActivityReactionTimeBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "attention_2"

    // Reaction time specific
    private var isSimpleMode = true
    private var stimulusStartTime = 0L
    private var isWaitingForStimulus = false
    private var isWaitingForResponse = false
    private var currentTrial = 0
    private var trialsPerRound = 15
    private var reactionTimes = mutableListOf<Long>()
    
    // Choice mode variables
    private var targetColor = ""
    private var currentStimulusColor = ""
    private val colors = listOf("RED", "BLUE", "GREEN", "YELLOW")
    private val colorResources = mapOf(
        "RED" to R.color.stroop_red,
        "BLUE" to R.color.stroop_blue,
        "GREEN" to R.color.stroop_green,
        "YELLOW" to R.color.stroop_yellow
    )

    // Dynamic visual variables
    private var currentRoundColor = ""
    private val dynamicColors = listOf(
        "#2196F3", // Blue
        "#4CAF50", // Green
        "#F44336", // Red
        "#FF9800", // Orange
        "#9C27B0", // Purple
        "#00BCD4", // Cyan
        "#FFEB3B", // Yellow
        "#E91E63"  // Pink
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityReactionTimeBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.reaction_time)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup reaction area click
            reactionArea.setOnClickListener {
                onReactionAreaClicked()
            }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentTrial = 0
        reactionTimes.clear()

        // Generate dynamic color for this round
        generateRoundColor()

        // Determine mode based on level
        isSimpleMode = currentLevel <= 10
        trialsPerRound = getTrialsPerRound(currentLevel)

        if (isSimpleMode) {
            binding.instructionText.text = "Click the circle as soon as it appears!"
            binding.modeText.text = "SIMPLE MODE"
        } else {
            targetColor = colors.random()
            binding.instructionText.text = "Click only when the circle is $targetColor!"
            binding.modeText.text = "CHOICE MODE - Target: $targetColor"
        }

        binding.trialText.text = "Trial 1/$trialsPerRound"

        startNextTrial()
    }

    private fun getTrialsPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 15      // 15 trials per round
            in 6..10 -> 18     // 18 trials per round
            in 11..15 -> 20    // 20 trials per round
            in 16..20 -> 22    // 22 trials per round
            else -> 25         // 25 trials per round
        }
    }

    private fun startNextTrial() {
        if (currentTrial >= trialsPerRound) {
            roundComplete()
            return
        }

        currentTrial++
        binding.trialText.text = "Trial $currentTrial/$trialsPerRound"
        
        // Hide stimulus
        binding.stimulusCircle.visibility = android.view.View.INVISIBLE
        binding.instructionText.text = "Wait for it..."
        
        isWaitingForStimulus = true
        isWaitingForResponse = false
        
        // Random delay before showing stimulus
        val delay = getRandomDelay(currentLevel)
        Handler(Looper.getMainLooper()).postDelayed({
            showStimulus()
        }, delay)
    }

    private fun getRandomDelay(level: Int): Long {
        val baseMin = when (level) {
            in 1..5 -> 1000L   // 1-3 seconds
            in 6..10 -> 800L   // 0.8-2.5 seconds
            in 11..15 -> 600L  // 0.6-2 seconds
            in 16..20 -> 400L  // 0.4-1.5 seconds
            else -> 200L       // 0.2-1 second
        }
        val baseMax = baseMin * 3
        return Random.nextLong(baseMin, baseMax)
    }

    private fun showStimulus() {
        if (!isWaitingForStimulus) return
        
        isWaitingForStimulus = false
        isWaitingForResponse = true
        stimulusStartTime = System.currentTimeMillis()
        
        // Set stimulus color
        if (isSimpleMode) {
            currentStimulusColor = "BLUE" // Always blue in simple mode
        } else {
            // In choice mode, sometimes show target, sometimes distractor
            val showTarget = Random.nextFloat() < 0.6f // 60% target trials
            currentStimulusColor = if (showTarget) targetColor else colors.filter { it != targetColor }.random()
        }
        
        // Apply dynamic color and show stimulus with animation
        if (isSimpleMode) {
            // Use dynamic round color in simple mode
            val dynamicDrawable = createDynamicDrawable(currentRoundColor)
            binding.stimulusCircle.background = dynamicDrawable
        } else {
            // Use traditional colors in choice mode for clarity
            val colorRes = colorResources[currentStimulusColor] ?: R.color.stroop_blue
            val color = ContextCompat.getColor(this, colorRes)
            binding.stimulusCircle.backgroundTintList = android.content.res.ColorStateList.valueOf(color)
        }

        binding.stimulusCircle.visibility = android.view.View.VISIBLE
        animateTargetAppearance()
        
        // Update instruction
        if (isSimpleMode) {
            binding.instructionText.text = "CLICK NOW!"
        } else {
            binding.instructionText.text = if (currentStimulusColor == targetColor) {
                "CLICK NOW! ($currentStimulusColor)"
            } else {
                "DON'T CLICK! ($currentStimulusColor)"
            }
        }
        
        // Auto-hide stimulus after timeout
        Handler(Looper.getMainLooper()).postDelayed({
            if (isWaitingForResponse) {
                timeoutTrial()
            }
        }, getStimulusTimeout(currentLevel))
    }

    private fun getStimulusTimeout(level: Int): Long {
        return when (level) {
            in 1..5 -> 2000L   // 2 seconds
            in 6..10 -> 1500L  // 1.5 seconds
            in 11..15 -> 1200L // 1.2 seconds
            in 16..20 -> 1000L // 1 second
            else -> 800L       // 0.8 seconds
        }
    }

    private fun onReactionAreaClicked() {
        if (isWaitingForStimulus) {
            // False start
            falseStart()
            return
        }
        
        if (!isWaitingForResponse) {
            return // No stimulus active
        }
        
        val reactionTime = System.currentTimeMillis() - stimulusStartTime
        isWaitingForResponse = false
        totalAttempts++
        
        val shouldHaveClicked = isSimpleMode || (currentStimulusColor == targetColor)
        
        if (shouldHaveClicked) {
            // Correct response
            totalCorrect++
            reactionTimes.add(reactionTime)
            showTrialFeedback(true, reactionTime)
        } else {
            // False alarm (clicked when shouldn't have)
            showTrialFeedback(false, reactionTime, "False alarm!")
        }
        
        // Hide stimulus with animation
        animateTargetDisappearance()
        
        // Continue to next trial
        Handler(Looper.getMainLooper()).postDelayed({
            startNextTrial()
        }, 1000)
    }

    private fun timeoutTrial() {
        isWaitingForResponse = false
        totalAttempts++
        
        val shouldHaveClicked = isSimpleMode || (currentStimulusColor == targetColor)
        
        if (shouldHaveClicked) {
            // Miss - should have clicked but didn't
            showTrialFeedback(false, 0, "Too slow!")
        } else {
            // Correct rejection - correctly didn't click
            totalCorrect++
            showTrialFeedback(true, 0, "Correct rejection!")
        }
        
        // Hide stimulus with animation
        animateTargetDisappearance()
        
        // Continue to next trial
        Handler(Looper.getMainLooper()).postDelayed({
            startNextTrial()
        }, 1000)
    }

    private fun falseStart() {
        isWaitingForStimulus = false
        totalAttempts++
        
        binding.instructionText.text = "Too early! Wait for the stimulus."
        binding.stimulusCircle.visibility = android.view.View.INVISIBLE
        
        Handler(Looper.getMainLooper()).postDelayed({
            startNextTrial()
        }, 1500)
    }

    private fun showTrialFeedback(isCorrect: Boolean, reactionTime: Long, customMessage: String? = null) {
        val feedback = customMessage ?: if (isCorrect) {
            if (reactionTime > 0) "Correct! ${reactionTime}ms" else "Correct!"
        } else {
            if (reactionTime > 0) "Wrong! ${reactionTime}ms" else "Wrong!"
        }
        binding.instructionText.text = feedback
    }

    private fun roundComplete() {
        val avgReactionTime = if (reactionTimes.isNotEmpty()) {
            reactionTimes.average().toLong()
        } else 0L
        
        val roundAccuracy = if (trialsPerRound > 0) {
            (totalCorrect.toDouble() / trialsPerRound)
        } else 1.0
        
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Accuracy: ${(roundAccuracy * 100).toInt()}%
                Avg RT: ${avgReactionTime}ms
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentTrial = 0
            reactionTimes.clear()
            
            Handler(Looper.getMainLooper()).postDelayed({
                setupGame()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0
        val avgReactionTime = if (reactionTimes.isNotEmpty()) reactionTimes.average().toLong() else 0L

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7, // 70% threshold
                timeSpentMs = totalTime / maxRounds,
                attempts = trialsPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = if (avgReactionTime > 0) (1000 / avgReactionTime * accuracy * 100).toInt() else (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Avg Reaction Time: ${avgReactionTime}ms
                Mode: ${if (isSimpleMode) "Simple" else "Choice"}

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Best Avg RT: ${avgReactionTime}ms

                Reaction Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Test your reaction speed and attention

                📋 MODES:
                • SIMPLE (Levels 1-10): Click when circle appears
                • CHOICE (Levels 11+): Click only for target color

                💡 RULES:
                • Wait for the stimulus to appear
                • Don't click too early (false start)
                • In choice mode, ignore wrong colors
                • Faster reactions = better scores

                🏆 SCORING:
                • Accuracy = correct responses / total trials
                • Speed = average reaction time in milliseconds
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun generateRoundColor() {
        currentRoundColor = dynamicColors.random()
    }

    private fun createDynamicDrawable(color: String): android.graphics.drawable.GradientDrawable {
        val baseColor = android.graphics.Color.parseColor(color)
        val darkerColor = darkenColor(baseColor, 0.3f)

        return android.graphics.drawable.GradientDrawable().apply {
            shape = android.graphics.drawable.GradientDrawable.OVAL
            setColor(baseColor)
            cornerRadius = 80f
            setStroke(12, darkerColor) // Thick border for visibility
        }
    }

    private fun darkenColor(color: Int, factor: Float): Int {
        val hsv = FloatArray(3)
        android.graphics.Color.colorToHSV(color, hsv)
        hsv[2] *= (1f - factor) // Reduce brightness
        return android.graphics.Color.HSVToColor(hsv)
    }

    private fun animateTargetAppearance() {
        // Scale animation for dramatic appearance
        binding.stimulusCircle.scaleX = 0.3f
        binding.stimulusCircle.scaleY = 0.3f
        binding.stimulusCircle.alpha = 0.7f

        binding.stimulusCircle.animate()
            .scaleX(1.0f)
            .scaleY(1.0f)
            .alpha(1.0f)
            .setDuration(200)
            .start()

        // Pulse effect
        binding.pulseEffect.alpha = 0.6f
        binding.pulseEffect.scaleX = 0.8f
        binding.pulseEffect.scaleY = 0.8f
        binding.pulseEffect.visibility = android.view.View.VISIBLE

        binding.pulseEffect.animate()
            .alpha(0.0f)
            .scaleX(1.2f)
            .scaleY(1.2f)
            .setDuration(400)
            .withEndAction {
                binding.pulseEffect.visibility = android.view.View.INVISIBLE
            }
            .start()
    }

    private fun animateTargetDisappearance() {
        binding.stimulusCircle.animate()
            .scaleX(0.8f)
            .scaleY(0.8f)
            .alpha(0.0f)
            .setDuration(150)
            .withEndAction {
                binding.stimulusCircle.visibility = android.view.View.INVISIBLE
                // Reset for next appearance
                binding.stimulusCircle.scaleX = 1.0f
                binding.stimulusCircle.scaleY = 1.0f
                binding.stimulusCircle.alpha = 1.0f
            }
            .start()
    }
}
