<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_logical_reasoning" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_logical_reasoning.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_logical_reasoning_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="272" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/trial_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/puzzle_type_text" view="TextView"><Expressions/><location startLine="103" startOffset="4" endLine="113" endOffset="34"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="116" startOffset="4" endLine="125" endOffset="33"/></Target><Target id="@+id/premises_text" view="TextView"><Expressions/><location startLine="150" startOffset="12" endLine="161" endOffset="84"/></Target><Target id="@+id/question_text" view="TextView"><Expressions/><location startLine="173" startOffset="12" endLine="183" endOffset="52"/></Target><Target id="@+id/btn_option1" view="Button"><Expressions/><location startLine="204" startOffset="12" endLine="216" endOffset="57"/></Target><Target id="@+id/btn_option2" view="Button"><Expressions/><location startLine="218" startOffset="12" endLine="230" endOffset="57"/></Target><Target id="@+id/btn_option3" view="Button"><Expressions/><location startLine="240" startOffset="12" endLine="252" endOffset="62"/></Target><Target id="@+id/btn_option4" view="Button"><Expressions/><location startLine="254" startOffset="12" endLine="266" endOffset="56"/></Target></Targets></Layout>