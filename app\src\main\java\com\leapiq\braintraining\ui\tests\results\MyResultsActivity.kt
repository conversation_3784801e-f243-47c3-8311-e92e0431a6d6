package com.leapiq.braintraining.ui.tests.results

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.leapiq.braintraining.databinding.ActivityMyResultsBinding
import com.leapiq.braintraining.data.TestResultsRepository
import com.leapiq.braintraining.data.TestProgressManager
import com.leapiq.braintraining.ui.tests.results.adapters.TestProgressAdapter
import com.leapiq.braintraining.ui.tests.results.adapters.RecentActivityAdapter
import com.leapiq.braintraining.data.TestDashboardData
import com.leapiq.braintraining.data.InsightPriority
import com.leapiq.braintraining.data.model.TestProgress
import com.leapiq.braintraining.data.PerformanceTrend
import com.leapiq.braintraining.R

/**
 * Activity to display user's test results, progress, and analytics
 */
class MyResultsActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMyResultsBinding
    private lateinit var repository: TestResultsRepository
    private lateinit var progressManager: TestProgressManager
    private lateinit var testProgressAdapter: TestProgressAdapter
    private lateinit var recentActivityAdapter: RecentActivityAdapter
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMyResultsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        repository = TestResultsRepository.getInstance(this)
        progressManager = TestProgressManager.getInstance(this)
        
        setupUI()
        loadData()
    }
    
    private fun setupUI() {
        binding.apply {
            // Setup toolbar
            toolbar.setNavigationOnClickListener { finish() }
            
            // Setup test progress RecyclerView
            testProgressAdapter = TestProgressAdapter { testId ->
                openTestProgress(testId)
            }
            
            testProgressRecycler.apply {
                layoutManager = LinearLayoutManager(this@MyResultsActivity)
                adapter = testProgressAdapter
            }
            
            // Setup recent activity RecyclerView
            recentActivityAdapter = RecentActivityAdapter { testId ->
                openTestProgress(testId)
            }
            
            recentActivityRecycler.apply {
                layoutManager = LinearLayoutManager(this@MyResultsActivity, LinearLayoutManager.HORIZONTAL, false)
                adapter = recentActivityAdapter
            }
            
            // Setup action buttons
            btnViewAllResults.setOnClickListener {
                val intent = Intent(this@MyResultsActivity, AllResultsActivity::class.java)
                startActivity(intent)
            }
            
            btnExportData.setOnClickListener {
                exportTestData()
            }
            
            btnAnalytics.setOnClickListener {
                val intent = Intent(this@MyResultsActivity, AnalyticsActivity::class.java)
                startActivity(intent)
            }
        }
    }
    
    private fun loadData() {
        val dashboardData = repository.getDashboardData()
        
        binding.apply {
            // Update overview statistics
            totalTestsText.text = dashboardData.totalTestsCompleted.toString()
            cognitiveTestsText.text = dashboardData.cognitiveTestsCompleted.toString()
            personalityTestsText.text = dashboardData.personalityTestsCompleted.toString()
            
            // Update average scores
            if (dashboardData.averageCognitiveScore > 0) {
                avgCognitiveScoreText.text = "${dashboardData.averageCognitiveScore.toInt()}%"
                cognitiveScoreContainer.visibility = android.view.View.VISIBLE
            } else {
                cognitiveScoreContainer.visibility = android.view.View.GONE
            }
            
            if (dashboardData.averagePersonalityScore > 0) {
                avgPersonalityScoreText.text = "${dashboardData.averagePersonalityScore.toInt()}%"
                personalityScoreContainer.visibility = android.view.View.VISIBLE
            } else {
                personalityScoreContainer.visibility = android.view.View.GONE
            }
            
            // Update overall progress
            val overallProgress = dashboardData.overallProgress
            overallProgressBar.progress = overallProgress.completionPercentage.toInt()
            overallProgressText.text = "${overallProgress.completionPercentage.toInt()}% Complete"
            
            // Update progress details
            improvingTestsText.text = overallProgress.improvingTests.toString()
            stableTestsText.text = overallProgress.stableTests.toString()
            decliningTestsText.text = overallProgress.decliningTests.toString()
            
            // Load test progress data
            val allProgresses = progressManager.getAllTestProgresses()
            val progressList = allProgresses.map { (testId, progress) ->
                TestProgressItem(
                    testId = testId,
                    testName = getTestName(testId),
                    progress = progress,
                    trend = progressManager.getPerformanceTrends(testId)
                )
            }.filter { it.progress.timesCompleted > 0 }
            
            testProgressAdapter.submitList(progressList)
            
            // Load recent activity
            val recentActivity = dashboardData.recentActivity
            recentActivityAdapter.submitList(recentActivity)
            
            // Show/hide empty states
            if (progressList.isEmpty()) {
                emptyStateContainer.visibility = android.view.View.VISIBLE
                contentContainer.visibility = android.view.View.GONE
            } else {
                emptyStateContainer.visibility = android.view.View.GONE
                contentContainer.visibility = android.view.View.VISIBLE
            }
            
            // Update insights
            displayInsights(dashboardData)
        }
    }
    
    private fun displayInsights(dashboardData: TestDashboardData) {
        val insights = repository.getPersonalizedInsights()

        // Add dashboard-based insights
        val dashboardInsights = mutableListOf<String>()
        if (dashboardData.totalTestsCompleted > 10) {
            dashboardInsights.add("You've completed ${dashboardData.totalTestsCompleted} tests - great consistency!")
        }
        if (dashboardData.averageCognitiveScore > 80) {
            dashboardInsights.add("Your cognitive performance is excellent (${dashboardData.averageCognitiveScore.toInt()}%)")
        }

        if (insights.isNotEmpty()) {
            binding.insightsContainer.visibility = android.view.View.VISIBLE
            
            val topInsight = insights.first()
            binding.apply {
                insightTitle.text = topInsight.title
                insightDescription.text = topInsight.description
                insightAction.text = topInsight.actionable
                
                // Set insight priority color
                val priorityColor = when (topInsight.priority) {
                    InsightPriority.HIGH -> R.color.error_red
                    InsightPriority.MEDIUM -> R.color.warning_orange
                    InsightPriority.LOW -> R.color.success_green
                }
                insightPriorityIndicator.setBackgroundColor(getColor(priorityColor))
            }
        } else {
            binding.insightsContainer.visibility = android.view.View.GONE
        }
    }
    
    private fun openTestProgress(testId: String) {
        val intent = Intent(this, TestProgressActivity::class.java).apply {
            putExtra(TestProgressActivity.EXTRA_TEST_ID, testId)
        }
        startActivity(intent)
    }
    
    private fun exportTestData() {
        // TODO: Implement data export functionality
        // This could open a dialog to choose export format (JSON, CSV, etc.)
    }
    
    private fun getTestName(testId: String): String {
        return when (testId) {
            "memory_assessment" -> "Memory Assessment"
            "attention_test" -> "Attention Test"
            "processing_speed" -> "Processing Speed"
            "learning_style" -> "Learning Style"
            "stress_response" -> "Stress Response"
            "problem_solving" -> "Problem Solving Style"
            else -> "Unknown Test"
        }
    }
}

/**
 * Data class for test progress items
 */
data class TestProgressItem(
    val testId: String,
    val testName: String,
    val progress: TestProgress,
    val trend: PerformanceTrend
)
