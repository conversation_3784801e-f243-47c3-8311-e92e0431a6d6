<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="attention_color">#E8F5E8</color>
    <color name="background_light_gray">#F5F5F5</color>
    <color name="background_white">#FFFFFF</color>
    <color name="black">#FF000000</color>
    <color name="error_red">#F44336</color>
    <color name="ic_launcher_background">#FFFFFF</color>
    <color name="language_color">#FFEBEE</color>
    <color name="logic_color">#F3E5F5</color>
    <color name="math_color">#FFF3E0</color>
    <color name="memory_color">#E3F2FD</color>
    <color name="premium_gold">#FFD700</color>
    <color name="premium_gold_dark">#FFC107</color>
    <color name="primary_color">#2196F3</color>
    <color name="primary_light_blue">#2196F3</color>
    <color name="primary_light_blue_dark">#1976D2</color>
    <color name="primary_light_blue_light">#BBDEFB</color>
    <color name="search_orange">#FF9800</color>
    <color name="search_purple">#9C27B0</color>
    <color name="secondary_blue">#03DAC5</color>
    <color name="secondary_blue_variant">#018786</color>
    <color name="stroop_blue">#2196F3</color>
    <color name="stroop_green">#4CAF50</color>
    <color name="stroop_green_light">#E8F5E8</color>
    <color name="stroop_red">#F44336</color>
    <color name="stroop_red_light">#FFEBEE</color>
    <color name="stroop_yellow">#FFEB3B</color>
    <color name="success_green">#4CAF50</color>
    <color name="surface_white">#FFFFFF</color>
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="text_white">#FFFFFF</color>
    <color name="tube_cyan">#00BCD4</color>
    <color name="tube_pink">#E91E63</color>
    <color name="warning_orange">#FF9800</color>
    <color name="white">#FFFFFFFF</color>
    <string name="accuracy_result">%d%%</string>
    <string name="achievements">Achievements</string>
    <string name="all_games">All</string>
    <string name="all_tests">All</string>
    <string name="anagrams">Anagrams</string>
    <string name="app_name">LeapIQ</string>
    <string name="attention_games">Attention</string>
    <string name="card_matching">Card Matching</string>
    <string name="category_progress">Category Progress</string>
    <string name="cognitive_tests">Cognitive</string>
    <string name="continue_game">Continue</string>
    <string name="current_level">Level %d</string>
    <string name="current_streak">Current: %d days</string>
    <string name="daily_challenges">Daily Challenges</string>
    <string name="estimation">Estimation</string>
    <string name="focus_challenge">Focus Challenge</string>
    <string name="free">Free</string>
    <string name="games_played_week">Games Played: %d this week</string>
    <string name="language_games">Language</string>
    <string name="learning_style">Learning Style</string>
    <string name="level_completed">Level %d Completed</string>
    <string name="level_format">Level %d</string>
    <string name="level_locked">Level Locked</string>
    <string name="locked">Locked</string>
    <string name="logic_games">Logic</string>
    <string name="logic_puzzles">Logic Puzzles</string>
    <string name="logical_reasoning">Logical Reasoning</string>
    <string name="math_games">Math</string>
    <string name="max_streak">Max Streak: %d days</string>
    <string name="memory_games">Memory</string>
    <string name="mental_arithmetic">Mental Arithmetic</string>
    <string name="nav_games">Games</string>
    <string name="nav_progress">Progress</string>
    <string name="nav_tests">Tests</string>
    <string name="nav_today">Today</string>
    <string name="next_level">Next Level (%d)</string>
    <string name="number_memory">Number Memory</string>
    <string name="number_sequences">Number Sequences</string>
    <string name="pattern_completion">Pattern Completion</string>
    <string name="pattern_memory">Pattern Memory</string>
    <string name="personality_tests">Personality</string>
    <string name="play">Play</string>
    <string name="premium">Premium</string>
    <string name="premium_only">Premium</string>
    <string name="price_placeholder">$0.00</string>
    <string name="problem_solving_style">Problem Solving Style</string>
    <string name="reaction_time">Reaction Time</string>
    <string name="retry_level">Retry Level</string>
    <string formatted="false" name="round_format">Round %d/%d</string>
    <string formatted="false" name="rounds_result">%d/%d</string>
    <string name="rule_discovery">Rule Discovery</string>
    <string name="score_result">%d</string>
    <string name="sequence_recall">Sequence Recall</string>
    <string name="settings">Settings</string>
    <string name="shape_matching">Shape Matching</string>
    <string name="spatial_rotation">Spatial Rotation</string>
    <string name="speed_math">Speed Math</string>
    <string name="start">Start</string>
    <string name="start_challenge">Start Challenge</string>
    <string name="streak">Streak</string>
    <string name="stress_response">Stress Response</string>
    <string name="stroop_test">Stroop Test</string>
    <string name="time_result">%s</string>
    <string name="total_score">Total Score</string>
    <string name="tower_of_hanoi">Tower of Hanoi</string>
    <string name="tube_sort">Tube Sort</string>
    <string name="visual_search">Visual Search</string>
    <string name="vocabulary">Vocabulary</string>
    <string name="welcome_user">Welcome User</string>
    <string name="word_association">Word Association</string>
    <string name="word_search">Word Search</string>
    <style name="LeapIQ">
        
    </style>
    <style name="LeapIQ.GameCard">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:background">@drawable/game_card_background</item>
        <item name="android:elevation">2dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>
    <style name="LeapIQ.GameTitle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">8dp</item>
    </style>
    <style name="LeapIQ.Header">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:background">@color/primary_light_blue</item>
        <item name="android:elevation">4dp</item>
    </style>
    <style name="LeapIQ.HeaderText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_white</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    <style name="Theme.LeapIQ" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/primary_light_blue</item>
        <item name="colorPrimaryVariant">@color/primary_light_blue_dark</item>
        <item name="colorOnPrimary">@color/text_white</item>
        
        
        <item name="colorSecondary">@color/secondary_blue</item>
        <item name="colorSecondaryVariant">@color/secondary_blue_variant</item>
        <item name="colorOnSecondary">@color/text_primary</item>
        
        
        <item name="android:colorBackground">@color/background_white</item>
        <item name="colorSurface">@color/surface_white</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        
        <item name="android:statusBarColor">@color/primary_light_blue_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        
        
        <item name="android:navigationBarColor">@color/background_white</item>
        <item name="android:windowLightNavigationBar">true</item>
    </style>
</resources>