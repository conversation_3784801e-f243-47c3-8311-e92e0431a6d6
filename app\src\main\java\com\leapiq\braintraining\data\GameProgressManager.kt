package com.leapiq.braintraining.data

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.leapiq.braintraining.data.model.*
import java.util.Date

/**
 * Manages local game progress storage using SharedPreferences
 * Handles saving/loading game results and calculating progress metrics
 */
class GameProgressManager(context: Context) {

    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    companion object {
        private const val PREFS_NAME = "game_progress"
        private const val KEY_USER_PROGRESS = "user_progress"
        private const val KEY_GAME_PROGRESSES = "game_progresses"
        private const val KEY_LEVEL_RESULTS = "level_results"
        
        @Volatile
        private var INSTANCE: GameProgressManager? = null
        
        fun getInstance(context: Context): GameProgressManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: GameProgressManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * Save a completed level result and update progress
     */
    fun saveLevelResult(levelResult: LevelResult) {
        // Save the level result
        val existingResults = getLevelResults(levelResult.gameId).toMutableList()
        existingResults.add(levelResult)
        saveLevelResults(levelResult.gameId, existingResults)
        
        // Update game progress
        updateGameProgress(levelResult.gameId, levelResult)
        
        // Update overall user progress
        updateUserProgress()
    }
    
    /**
     * Get current progress for a specific game
     */
    fun getGameProgress(gameId: String): GameProgress {
        return GameProgress(
            gameId = gameId,
            currentLevel = prefs.getInt("${gameId}_current_level", 1),
            highestLevel = prefs.getInt("${gameId}_highest_level", 1),
            totalLevelsCompleted = prefs.getInt("${gameId}_total_completed", 0),
            bestAccuracy = prefs.getFloat("${gameId}_best_accuracy", 0.0f).toDouble(),
            averageAccuracy = prefs.getFloat("${gameId}_avg_accuracy", 0.0f).toDouble(),
            totalTimePlayed = prefs.getLong("${gameId}_total_time", 0L),
            lastPlayedAt = null // Simplified for now
        )
    }
    
    /**
     * Get overall user progress
     */
    fun getUserProgress(): UserProgress {
        return UserProgress(
            totalScore = prefs.getInt("total_score", 0),
            overallAccuracy = prefs.getFloat("overall_accuracy", 0.0f).toDouble(),
            currentStreak = prefs.getInt("current_streak", 0),
            maxStreak = prefs.getInt("max_streak", 0),
            userLevel = prefs.getInt("user_level", 1),
            gamesPlayedThisWeek = prefs.getInt("games_played_week", 0),
            categoryAccuracies = getCategoryAccuracies(),
            gameProgresses = emptyMap() // Simplified for now
        )
    }
    
    /**
     * Get level results for a specific game
     */
    fun getLevelResults(gameId: String): List<LevelResult> {
        val json = prefs.getString("${KEY_LEVEL_RESULTS}_$gameId", null)
        return if (json != null) {
            try {
                val type = object : TypeToken<List<LevelResult>>() {}.type
                gson.fromJson(json, type) ?: emptyList()
            } catch (e: Exception) {
                emptyList()
            }
        } else {
            emptyList()
        }
    }
    
    /**
     * Calculate next level for a game
     */
    fun getNextLevel(gameId: String): Int {
        val gameProgress = getGameProgress(gameId)
        return gameProgress.currentLevel
    }
    
    /**
     * Check if a level is unlocked
     */
    fun isLevelUnlocked(gameId: String, level: Int): Boolean {
        val gameProgress = getGameProgress(gameId)
        return level <= gameProgress.currentLevel
    }
    
    private fun saveLevelResults(gameId: String, results: List<LevelResult>) {
        // Simplified - just save count for now
        prefs.edit {
            putInt("${gameId}_results_count", results.size)
        }
    }
    
    private fun updateGameProgress(gameId: String, newResult: LevelResult) {
        val currentProgress = getGameProgress(gameId)
        val allResults = getLevelResults(gameId)
        
        val newProgress = currentProgress.copy(
            currentLevel = if (newResult.accuracy >= 0.6) { // 60% accuracy to advance
                maxOf(currentProgress.currentLevel, newResult.level + 1)
            } else {
                currentProgress.currentLevel
            },
            highestLevel = maxOf(currentProgress.highestLevel, newResult.level),
            totalLevelsCompleted = allResults.size,
            bestAccuracy = maxOf(currentProgress.bestAccuracy, newResult.accuracy),
            averageAccuracy = allResults.map { it.accuracy }.average(),
            totalTimePlayed = currentProgress.totalTimePlayed + newResult.totalTimeMs,
            lastPlayedAt = Date(),
            levelResults = allResults
        )
        
        // Save updated game progress using individual keys
        prefs.edit {
            putInt("${gameId}_current_level", newProgress.currentLevel)
            putInt("${gameId}_highest_level", newProgress.highestLevel)
            putInt("${gameId}_total_completed", newProgress.totalLevelsCompleted)
            putFloat("${gameId}_best_accuracy", newProgress.bestAccuracy.toFloat())
            putFloat("${gameId}_avg_accuracy", newProgress.averageAccuracy.toFloat())
            putLong("${gameId}_total_time", newProgress.totalTimePlayed)
        }
    }
    
    private fun updateUserProgress() {
        // Simplified user progress update
        val currentUserProgress = getUserProgress()
        val totalScore = currentUserProgress.totalScore + 10 // Simple increment

        prefs.edit {
            putInt("total_score", totalScore)
            putInt("user_level", calculateUserLevel(totalScore))
        }
    }

    private fun getCategoryAccuracies(): Map<GameCategory, Double> {
        // Simplified - return stored category accuracies
        val map = mutableMapOf<GameCategory, Double>()
        map[GameCategory.MEMORY] = prefs.getFloat("memory_accuracy", 0.0f).toDouble()
        map[GameCategory.ATTENTION] = prefs.getFloat("attention_accuracy", 0.0f).toDouble()
        map[GameCategory.MATH] = prefs.getFloat("math_accuracy", 0.0f).toDouble()
        map[GameCategory.LOGIC] = prefs.getFloat("logic_accuracy", 0.0f).toDouble()
        map[GameCategory.LANGUAGE] = prefs.getFloat("language_accuracy", 0.0f).toDouble()
        return map
    }
    
    private fun calculateUserLevel(totalScore: Int): Int {
        return (totalScore / 100) + 1 // Simple level calculation
    }
    
    private fun getGameCategory(gameId: String): GameCategory {
        return when {
            gameId.startsWith("memory_") -> GameCategory.MEMORY
            gameId.startsWith("attention_") -> GameCategory.ATTENTION
            gameId.startsWith("math_") -> GameCategory.MATH
            gameId.startsWith("logic_") -> GameCategory.LOGIC
            gameId.startsWith("language_") -> GameCategory.LANGUAGE
            else -> GameCategory.ALL
        }
    }
}
