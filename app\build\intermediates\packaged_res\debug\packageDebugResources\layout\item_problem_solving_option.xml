<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:layout_marginBottom="8dp"
    android:background="@drawable/rounded_background"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    android:gravity="center_vertical">

    <RadioButton
        android:id="@+id/option_radio"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:buttonTint="@color/primary_light_blue"
        android:clickable="false"
        android:focusable="false" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/option_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Break down the problem into smaller, manageable parts and tackle each systematically"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:lineSpacingExtra="2dp" />

        <TextView
            android:id="@+id/style_indicator"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="📋 Systematic"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            android:visibility="gone" />

    </LinearLayout>

    <View
        android:id="@+id/strength_indicator"
        android:layout_width="4dp"
        android:layout_height="match_parent"
        android:layout_marginStart="8dp"
        android:background="@color/success_green"
        android:visibility="gone" />

</LinearLayout>
