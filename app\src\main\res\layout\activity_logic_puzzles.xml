<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.games.LogicPuzzlesActivity">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/primary_light_blue"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Quit Button -->
        <ImageButton
            android:id="@+id/btn_quit"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="Quit game"
            app:tint="@color/text_white" />

        <!-- Game Title -->
        <TextView
            android:id="@+id/game_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Logic Puzzles"
            android:textColor="@color/text_white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Menu Button -->
        <ImageButton
            android:id="@+id/btn_menu"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_menu"
            android:contentDescription="Game menu"
            app:tint="@color/text_white" />

    </LinearLayout>

    <!-- Game Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Level -->
        <TextView
            android:id="@+id/level_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Level 1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Round -->
        <TextView
            android:id="@+id/round_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Round 1/3"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Puzzle -->
        <TextView
            android:id="@+id/puzzle_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Puzzle 1/2"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Hints -->
        <TextView
            android:id="@+id/hints_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Hints: 0/3"
            android:textColor="@color/text_secondary"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- Instructions -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:padding="16dp"
        android:text="Use clues to fill the grid. Mark X for impossible, O for confirmed!"
        android:textColor="@color/text_primary"
        android:textSize="16sp" />

    <!-- Main Content -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal"
        android:padding="8dp">

        <!-- Grid Section -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="2"
            android:orientation="vertical"
            android:background="@color/surface_white"
            android:layout_margin="4dp"
            android:padding="8dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Logic Grid"
                android:textStyle="bold"
                android:textSize="16sp"
                android:gravity="center"
                android:padding="8dp"
                android:background="@color/primary_light_blue_light"
                android:textColor="@color/text_primary" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/grid_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:layout_margin="8dp"
                tools:listitem="@layout/item_logic_grid_row" />

        </LinearLayout>

        <!-- Clues Section -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:background="@color/surface_white"
            android:layout_margin="4dp"
            android:padding="8dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Clues"
                android:textStyle="bold"
                android:textSize="16sp"
                android:gravity="center"
                android:padding="8dp"
                android:background="@color/primary_light_blue_light"
                android:textColor="@color/text_primary" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/clues_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:layout_margin="8dp"
                tools:listitem="@layout/item_logic_clue" />

        </LinearLayout>

    </LinearLayout>

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:orientation="horizontal"
        android:padding="16dp">

        <Button
            android:id="@+id/btn_hint"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginEnd="4dp"
            android:text="Hint"
            android:textSize="14sp"
            android:backgroundTint="@color/primary_light_blue_light"
            android:textColor="@color/text_primary"
            app:cornerRadius="8dp" />

        <Button
            android:id="@+id/btn_check_solution"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginStart="2dp"
            android:layout_marginEnd="2dp"
            android:text="Check"
            android:textSize="14sp"
            android:backgroundTint="@color/stroop_green"
            android:textColor="@color/text_white"
            app:cornerRadius="8dp" />

        <Button
            android:id="@+id/btn_reset"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:layout_marginStart="4dp"
            android:text="Reset"
            android:textSize="14sp"
            android:backgroundTint="@color/stroop_red"
            android:textColor="@color/text_white"
            app:cornerRadius="8dp" />

    </LinearLayout>

</LinearLayout>
