package com.leapiq.braintraining.notifications

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import java.util.*

class NotificationScheduler(private val context: Context) {
    
    private val sharedPreferences: SharedPreferences = 
        context.getSharedPreferences("LeapIQ_Prefs", Context.MODE_PRIVATE)
    
    companion object {
        const val DAILY_REMINDER_REQUEST_CODE = 2001
        const val CHALLENGE_REMINDER_REQUEST_CODE = 2002
        const val MOTIVATIONAL_TIP_REQUEST_CODE = 2003
    }
    
    fun scheduleDailyNotifications() {
        val userName = sharedPreferences.getString("user_name", "User") ?: "User"
        
        // Schedule morning reminder (9:00 AM)
        scheduleDailyReminder(9, 0, DAILY_REMINDER_REQUEST_CODE) {
            NotificationService(context).sendDailyReminder(userName)
        }
        
        // Schedule afternoon challenge reminder (2:00 PM)
        scheduleDailyReminder(14, 0, CHALLENGE_REMINDER_REQUEST_CODE) {
            // Only send if user hasn't completed today's challenge
            if (!hasCompletedTodaysChallenge()) {
                NotificationService(context).sendChallengeReminder(userName)
            }
        }
        
        // Schedule evening motivational tip (7:00 PM)
        scheduleDailyReminder(19, 0, MOTIVATIONAL_TIP_REQUEST_CODE) {
            val tips = NotificationService(context).getMotivationalTips()
            val randomTip = tips.random()
            NotificationService(context).sendMotivationalTip(randomTip)
        }
    }
    
    private fun scheduleDailyReminder(hour: Int, minute: Int, requestCode: Int, action: () -> Unit) {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        
        val intent = Intent(context, NotificationReceiver::class.java).apply {
            putExtra("action", requestCode)
        }
        
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val calendar = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, hour)
            set(Calendar.MINUTE, minute)
            set(Calendar.SECOND, 0)
            
            // If the time has already passed today, schedule for tomorrow
            if (timeInMillis <= System.currentTimeMillis()) {
                add(Calendar.DAY_OF_MONTH, 1)
            }
        }
        
        try {
            alarmManager.setRepeating(
                AlarmManager.RTC_WAKEUP,
                calendar.timeInMillis,
                AlarmManager.INTERVAL_DAY,
                pendingIntent
            )
        } catch (e: SecurityException) {
            // Permission not granted for exact alarms, use inexact
            alarmManager.setInexactRepeating(
                AlarmManager.RTC_WAKEUP,
                calendar.timeInMillis,
                AlarmManager.INTERVAL_DAY,
                pendingIntent
            )
        }
    }
    
    private fun hasCompletedTodaysChallenge(): Boolean {
        val today = Calendar.getInstance()
        val todayString = "${today.get(Calendar.YEAR)}-${today.get(Calendar.MONTH)}-${today.get(Calendar.DAY_OF_MONTH)}"
        val lastChallengeDate = sharedPreferences.getString("last_challenge_date", "")
        
        return lastChallengeDate == todayString
    }
    
    fun cancelAllNotifications() {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        
        val requestCodes = listOf(
            DAILY_REMINDER_REQUEST_CODE,
            CHALLENGE_REMINDER_REQUEST_CODE,
            MOTIVATIONAL_TIP_REQUEST_CODE
        )
        
        requestCodes.forEach { requestCode ->
            val intent = Intent(context, NotificationReceiver::class.java)
            val pendingIntent = PendingIntent.getBroadcast(
                context,
                requestCode,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )
            alarmManager.cancel(pendingIntent)
        }
    }
}
