<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_pattern_memory" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_pattern_memory.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_pattern_memory_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="119" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="91" startOffset="4" endLine="100" endOffset="33"/></Target><Target id="@+id/pattern_grid" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="110" startOffset="8" endLine="115" endOffset="56"/></Target></Targets></Layout>