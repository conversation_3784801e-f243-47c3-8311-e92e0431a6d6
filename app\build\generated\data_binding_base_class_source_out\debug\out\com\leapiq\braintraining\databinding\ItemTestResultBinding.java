// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.card.MaterialCardView;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTestResultBinding implements ViewBinding {
  @NonNull
  private final MaterialCardView rootView;

  @NonNull
  public final TextView accuracyText;

  @NonNull
  public final TextView dateText;

  @NonNull
  public final TextView insightText;

  @NonNull
  public final TextView questionsText;

  @NonNull
  public final TextView responseTimeText;

  @NonNull
  public final TextView scoreIcon;

  @NonNull
  public final TextView scoreText;

  @NonNull
  public final TextView timeText;

  @NonNull
  public final TextView totalTimeText;

  private ItemTestResultBinding(@NonNull MaterialCardView rootView, @NonNull TextView accuracyText,
      @NonNull TextView dateText, @NonNull TextView insightText, @NonNull TextView questionsText,
      @NonNull TextView responseTimeText, @NonNull TextView scoreIcon, @NonNull TextView scoreText,
      @NonNull TextView timeText, @NonNull TextView totalTimeText) {
    this.rootView = rootView;
    this.accuracyText = accuracyText;
    this.dateText = dateText;
    this.insightText = insightText;
    this.questionsText = questionsText;
    this.responseTimeText = responseTimeText;
    this.scoreIcon = scoreIcon;
    this.scoreText = scoreText;
    this.timeText = timeText;
    this.totalTimeText = totalTimeText;
  }

  @Override
  @NonNull
  public MaterialCardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTestResultBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTestResultBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_test_result, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTestResultBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.accuracy_text;
      TextView accuracyText = ViewBindings.findChildViewById(rootView, id);
      if (accuracyText == null) {
        break missingId;
      }

      id = R.id.date_text;
      TextView dateText = ViewBindings.findChildViewById(rootView, id);
      if (dateText == null) {
        break missingId;
      }

      id = R.id.insight_text;
      TextView insightText = ViewBindings.findChildViewById(rootView, id);
      if (insightText == null) {
        break missingId;
      }

      id = R.id.questions_text;
      TextView questionsText = ViewBindings.findChildViewById(rootView, id);
      if (questionsText == null) {
        break missingId;
      }

      id = R.id.response_time_text;
      TextView responseTimeText = ViewBindings.findChildViewById(rootView, id);
      if (responseTimeText == null) {
        break missingId;
      }

      id = R.id.score_icon;
      TextView scoreIcon = ViewBindings.findChildViewById(rootView, id);
      if (scoreIcon == null) {
        break missingId;
      }

      id = R.id.score_text;
      TextView scoreText = ViewBindings.findChildViewById(rootView, id);
      if (scoreText == null) {
        break missingId;
      }

      id = R.id.time_text;
      TextView timeText = ViewBindings.findChildViewById(rootView, id);
      if (timeText == null) {
        break missingId;
      }

      id = R.id.total_time_text;
      TextView totalTimeText = ViewBindings.findChildViewById(rootView, id);
      if (totalTimeText == null) {
        break missingId;
      }

      return new ItemTestResultBinding((MaterialCardView) rootView, accuracyText, dateText,
          insightText, questionsText, responseTimeText, scoreIcon, scoreText, timeText,
          totalTimeText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
