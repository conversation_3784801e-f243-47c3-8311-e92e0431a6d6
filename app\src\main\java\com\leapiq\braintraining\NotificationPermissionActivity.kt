package com.leapiq.braintraining

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.databinding.ActivityNotificationPermissionBinding
import com.leapiq.braintraining.notifications.NotificationScheduler

class NotificationPermissionActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityNotificationPermissionBinding
    
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            // Schedule notifications if permission granted
            NotificationScheduler(this).scheduleDailyNotifications()
        }
        // Proceed to main app regardless of permission result
        proceedToMainApp()
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        binding = ActivityNotificationPermissionBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
    }
    
    private fun setupUI() {
        binding.btnAllow.setOnClickListener {
            requestNotificationPermission()
        }
        
        binding.btnNotNow.setOnClickListener {
            proceedToMainApp()
        }
    }
    
    private fun requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            when {
                ContextCompat.checkSelfPermission(
                    this,
                    Manifest.permission.POST_NOTIFICATIONS
                ) == PackageManager.PERMISSION_GRANTED -> {
                    // Permission already granted
                    proceedToMainApp()
                }
                else -> {
                    // Request permission
                    requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                }
            }
        } else {
            // For Android 12 and below, notifications are enabled by default
            proceedToMainApp()
        }
    }
    
    private fun proceedToMainApp() {
        startActivity(Intent(this, MainActivity::class.java))
        finish()
    }
}
