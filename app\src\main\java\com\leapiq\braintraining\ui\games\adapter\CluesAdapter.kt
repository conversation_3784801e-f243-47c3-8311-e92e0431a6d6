package com.leapiq.braintraining.ui.games.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.ui.games.model.Clue
import com.leapiq.braintraining.ui.games.model.ClueType

/**
 * Adapter for displaying clues in the Logic Puzzles game
 */
class CluesAdapter(
    private val clues: List<Clue>,
    private val onClueClicked: (Int) -> Unit
) : RecyclerView.Adapter<CluesAdapter.ClueViewHolder>() {

    private var highlightedClueIndex = -1

    class ClueViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val clueNumber: TextView = itemView.findViewById(R.id.clue_number)
        val clueText: TextView = itemView.findViewById(R.id.clue_text)
        val clueType: TextView = itemView.findViewById(R.id.clue_type)
        val clueContainer: View = itemView.findViewById(R.id.clue_container)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ClueViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_logic_clue, parent, false)
        return ClueViewHolder(view)
    }

    override fun onBindViewHolder(holder: ClueViewHolder, position: Int) {
        val clue = clues[position]
        val context = holder.itemView.context

        holder.clueNumber.text = "${position + 1}."
        holder.clueText.text = clue.text
        holder.clueType.text = getClueTypeText(clue.type)

        // Set clue type color
        val typeColor = when (clue.type) {
            ClueType.DIRECT -> R.color.stroop_green
            ClueType.NOT_EQUAL -> R.color.stroop_red
            ClueType.ADJACENT -> R.color.stroop_blue
            ClueType.RELATIVE -> R.color.stroop_yellow
            ClueType.CONDITIONAL -> R.color.search_purple
        }
        holder.clueType.setTextColor(ContextCompat.getColor(context, typeColor))

        // Highlight if selected
        if (position == highlightedClueIndex) {
            holder.clueContainer.setBackgroundColor(
                ContextCompat.getColor(context, R.color.primary_light_blue_light)
            )
        } else {
            holder.clueContainer.setBackgroundColor(
                ContextCompat.getColor(context, R.color.surface_white)
            )
        }

        // Set click listener
        holder.itemView.setOnClickListener {
            onClueClicked(position)
        }
    }

    override fun getItemCount(): Int = clues.size

    fun highlightClue(index: Int) {
        val previousIndex = highlightedClueIndex
        highlightedClueIndex = index
        
        if (previousIndex != -1) {
            notifyItemChanged(previousIndex)
        }
        notifyItemChanged(index)
    }

    private fun getClueTypeText(type: ClueType): String {
        return when (type) {
            ClueType.DIRECT -> "Direct"
            ClueType.NOT_EQUAL -> "Negative"
            ClueType.ADJACENT -> "Adjacent"
            ClueType.RELATIVE -> "Relative"
            ClueType.CONDITIONAL -> "Conditional"
        }
    }
}
