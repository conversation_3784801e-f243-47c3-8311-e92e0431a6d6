package com.leapiq.braintraining.ui.games

import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityStroopTestBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date

/**
 * Stroop Test Game
 * Players see color words (RED, BLUE, GREEN, YELLOW) displayed in different ink colors
 * They must identify the ink color, not the word meaning - tests cognitive control
 */
class StroopTestActivity : AppCompatActivity() {

    private lateinit var binding: ActivityStroopTestBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val roundTimes = mutableListOf<Long>()
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "attention_1"

    // Stroop test specific
    private var currentWordText = ""
    private var currentWordColor = ""
    private var trialStartTime = 0L
    private var trialsPerRound = 10
    private var currentTrial = 0
    private var roundStartTime = 0L

    // Color definitions
    private val colorWords = listOf("RED", "BLUE", "GREEN", "YELLOW")
    private val colorValues = mapOf(
        "RED" to R.color.stroop_red,
        "BLUE" to R.color.stroop_blue, 
        "GREEN" to R.color.stroop_green,
        "YELLOW" to R.color.stroop_yellow
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityStroopTestBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.stroop_test)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Click the color of the INK, not the word!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup color answer buttons
            setupColorButtons()
        }
    }

    private fun setupColorButtons() {
        binding.apply {
            btnRed.setOnClickListener { onColorSelected("RED") }
            btnBlue.setOnClickListener { onColorSelected("BLUE") }
            btnGreen.setOnClickListener { onColorSelected("GREEN") }
            btnYellow.setOnClickListener { onColorSelected("YELLOW") }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        roundStartTime = System.currentTimeMillis()
        currentTrial = 0
        trialsPerRound = getTrialsPerRound(currentLevel)
        
        binding.trialText.text = "Trial 1/$trialsPerRound"
        
        showNextTrial()
    }

    private fun getTrialsPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 10      // 10 trials per round
            in 6..10 -> 12     // 12 trials per round
            in 11..15 -> 15    // 15 trials per round
            in 16..20 -> 18    // 18 trials per round
            else -> 20         // 20 trials per round
        }
    }

    private fun showNextTrial() {
        if (currentTrial >= trialsPerRound) {
            roundComplete()
            return
        }

        currentTrial++
        binding.trialText.text = "Trial $currentTrial/$trialsPerRound"
        
        generateStroopStimulus()
        displayStimulus()
        trialStartTime = System.currentTimeMillis()
    }

    private fun generateStroopStimulus() {
        // Select random word and color
        currentWordText = colorWords.random()
        currentWordColor = colorWords.random()
        
        // Increase congruent trials at easier levels, incongruent at harder levels
        val congruencyRate = when (currentLevel) {
            in 1..5 -> 0.5f    // 50% congruent
            in 6..10 -> 0.3f   // 30% congruent (more conflict)
            in 11..15 -> 0.2f  // 20% congruent (high conflict)
            else -> 0.1f       // 10% congruent (maximum conflict)
        }
        
        // Force congruency based on rate
        if (Math.random() < congruencyRate) {
            currentWordColor = currentWordText // Make congruent
        }
    }

    private fun displayStimulus() {
        binding.apply {
            // Set the word text
            wordDisplay.text = currentWordText
            
            // Set the ink color
            val colorResId = colorValues[currentWordColor] ?: R.color.stroop_red
            val color = ContextCompat.getColor(this@StroopTestActivity, colorResId)
            wordDisplay.setTextColor(color)
            
            // Show stimulus
            wordDisplay.visibility = android.view.View.VISIBLE
            enableColorButtons(true)
        }
    }

    private fun onColorSelected(selectedColor: String) {
        val reactionTime = System.currentTimeMillis() - trialStartTime
        totalAttempts++
        
        val isCorrect = selectedColor == currentWordColor
        if (isCorrect) {
            totalCorrect++
        }
        
        // Hide stimulus
        binding.wordDisplay.visibility = android.view.View.INVISIBLE
        enableColorButtons(false)
        
        // Show feedback briefly
        showTrialFeedback(isCorrect, reactionTime)
        
        // Continue to next trial after delay
        Handler(Looper.getMainLooper()).postDelayed({
            showNextTrial()
        }, getInterTrialInterval(currentLevel))
    }

    private fun showTrialFeedback(isCorrect: Boolean, reactionTime: Long) {
        val feedback = if (isCorrect) {
            "Correct! (${reactionTime}ms)"
        } else {
            "Wrong! Ink was $currentWordColor"
        }
        binding.instructionText.text = feedback
    }

    private fun getInterTrialInterval(level: Int): Long {
        return when (level) {
            in 1..5 -> 1000L   // 1 second between trials
            in 6..10 -> 800L   // 0.8 seconds
            in 11..15 -> 600L  // 0.6 seconds
            in 16..20 -> 400L  // 0.4 seconds
            else -> 200L       // 0.2 seconds (very fast)
        }
    }

    private fun enableColorButtons(enabled: Boolean) {
        binding.apply {
            btnRed.isEnabled = enabled
            btnBlue.isEnabled = enabled
            btnGreen.isEnabled = enabled
            btnYellow.isEnabled = enabled
            
            val alpha = if (enabled) 1.0f else 0.5f
            colorButtonsContainer.alpha = alpha
        }
    }

    private fun roundComplete() {
        val roundTime = System.currentTimeMillis() - roundStartTime
        val roundAccuracy = if (trialsPerRound > 0) {
            (totalCorrect.toDouble() / trialsPerRound)
        } else 1.0

        // Store round time for detailed analysis
        roundTimes.add(roundTime)

        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = "Round complete! Accuracy: ${(roundAccuracy * 100).toInt()}%"
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentTrial = 0
            roundStartTime = System.currentTimeMillis()
            
            Handler(Looper.getMainLooper()).postDelayed({
                binding.instructionText.text = "Click the color of the INK, not the word!"
                showNextTrial()
            }, 2000)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0

        // Save level result using actual round data
        val roundResults = mutableListOf<RoundResult>()
        val trialsPerRoundTotal = trialsPerRound * maxRounds
        val correctPerRound = totalCorrect / maxRounds

        // Validate that we have the expected number of trials
        val expectedTrials = trialsPerRoundTotal
        val actualTrials = totalAttempts

        // Use expected trials for more consistent scoring, but fall back to actual if needed
        val trialsForAccuracy = if (actualTrials >= expectedTrials * 0.8) expectedTrials else actualTrials
        val adjustedAccuracy = if (trialsForAccuracy > 0) (totalCorrect.toDouble() / trialsForAccuracy) else 0.0

        for (i in 1..maxRounds) {
            val roundTime = if (i <= roundTimes.size) roundTimes[i-1] else totalTime / maxRounds
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = correctPerRound > (trialsPerRound * 0.7), // 70% threshold
                timeSpentMs = roundTime,
                attempts = trialsPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = adjustedAccuracy,
            score = (adjustedAccuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Avg Reaction Time: ${totalTime / totalAttempts}ms
                Trials: $totalAttempts

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Total Trials: $totalAttempts

                Attention Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Test your cognitive control and attention

                📋 RULES:
                • Color words appear in different ink colors
                • Click the button matching the INK color
                • Ignore what the word says!
                • Complete trials as quickly and accurately as possible

                💡 TIPS:
                • Focus on the color, not the meaning
                • Stay concentrated throughout
                • Speed improves with practice

                🏆 SCORING:
                • Accuracy = correct responses / total trials
                • Faster reaction times = better performance
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
