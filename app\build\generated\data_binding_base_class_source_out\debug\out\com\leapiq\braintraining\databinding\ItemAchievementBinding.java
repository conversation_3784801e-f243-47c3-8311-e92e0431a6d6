// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemAchievementBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView achievementDescription;

  @NonNull
  public final ImageView achievementIcon;

  @NonNull
  public final TextView achievementName;

  private ItemAchievementBinding(@NonNull LinearLayout rootView,
      @NonNull TextView achievementDescription, @NonNull ImageView achievementIcon,
      @NonNull TextView achievementName) {
    this.rootView = rootView;
    this.achievementDescription = achievementDescription;
    this.achievementIcon = achievementIcon;
    this.achievementName = achievementName;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemAchievementBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemAchievementBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_achievement, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemAchievementBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.achievement_description;
      TextView achievementDescription = ViewBindings.findChildViewById(rootView, id);
      if (achievementDescription == null) {
        break missingId;
      }

      id = R.id.achievement_icon;
      ImageView achievementIcon = ViewBindings.findChildViewById(rootView, id);
      if (achievementIcon == null) {
        break missingId;
      }

      id = R.id.achievement_name;
      TextView achievementName = ViewBindings.findChildViewById(rootView, id);
      if (achievementName == null) {
        break missingId;
      }

      return new ItemAchievementBinding((LinearLayout) rootView, achievementDescription,
          achievementIcon, achievementName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
