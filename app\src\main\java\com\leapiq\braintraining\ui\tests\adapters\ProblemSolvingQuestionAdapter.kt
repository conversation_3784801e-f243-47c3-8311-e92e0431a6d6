package com.leapiq.braintraining.ui.tests.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ItemProblemSolvingOptionBinding
import com.leapiq.braintraining.ui.tests.ProblemSolvingStyleActivity
import com.leapiq.braintraining.ui.tests.ProblemSolvingQuestion
import com.leapiq.braintraining.ui.tests.ProblemSolvingOption
import com.leapiq.braintraining.ui.tests.ProblemSolvingType

/**
 * Adapter for problem solving question options
 */
class ProblemSolvingQuestionAdapter(
    private val onOptionSelected: (Int) -> Unit
) : RecyclerView.Adapter<ProblemSolvingQuestionAdapter.OptionViewHolder>() {
    
    private var currentQuestion: ProblemSolvingQuestion? = null

    fun updateQuestion(question: ProblemSolvingQuestion) {
        currentQuestion = question
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OptionViewHolder {
        val binding = ItemProblemSolvingOptionBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return OptionViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: OptionViewHolder, position: Int) {
        currentQuestion?.let { question ->
            if (position < question.options.size) {
                holder.bind(question.options[position], position)
            }
        }
    }
    
    override fun getItemCount(): Int {
        return currentQuestion?.options?.size ?: 0
    }
    
    inner class OptionViewHolder(
        private val binding: ItemProblemSolvingOptionBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(option: ProblemSolvingOption, position: Int) {
            binding.apply {
                optionText.text = option.text
                optionRadio.isChecked = option.isSelected

                // Set style type indicator
                val styleIcon = when (option.styleType) {
                    ProblemSolvingType.ANALYTICAL -> "📊"
                    ProblemSolvingType.INTUITIVE -> "💡"
                    ProblemSolvingType.SYSTEMATIC -> "📋"
                    ProblemSolvingType.CREATIVE -> "🎨"
                }

                val styleLabel = when (option.styleType) {
                    ProblemSolvingType.ANALYTICAL -> "Analytical"
                    ProblemSolvingType.INTUITIVE -> "Intuitive"
                    ProblemSolvingType.SYSTEMATIC -> "Systematic"
                    ProblemSolvingType.CREATIVE -> "Creative"
                }

                // Set style indicator (hidden during test, can be shown for debugging)
                styleIndicator.text = "$styleIcon $styleLabel"
                // styleIndicator.visibility = View.VISIBLE // Uncomment for debugging
                // styleIndicator.text = "$styleIcon $styleLabel"
                // styleIndicator.visibility = View.VISIBLE
                
                // Set click listeners
                root.setOnClickListener {
                    onOptionSelected(position)
                }
                
                optionRadio.setOnClickListener {
                    onOptionSelected(position)
                }
                
                // Visual feedback for selection
                if (option.isSelected) {
                    root.setBackgroundColor(ContextCompat.getColor(root.context, R.color.primary_light_blue_light))
                    optionText.setTextColor(ContextCompat.getColor(root.context, R.color.text_primary))
                } else {
                    root.setBackgroundColor(ContextCompat.getColor(root.context, R.color.surface_white))
                    optionText.setTextColor(ContextCompat.getColor(root.context, R.color.text_primary))
                }
                
                // Add style strength indicator (subtle visual cue)
                val strengthColor = when (option.score) {
                    5 -> R.color.success_green      // Very strong for this style
                    4 -> R.color.primary_light_blue // Strong for this style
                    3 -> R.color.warning_orange     // Moderate for this style
                    2 -> R.color.text_secondary     // Weak for this style
                    1 -> R.color.error_red          // Very weak for this style
                    else -> R.color.text_secondary
                }
                
                // Set strength indicator (hidden during test, can be shown for debugging)
                strengthIndicator.setBackgroundColor(ContextCompat.getColor(root.context, strengthColor))
                // strengthIndicator.visibility = View.VISIBLE // Uncomment for debugging
            }
        }
    }
}
