package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityAnagramsBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Anagrams Game
 * Players unscramble letters to form valid words
 * Tests spelling, vocabulary, and pattern recognition
 */
class AnagramsActivity : AppCompatActivity() {

    private lateinit var binding: ActivityAnagramsBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "language_2"

    // Anagrams specific
    private var currentWord = ""
    private var scrambledWord = ""
    private var userInput = ""
    private var problemStartTime = 0L
    private var currentTrial = 0
    private var trialsPerRound = 8
    private var reactionTimes = mutableListOf<Long>()

    // Word lists by difficulty
    private val wordLists = mapOf(
        // Level 1-3: 3-4 letter words
        1 to listOf("cat", "dog", "sun", "car", "run", "big", "red", "top", "cup", "hat"),
        2 to listOf("book", "tree", "fish", "bird", "hand", "foot", "door", "moon", "star", "fire"),
        3 to listOf("house", "water", "green", "happy", "music", "dance", "smile", "heart", "light", "peace"),
        
        // Level 4-6: 5-6 letter words
        4 to listOf("friend", "school", "family", "garden", "flower", "animal", "planet", "forest", "bridge", "castle"),
        5 to listOf("freedom", "wisdom", "beauty", "nature", "future", "dreams", "wonder", "spirit", "energy", "health"),
        6 to listOf("journey", "mystery", "harmony", "balance", "courage", "passion", "victory", "destiny", "miracle", "diamond"),
        
        // Level 7-10: 6-7 letter words
        7 to listOf("science", "history", "culture", "society", "economy", "justice", "liberty", "dignity", "respect", "honesty"),
        8 to listOf("creative", "positive", "negative", "powerful", "peaceful", "grateful", "mindful", "hopeful", "careful", "helpful"),
        9 to listOf("universe", "infinite", "absolute", "relative", "complete", "perfect", "natural", "magical", "logical", "typical"),
        10 to listOf("adventure", "discovery", "knowledge", "education", "evolution", "revolution", "tradition", "innovation", "inspiration", "motivation"),
        
        // Level 11-15: 7-8 letter words
        11 to listOf("philosophy", "psychology", "technology", "democracy", "geography", "biography", "astronomy", "chemistry", "mathematics", "literature"),
        12 to listOf("beautiful", "wonderful", "excellent", "brilliant", "fantastic", "incredible", "remarkable", "spectacular", "magnificent", "extraordinary"),
        13 to listOf("understand", "appreciate", "communicate", "concentrate", "demonstrate", "investigate", "participate", "collaborate", "celebrate", "integrate"),
        14 to listOf("imagination", "inspiration", "transformation", "communication", "collaboration", "determination", "concentration", "appreciation", "consideration", "organization"),
        15 to listOf("responsibility", "sustainability", "accessibility", "compatibility", "accountability", "availability", "capability", "reliability", "flexibility", "possibility"),
        
        // Level 16+: 8+ letter words
        16 to listOf("consciousness", "subconscious", "unconscious", "self-aware", "enlightened", "transcendent", "omnipresent", "omnipotent", "omniscient", "metaphysical"),
        17 to listOf("extraordinary", "revolutionary", "evolutionary", "transformative", "comprehensive", "sophisticated", "philosophical", "psychological", "technological", "astronomical"),
        18 to listOf("incomprehensible", "indistinguishable", "uncharacteristic", "disproportionate", "counterproductive", "interdisciplinary", "multidimensional", "unconventional", "unprecedented", "uncompromising")
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAnagramsBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.anagrams)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Unscramble the letters to form a word!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup letter buttons and controls
            setupLetterButtons()
            setupControlButtons()
        }
    }

    private fun setupLetterButtons() {
        // Letter buttons will be created dynamically based on word length
    }

    private fun setupControlButtons() {
        binding.apply {
            btnClear.setOnClickListener {
                clearInput()
            }

            btnSubmit.setOnClickListener {
                submitAnswer()
            }

            btnHint.setOnClickListener {
                showHint()
            }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentTrial = 0
        reactionTimes.clear()
        trialsPerRound = getTrialsPerRound(currentLevel)
        
        binding.trialText.text = "Word 1/$trialsPerRound"
        
        generateNextAnagram()
    }

    private fun getTrialsPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 8       // 8 words per round
            in 6..10 -> 10     // 10 words per round
            in 11..15 -> 12    // 12 words per round
            in 16..20 -> 15    // 15 words per round
            else -> 18         // 18 words per round
        }
    }

    private fun generateNextAnagram() {
        if (currentTrial >= trialsPerRound) {
            roundComplete()
            return
        }

        currentTrial++
        binding.trialText.text = "Word $currentTrial/$trialsPerRound"
        
        val levelGroup = getLevelGroup(currentLevel)
        val wordList = wordLists[levelGroup] ?: wordLists[1]!!
        currentWord = wordList.random()
        
        generateScrambledWord()
        createLetterButtons()
        clearInput()
        
        problemStartTime = System.currentTimeMillis()
    }

    private fun getLevelGroup(level: Int): Int {
        return when (level) {
            in 1..3 -> Random.nextInt(1, 4)
            in 4..6 -> Random.nextInt(4, 7)
            in 7..10 -> Random.nextInt(7, 11)
            in 11..15 -> Random.nextInt(11, 16)
            else -> Random.nextInt(16, 19)
        }
    }

    private fun generateScrambledWord() {
        val letters = currentWord.toCharArray().toMutableList()
        
        // Scramble until it's different from original
        do {
            letters.shuffle()
            scrambledWord = letters.joinToString("")
        } while (scrambledWord == currentWord && currentWord.length > 2)
        
        binding.scrambledWordDisplay.text = scrambledWord.uppercase()
    }

    private fun createLetterButtons() {
        binding.lettersContainer.removeAllViews()
        
        scrambledWord.forEach { letter ->
            val button = Button(this)
            button.text = letter.uppercase()
            button.textSize = 18f
            button.setTextColor(ContextCompat.getColor(this, R.color.text_white))
            button.backgroundTintList = ContextCompat.getColorStateList(this, R.color.primary_light_blue)
            
            val layoutParams = android.widget.LinearLayout.LayoutParams(
                0,
                120,
                1f
            )
            layoutParams.setMargins(4, 4, 4, 4)
            button.layoutParams = layoutParams
            
            button.setOnClickListener {
                addLetterToInput(letter)
            }
            
            binding.lettersContainer.addView(button)
        }
    }

    private fun addLetterToInput(letter: Char) {
        if (userInput.length < currentWord.length) {
            userInput += letter
            updateInputDisplay()
        }
    }

    private fun clearInput() {
        userInput = ""
        updateInputDisplay()
    }

    private fun updateInputDisplay() {
        val displayText = userInput.uppercase() + "_".repeat(currentWord.length - userInput.length)
        binding.userInputDisplay.text = displayText
    }

    private fun submitAnswer() {
        if (userInput.length != currentWord.length) {
            showMessage("Complete the word first!")
            return
        }
        
        val reactionTime = System.currentTimeMillis() - problemStartTime
        
        totalAttempts++
        val isCorrect = userInput.lowercase() == currentWord.lowercase()
        
        if (isCorrect) {
            totalCorrect++
            reactionTimes.add(reactionTime)
        }
        
        showFeedback(isCorrect, reactionTime)
        
        // Continue to next anagram after delay
        Handler(Looper.getMainLooper()).postDelayed({
            generateNextAnagram()
        }, 2500)
    }

    private fun showHint() {
        val hint = when (currentWord.length) {
            3, 4 -> "This is a ${currentWord.length}-letter word"
            5, 6 -> "This word has ${currentWord.length} letters and starts with '${currentWord.first().uppercase()}'"
            else -> "This ${currentWord.length}-letter word starts with '${currentWord.first().uppercase()}' and ends with '${currentWord.last().uppercase()}'"
        }
        
        AlertDialog.Builder(this)
            .setTitle("Hint")
            .setMessage(hint)
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun showFeedback(isCorrect: Boolean, reactionTime: Long) {
        val feedback = if (isCorrect) {
            "Correct! '$scrambledWord' → '$currentWord' (${reactionTime}ms)"
        } else {
            "Wrong! '$scrambledWord' → '$currentWord' (not '$userInput')"
        }
        
        binding.instructionText.text = feedback
        binding.instructionText.setTextColor(
            ContextCompat.getColor(this, if (isCorrect) R.color.stroop_green else R.color.stroop_red)
        )
        
        // Reset color after delay
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.text = "Unscramble the letters to form a word!"
            binding.instructionText.setTextColor(
                ContextCompat.getColor(this, R.color.text_secondary)
            )
        }, 2500)
    }

    private fun showMessage(message: String) {
        binding.instructionText.text = message
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.text = "Unscramble the letters to form a word!"
        }, 2000)
    }

    private fun roundComplete() {
        val avgReactionTime = if (reactionTimes.isNotEmpty()) {
            reactionTimes.average().toLong()
        } else 0L
        
        val roundAccuracy = if (trialsPerRound > 0) {
            (totalCorrect.toDouble() / trialsPerRound)
        } else 1.0
        
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Accuracy: ${(roundAccuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentTrial = 0
            reactionTimes.clear()
            
            Handler(Looper.getMainLooper()).postDelayed({
                generateNextAnagram()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0
        val avgReactionTime = if (reactionTimes.isNotEmpty()) reactionTimes.average().toLong() else 0L

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7,
                timeSpentMs = totalTime / maxRounds,
                attempts = trialsPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
                Words: $totalAttempts

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Total Words: $totalAttempts

                Anagram Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Unscramble letters to form valid words

                📋 RULES:
                • Look at the scrambled letters at the top
                • Tap letter buttons to build your word
                • Use all letters exactly once
                • Submit when you think you have the correct word

                💡 TIPS:
                • Look for common letter patterns
                • Think of word categories (nouns, verbs, adjectives)
                • Use the hint button if you're stuck
                • Clear and try different combinations

                🏆 SCORING:
                • Accuracy = correct words / total words
                • Speed = average unscrambling time
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
