package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityNumberMemoryBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date

/**
 * Number Memory Game (Digit Span)
 * Players watch a sequence of numbers, then type them back in correct order
 * Supports both forward and reverse recall modes
 */
class NumberMemoryActivity : AppCompatActivity() {

    private lateinit var binding: ActivityNumberMemoryBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "memory_4"

    // Number memory specific
    private var targetSequence = ""
    private var isShowingNumbers = false
    private var isPlayerTurn = false
    private var isReverseMode = false
    private var currentRoundColor = "#2196F3" // Default blue
    private var playerInput = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityNumberMemoryBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.number_memory)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Watch the numbers, then type them back!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup number pad
            setupNumberPad()
        }
    }

    private fun setupNumberPad() {
        binding.apply {
            btn0.setOnClickListener { addDigit("0") }
            btn1.setOnClickListener { addDigit("1") }
            btn2.setOnClickListener { addDigit("2") }
            btn3.setOnClickListener { addDigit("3") }
            btn4.setOnClickListener { addDigit("4") }
            btn5.setOnClickListener { addDigit("5") }
            btn6.setOnClickListener { addDigit("6") }
            btn7.setOnClickListener { addDigit("7") }
            btn8.setOnClickListener { addDigit("8") }
            btn9.setOnClickListener { addDigit("9") }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        generateRoundColor()
        updateNumberPadColors()
        isReverseMode = shouldUseReverseMode(currentLevel)
        generateNumberSequence()
        clearInput()
        showNumbers()
    }

    private fun shouldUseReverseMode(level: Int): Boolean {
        // Introduce reverse mode at higher levels
        return level > 10 && (level % 3 == 0) // Every 3rd level after level 10
    }

    private fun generateRoundColor() {
        val colors = listOf(
            "#2196F3", // Blue
            "#4CAF50", // Green
            "#F44336", // Red
            "#FF9800", // Orange
            "#9C27B0", // Purple
            "#00BCD4", // Cyan
            "#FFEB3B", // Yellow (but not too bright)
            "#E91E63"  // Pink
        )
        currentRoundColor = colors.random()
    }

    private fun updateNumberPadColors() {
        val baseColor = android.graphics.Color.parseColor(currentRoundColor)
        val darkerColor = darkenColor(baseColor, 0.2f)

        val drawable = android.graphics.drawable.GradientDrawable().apply {
            shape = android.graphics.drawable.GradientDrawable.RECTANGLE
            setColor(baseColor)
            cornerRadius = 16f
            setStroke(4, darkerColor)
        }

        // Apply to all number buttons
        val buttons = listOf(
            binding.btn0, binding.btn1, binding.btn2, binding.btn3, binding.btn4,
            binding.btn5, binding.btn6, binding.btn7, binding.btn8, binding.btn9
        )

        buttons.forEach { button ->
            button.background = drawable.constantState?.newDrawable()?.mutate()
        }
    }

    private fun darkenColor(color: Int, factor: Float): Int {
        val hsv = FloatArray(3)
        android.graphics.Color.colorToHSV(color, hsv)
        hsv[2] *= (1f - factor) // Reduce brightness
        return android.graphics.Color.HSVToColor(hsv)
    }

    private fun generateNumberSequence() {
        val sequenceLength = getSequenceLength(currentLevel)
        targetSequence = ""
        
        repeat(sequenceLength) {
            targetSequence += (0..9).random().toString()
        }
    }

    private fun getSequenceLength(level: Int): Int {
        return when (level) {
            in 1..5 -> 4 + (level - 1)      // 4, 5, 6, 7, 8
            in 6..10 -> 8 + (level - 6)     // 9, 10, 11, 12, 13
            in 11..15 -> 13 + (level - 11)  // 14, 15, 16, 17, 18
            in 16..20 -> 18 + (level - 16)  // 19, 20, 21, 22, 23
            else -> 23 + (level - 21)       // 24+
        }
    }

    private fun showNumbers() {
        isShowingNumbers = true
        isPlayerTurn = false
        
        val modeText = if (isReverseMode) " (REVERSE)" else ""
        binding.instructionText.text = "Memorize these numbers$modeText..."
        
        // Show the number sequence
        binding.numberDisplay.text = formatNumberSequence(targetSequence)
        binding.numberDisplay.visibility = android.view.View.VISIBLE
        
        // Disable input during display
        setInputEnabled(false)
        
        // Hide numbers after delay
        val showDuration = getShowDuration(currentLevel)
        Handler(Looper.getMainLooper()).postDelayed({
            hideNumbers()
        }, showDuration)
    }

    private fun formatNumberSequence(sequence: String): String {
        // Add spaces between digits for better readability
        return sequence.toCharArray().joinToString(" ")
    }

    private fun getShowDuration(level: Int): Long {
        val baseTime = 1000L // 1 second per digit
        val sequenceLength = getSequenceLength(level)
        return baseTime * sequenceLength
    }

    private fun hideNumbers() {
        isShowingNumbers = false
        isPlayerTurn = true
        
        binding.numberDisplay.visibility = android.view.View.GONE
        
        val instruction = if (isReverseMode) {
            "Type the numbers in REVERSE order:"
        } else {
            "Type the numbers in the same order:"
        }
        binding.instructionText.text = instruction
        
        // Enable input
        setInputEnabled(true)
    }

    private fun addDigit(digit: String) {
        if (!isPlayerTurn) return

        val maxLength = getSequenceLength(currentLevel)

        if (playerInput.length < maxLength) {
            playerInput += digit
            binding.inputDisplay.text = playerInput

            // Auto-submit when we have the correct number of digits
            if (playerInput.length == maxLength) {
                Handler(Looper.getMainLooper()).postDelayed({
                    submitAnswer()
                }, 300) // Small delay for visual feedback
            }
        }
    }

    private fun clearInput() {
        playerInput = ""
        binding.inputDisplay.text = ""
    }

    private fun submitAnswer() {
        if (!isPlayerTurn) return

        val expectedAnswer = if (isReverseMode) {
            targetSequence.reversed()
        } else {
            targetSequence
        }

        checkAnswer(playerInput, expectedAnswer)
    }

    private fun checkAnswer(playerInput: String, expectedAnswer: String) {
        isPlayerTurn = false
        totalAttempts++
        
        val isCorrect = playerInput == expectedAnswer
        
        if (isCorrect) {
            totalCorrect++
            binding.instructionText.text = "Correct! Well done!"
            
            Handler(Looper.getMainLooper()).postDelayed({
                roundComplete()
            }, 1500)
        } else {
            binding.instructionText.text = "Incorrect. The correct sequence was: ${formatNumberSequence(expectedAnswer)}"
            
            Handler(Looper.getMainLooper()).postDelayed({
                roundFailed()
            }, 3000)
        }
        
        setInputEnabled(false)
    }

    private fun roundComplete() {
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = "Round $currentRound starting..."
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            Handler(Looper.getMainLooper()).postDelayed({
                setupGame()
            }, 1500)
        }
    }

    private fun roundFailed() {
        binding.instructionText.text = "Try again! Round $currentRound starting..."
        
        Handler(Looper.getMainLooper()).postDelayed({
            setupGame()
        }, 1500)
    }

    private fun setInputEnabled(enabled: Boolean) {
        binding.apply {
            btn0.isEnabled = enabled
            btn1.isEnabled = enabled
            btn2.isEnabled = enabled
            btn3.isEnabled = enabled
            btn4.isEnabled = enabled
            btn5.isEnabled = enabled
            btn6.isEnabled = enabled
            btn7.isEnabled = enabled
            btn8.isEnabled = enabled
            btn9.isEnabled = enabled
            btnClear.isEnabled = enabled
            btnSubmit.isEnabled = enabled
            
            val alpha = if (enabled) 1.0f else 0.5f
            numberPadContainer.alpha = alpha
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = i <= totalCorrect,
                timeSpentMs = totalTime / maxRounds,
                attempts = 1
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Time: ${totalTime / 1000}s
                Sequence Length: ${getSequenceLength(currentLevel)}

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Total Time: ${totalTime / 1000}s

                Number Memory Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Remember and type back number sequences

                📋 RULES:
                • Watch the sequence of numbers
                • Type them back in the correct order
                • At higher levels: reverse order mode
                • Complete 3 rounds to advance

                💡 TIPS:
                • Focus on the rhythm of numbers
                • Use chunking techniques (group digits)
                • Practice both forward and reverse recall

                🏆 SCORING:
                • Accuracy = correct sequences / total attempts
                • Longer sequences = higher difficulty
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
