package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityLogicalReasoningBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Logical Reasoning Game
 * Players solve deductive reasoning puzzles with premises and conclusions
 * Tests logical thinking and deductive reasoning skills
 */
class LogicalReasoningActivity : AppCompatActivity() {

    private lateinit var binding: ActivityLogicalReasoningBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "logic_2"

    // Logical reasoning specific
    private var currentPuzzleType = PuzzleType.SYLLOGISM
    private var correctAnswer = ""
    private var answerOptions = mutableListOf<String>()
    private var problemStartTime = 0L
    private var currentTrial = 0
    private var trialsPerRound = 8
    private var reactionTimes = mutableListOf<Long>()

    // Puzzle types
    private enum class PuzzleType {
        SYLLOGISM,          // All A are B, All B are C, Therefore...
        CONDITIONAL,        // If-then statements
        NEGATION,           // Not statements and logic
        CONJUNCTION,        // And/Or logic
        SET_LOGIC,          // Venn diagram logic
        SEQUENCE_LOGIC      // Pattern-based logical sequences
    }

    // Logic elements
    private val subjects = listOf("cats", "dogs", "birds", "fish", "trees", "flowers", "cars", "books", "students", "teachers")
    private val properties = listOf("red", "blue", "large", "small", "fast", "slow", "smart", "kind", "old", "new")
    private val categories = listOf("animals", "plants", "vehicles", "objects", "people", "things", "creatures", "items")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLogicalReasoningBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.logical_reasoning)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Read the premises and choose the logical conclusion!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup answer buttons
            setupAnswerButtons()
        }
    }

    private fun setupAnswerButtons() {
        binding.apply {
            btnOption1.setOnClickListener { selectAnswer(0) }
            btnOption2.setOnClickListener { selectAnswer(1) }
            btnOption3.setOnClickListener { selectAnswer(2) }
            btnOption4.setOnClickListener { selectAnswer(3) }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentTrial = 0
        reactionTimes.clear()
        trialsPerRound = getTrialsPerRound(currentLevel)
        
        binding.trialText.text = "Puzzle 1/$trialsPerRound"
        
        generateNextPuzzle()
    }

    private fun getTrialsPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 8       // 8 puzzles per round
            in 6..10 -> 10     // 10 puzzles per round
            in 11..15 -> 12    // 12 puzzles per round
            in 16..20 -> 15    // 15 puzzles per round
            else -> 18         // 18 puzzles per round
        }
    }

    private fun generateNextPuzzle() {
        if (currentTrial >= trialsPerRound) {
            roundComplete()
            return
        }

        currentTrial++
        binding.trialText.text = "Puzzle $currentTrial/$trialsPerRound"
        
        val availableTypes = getAvailablePuzzleTypes(currentLevel)
        currentPuzzleType = availableTypes.random()
        
        generateLogicPuzzle()
        problemStartTime = System.currentTimeMillis()
    }

    private fun getAvailablePuzzleTypes(level: Int): List<PuzzleType> {
        return when (level) {
            in 1..3 -> listOf(PuzzleType.SYLLOGISM)
            in 4..6 -> listOf(PuzzleType.SYLLOGISM, PuzzleType.CONDITIONAL)
            in 7..10 -> listOf(PuzzleType.SYLLOGISM, PuzzleType.CONDITIONAL, PuzzleType.NEGATION)
            in 11..15 -> listOf(PuzzleType.SYLLOGISM, PuzzleType.CONDITIONAL, PuzzleType.NEGATION, PuzzleType.CONJUNCTION)
            in 16..20 -> listOf(PuzzleType.SYLLOGISM, PuzzleType.CONDITIONAL, PuzzleType.NEGATION, PuzzleType.CONJUNCTION, PuzzleType.SET_LOGIC)
            else -> PuzzleType.values().toList()
        }
    }

    private fun generateLogicPuzzle() {
        when (currentPuzzleType) {
            PuzzleType.SYLLOGISM -> generateSyllogism()
            PuzzleType.CONDITIONAL -> generateConditional()
            PuzzleType.NEGATION -> generateNegation()
            PuzzleType.CONJUNCTION -> generateConjunction()
            PuzzleType.SET_LOGIC -> generateSetLogic()
            PuzzleType.SEQUENCE_LOGIC -> generateSequenceLogic()
        }
    }

    private fun generateSyllogism() {
        val subject1 = subjects.random()
        val subject2 = subjects.filter { it != subject1 }.random()
        val property = properties.random()
        val category = categories.random()
        
        val premises = """
            All $subject1 are $property.
            All $property things are $category.
        """.trimIndent()
        
        correctAnswer = "All $subject1 are $category"
        
        answerOptions = mutableListOf(
            correctAnswer,
            "All $category are $subject1",
            "Some $subject1 are not $category",
            "All $subject2 are $category"  // Use subject2 as a distractor
        )
        
        answerOptions.shuffle()
        
        binding.apply {
            puzzleTypeText.text = "SYLLOGISM"
            premisesText.text = premises
            questionText.text = "What can we conclude?"
        }
        
        displayAnswerOptions()
    }

    private fun generateConditional() {
        val condition = subjects.random()
        val result = properties.random()
        val subject = subjects.filter { it != condition }.random()
        
        val premises = """
            If something is a $condition, then it is $result.
            $subject is a $condition.
        """.trimIndent()
        
        correctAnswer = "$subject is $result"
        
        answerOptions = mutableListOf(
            correctAnswer,
            "$subject is not $result",
            "All $result things are $condition",
            "We cannot determine if $subject is $result"
        )
        
        answerOptions.shuffle()
        
        binding.apply {
            puzzleTypeText.text = "CONDITIONAL LOGIC"
            premisesText.text = premises
            questionText.text = "What can we conclude?"
        }
        
        displayAnswerOptions()
    }

    private fun generateNegation() {
        val subject = subjects.random()
        val property1 = properties.random()
        val property2 = properties.filter { it != property1 }.random()
        
        val premises = """
            No $subject are $property1.
            All $property1 things are $property2.
        """.trimIndent()
        
        correctAnswer = "Some $property2 things are not $subject"
        
        answerOptions = mutableListOf(
            correctAnswer,
            "All $subject are $property2",
            "No $subject are $property2",
            "All $property2 things are $subject"
        )
        
        answerOptions.shuffle()
        
        binding.apply {
            puzzleTypeText.text = "NEGATION LOGIC"
            premisesText.text = premises
            questionText.text = "What can we conclude?"
        }
        
        displayAnswerOptions()
    }

    private fun generateConjunction() {
        val subject = subjects.random()
        val property1 = properties.random()
        val property2 = properties.filter { it != property1 }.random()
        val property3 = properties.filter { it != property1 && it != property2 }.random()
        
        val premises = """
            $subject is $property1 and $property2.
            If something is $property1, then it is $property3.
        """.trimIndent()
        
        correctAnswer = "$subject is $property3"
        
        answerOptions = mutableListOf(
            correctAnswer,
            "$subject is not $property3",
            "$subject is $property3 or not $property2",
            "We cannot determine if $subject is $property3"
        )
        
        answerOptions.shuffle()
        
        binding.apply {
            puzzleTypeText.text = "CONJUNCTION LOGIC"
            premisesText.text = premises
            questionText.text = "What can we conclude?"
        }
        
        displayAnswerOptions()
    }

    private fun generateSetLogic() {
        val setA = subjects.random()
        val setB = subjects.filter { it != setA }.random()
        val element = subjects.filter { it != setA && it != setB }.random()
        
        val premises = """
            All $setA are in group X.
            Some $setB are in group X.
            $element is a $setA.
        """.trimIndent()
        
        correctAnswer = "$element is in group X"
        
        answerOptions = mutableListOf(
            correctAnswer,
            "$element is not in group X",
            "$element is a $setB",
            "All group X members are $setA"
        )
        
        answerOptions.shuffle()
        
        binding.apply {
            puzzleTypeText.text = "SET LOGIC"
            premisesText.text = premises
            questionText.text = "What can we conclude?"
        }
        
        displayAnswerOptions()
    }

    private fun generateSequenceLogic() {
        val numbers = listOf(2, 4, 8, 16)
        val pattern = "doubling"
        
        val premises = """
            In this sequence: ${numbers.joinToString(", ")}, ...
            Each number follows a specific pattern ($pattern).
            What is the next number?
        """.trimIndent()
        
        correctAnswer = "The next number is 32"
        
        answerOptions = mutableListOf(
            correctAnswer,
            "The next number is 24",
            "The next number is 20",
            "The pattern cannot be determined"
        )
        
        answerOptions.shuffle()
        
        binding.apply {
            puzzleTypeText.text = "SEQUENCE LOGIC"
            premisesText.text = premises
            questionText.text = "What comes next?"
        }
        
        displayAnswerOptions()
    }

    private fun displayAnswerOptions() {
        binding.apply {
            btnOption1.text = answerOptions[0]
            btnOption2.text = answerOptions[1]
            btnOption3.text = answerOptions[2]
            btnOption4.text = answerOptions[3]
        }
    }

    private fun selectAnswer(optionIndex: Int) {
        val selectedAnswer = answerOptions[optionIndex]
        val reactionTime = System.currentTimeMillis() - problemStartTime
        
        totalAttempts++
        val isCorrect = selectedAnswer == correctAnswer
        
        if (isCorrect) {
            totalCorrect++
            reactionTimes.add(reactionTime)
        }
        
        showFeedback(selectedAnswer, isCorrect, reactionTime)
        
        // Continue to next puzzle after delay
        Handler(Looper.getMainLooper()).postDelayed({
            generateNextPuzzle()
        }, 2500)
    }

    private fun showFeedback(selectedAnswer: String, isCorrect: Boolean, reactionTime: Long) {
        val feedback = if (isCorrect) {
            "Correct! You selected: \"$selectedAnswer\" (${reactionTime}ms)"
        } else {
            "Wrong! You selected: \"$selectedAnswer\"\nCorrect answer: \"$correctAnswer\""
        }
        
        binding.instructionText.text = feedback
        binding.instructionText.setTextColor(
            ContextCompat.getColor(this, if (isCorrect) R.color.stroop_green else R.color.stroop_red)
        )
        
        // Reset color after delay
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.text = "Read the premises and choose the logical conclusion!"
            binding.instructionText.setTextColor(
                ContextCompat.getColor(this, R.color.text_secondary)
            )
        }, 2500)
    }

    private fun roundComplete() {
        val avgReactionTime = if (reactionTimes.isNotEmpty()) {
            reactionTimes.average().toLong()
        } else 0L
        
        val roundAccuracy = if (trialsPerRound > 0) {
            (totalCorrect.toDouble() / trialsPerRound)
        } else 1.0
        
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Accuracy: ${(roundAccuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentTrial = 0
            reactionTimes.clear()
            
            Handler(Looper.getMainLooper()).postDelayed({
                generateNextPuzzle()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0
        val avgReactionTime = if (reactionTimes.isNotEmpty()) reactionTimes.average().toLong() else 0L

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7,
                timeSpentMs = totalTime / maxRounds,
                attempts = trialsPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
                Puzzles Solved: $totalCorrect/$totalAttempts

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Total Puzzles: $totalAttempts

                Logic Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Solve logical reasoning puzzles using deductive thinking

                📋 PUZZLE TYPES:
                • Syllogism: All A are B, All B are C, Therefore...
                • Conditional: If-then statements and implications
                • Negation: Logic with "not" statements
                • Conjunction: And/or logic combinations
                • Set Logic: Group membership and relationships
                • Sequence Logic: Pattern-based reasoning

                💡 TIPS:
                • Read premises carefully
                • Think step by step
                • Avoid common logical fallacies
                • Consider what MUST be true, not what might be true

                🏆 SCORING:
                • Accuracy = correct conclusions / total puzzles
                • Speed = average reasoning time
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
