package com.leapiq.braintraining.ui.games.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.databinding.ItemGameCardBinding
import com.leapiq.braintraining.data.model.Game
import com.leapiq.braintraining.data.model.GameType

class GameAdapter(
    private val onGameClick: (Game) -> Unit
) : ListAdapter<Game, GameAdapter.GameViewHolder>(DiffCallback) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GameViewHolder {
        val binding = ItemGameCardBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return GameViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON><PERSON>iewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class GameViewHolder(
        private val binding: ItemGameCardBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(game: Game) {
            binding.apply {
                gameName.text = game.name
                gameCategory.text = game.category.displayName
                gameProgress.progress = game.progressPercentage
                progressText.text = "${game.completedLevels}/${game.totalLevels} levels"
                
                // Set game image based on category
                // TODO: Set appropriate image based on game.imageResource
                
                // Handle game type visibility
                when (game.gameType) {
                    GameType.FREE -> {
                        premiumBadge.visibility = View.GONE
                        lockIcon.visibility = View.GONE
                    }
                    GameType.UNLOCKABLE -> {
                        premiumBadge.visibility = View.GONE
                        lockIcon.visibility = if (game.isUnlocked) View.GONE else View.VISIBLE
                    }
                    GameType.PREMIUM -> {
                        premiumBadge.visibility = View.VISIBLE
                        lockIcon.visibility = if (game.isUnlocked) View.GONE else View.VISIBLE
                    }
                }
                
                root.setOnClickListener {
                    onGameClick(game)
                }
            }
        }
    }

    companion object {
        private val DiffCallback = object : DiffUtil.ItemCallback<Game>() {
            override fun areItemsTheSame(oldItem: Game, newItem: Game): Boolean {
                return oldItem.id == newItem.id
            }

            override fun areContentsTheSame(oldItem: Game, newItem: Game): Boolean {
                return oldItem == newItem
            }
        }
    }
}
