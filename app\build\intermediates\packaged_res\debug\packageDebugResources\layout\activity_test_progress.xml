<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/surface_white"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="Test Progress"
        app:titleTextColor="@color/text_primary" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/empty_state_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="32dp"
        android:gravity="center"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📊"
            android:textSize="64sp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No Progress Data"
            android:textColor="@color/text_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Complete this test to see your progress and detailed analytics"
            android:textColor="@color/text_secondary"
            android:textSize="16sp"
            android:gravity="center" />

    </LinearLayout>

    <!-- Content Container -->
    <ScrollView
        android:id="@+id/content_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Progress Overview -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Progress Overview"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <!-- Statistics Grid -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="3">

                        <!-- Times Completed -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/times_completed_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0"
                                android:textColor="@color/primary_light_blue"
                                android:textSize="28sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Attempts"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:gravity="center" />

                        </LinearLayout>

                        <!-- Best Score -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/best_score_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0%"
                                android:textColor="@color/success_green"
                                android:textSize="28sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Best Score"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:gravity="center" />

                        </LinearLayout>

                        <!-- Average Score -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:padding="8dp">

                            <TextView
                                android:id="@+id/average_score_text"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="0%"
                                android:textColor="@color/warning_orange"
                                android:textSize="28sp"
                                android:textStyle="bold" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Average"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                android:gravity="center" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Progress Bar -->
                    <ProgressBar
                        android:id="@+id/progress_bar"
                        style="?android:attr/progressBarStyleHorizontal"
                        android:layout_width="match_parent"
                        android:layout_height="12dp"
                        android:layout_marginTop="16dp"
                        android:progressTint="@color/primary_light_blue"
                        android:max="100" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Trend Analysis -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Performance Trend"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:id="@+id/trend_indicator"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="📈"
                            android:textSize="32sp"
                            android:layout_marginEnd="16dp" />

                        <TextView
                            android:id="@+id/trend_description"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="Improving (5.2%)"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Detailed Statistics -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Detailed Statistics"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="16dp" />

                    <!-- Statistics Grid -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- Row 1 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="12dp">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Fastest Time"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp"
                                    android:layout_marginBottom="4dp" />

                                <TextView
                                    android:id="@+id/fastest_time_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="2m 30s"
                                    android:textColor="@color/text_primary"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Average Time"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp"
                                    android:layout_marginBottom="4dp" />

                                <TextView
                                    android:id="@+id/average_time_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="3m 15s"
                                    android:textColor="@color/text_primary"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- Row 2 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Current Streak"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp"
                                    android:layout_marginBottom="4dp" />

                                <TextView
                                    android:id="@+id/streak_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="3 attempts"
                                    android:textColor="@color/text_primary"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:gravity="center">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Consistency"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp"
                                    android:layout_marginBottom="4dp" />

                                <TextView
                                    android:id="@+id/consistency_text"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="85%"
                                    android:textColor="@color/text_primary"
                                    android:textSize="14sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Recommendations -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/recommendations_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="💡 Recommendation"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/recommendation_title"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Focus on Fundamentals"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:id="@+id/recommendation_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Build a strong foundation in the core skills tested"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Test Results -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Test Results History"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/results_recycler"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
