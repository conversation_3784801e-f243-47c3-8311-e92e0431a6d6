<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_game_card" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\item_game_card.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_game_card_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="125" endOffset="35"/></Target><Target id="@+id/game_image" view="ImageView"><Expressions/><location startLine="23" startOffset="12" endLine="29" endOffset="48"/></Target><Target id="@+id/lock_icon" view="ImageView"><Expressions/><location startLine="32" startOffset="12" endLine="43" endOffset="44"/></Target><Target id="@+id/premium_badge" view="TextView"><Expressions/><location startLine="46" startOffset="12" endLine="62" endOffset="44"/></Target><Target id="@+id/game_name" view="TextView"><Expressions/><location startLine="74" startOffset="12" endLine="83" endOffset="44"/></Target><Target id="@+id/game_category" view="TextView"><Expressions/><location startLine="86" startOffset="12" endLine="95" endOffset="37"/></Target><Target id="@+id/game_progress" view="ProgressBar"><Expressions/><location startLine="98" startOffset="12" endLine="107" endOffset="37"/></Target><Target id="@+id/progress_text" view="TextView"><Expressions/><location startLine="110" startOffset="12" endLine="119" endOffset="42"/></Target></Targets></Layout>