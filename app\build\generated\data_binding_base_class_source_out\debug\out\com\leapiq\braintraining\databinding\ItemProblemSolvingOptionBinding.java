// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemProblemSolvingOptionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final RadioButton optionRadio;

  @NonNull
  public final TextView optionText;

  @NonNull
  public final View strengthIndicator;

  @NonNull
  public final TextView styleIndicator;

  private ItemProblemSolvingOptionBinding(@NonNull LinearLayout rootView,
      @NonNull RadioButton optionRadio, @NonNull TextView optionText,
      @NonNull View strengthIndicator, @NonNull TextView styleIndicator) {
    this.rootView = rootView;
    this.optionRadio = optionRadio;
    this.optionText = optionText;
    this.strengthIndicator = strengthIndicator;
    this.styleIndicator = styleIndicator;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemProblemSolvingOptionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemProblemSolvingOptionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_problem_solving_option, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemProblemSolvingOptionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.option_radio;
      RadioButton optionRadio = ViewBindings.findChildViewById(rootView, id);
      if (optionRadio == null) {
        break missingId;
      }

      id = R.id.option_text;
      TextView optionText = ViewBindings.findChildViewById(rootView, id);
      if (optionText == null) {
        break missingId;
      }

      id = R.id.strength_indicator;
      View strengthIndicator = ViewBindings.findChildViewById(rootView, id);
      if (strengthIndicator == null) {
        break missingId;
      }

      id = R.id.style_indicator;
      TextView styleIndicator = ViewBindings.findChildViewById(rootView, id);
      if (styleIndicator == null) {
        break missingId;
      }

      return new ItemProblemSolvingOptionBinding((LinearLayout) rootView, optionRadio, optionText,
          strengthIndicator, styleIndicator);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
