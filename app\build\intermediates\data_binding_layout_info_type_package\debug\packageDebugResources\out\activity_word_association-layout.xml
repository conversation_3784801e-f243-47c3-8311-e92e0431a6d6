<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_word_association" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_word_association.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_word_association_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="237" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/trial_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="103" startOffset="4" endLine="112" endOffset="33"/></Target><Target id="@+id/target_word_display" view="TextView"><Expressions/><location startLine="121" startOffset="8" endLine="131" endOffset="34"/></Target><Target id="@+id/btn_option1" view="Button"><Expressions/><location startLine="164" startOffset="12" endLine="177" endOffset="38"/></Target><Target id="@+id/btn_option2" view="Button"><Expressions/><location startLine="179" startOffset="12" endLine="192" endOffset="40"/></Target><Target id="@+id/btn_option3" view="Button"><Expressions/><location startLine="203" startOffset="12" endLine="216" endOffset="43"/></Target><Target id="@+id/btn_option4" view="Button"><Expressions/><location startLine="218" startOffset="12" endLine="231" endOffset="37"/></Target></Targets></Layout>