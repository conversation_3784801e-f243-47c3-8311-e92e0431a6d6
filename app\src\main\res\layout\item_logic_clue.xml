<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/clue_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@color/surface_white"
    android:layout_margin="2dp"
    android:padding="12dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:id="@+id/clue_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="1."
            android:textStyle="bold"
            android:textSize="14sp"
            android:textColor="@color/text_primary"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/clue_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Direct"
            android:textSize="10sp"
            android:textStyle="bold"
            android:textColor="@color/stroop_green"
            android:background="@color/background_light_gray"
            android:padding="2dp"
            android:layout_marginEnd="8dp" />

    </LinearLayout>

    <TextView
        android:id="@+id/clue_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Alice has a cat"
        android:textSize="14sp"
        android:textColor="@color/text_primary"
        android:layout_marginTop="4dp"
        android:lineSpacingExtra="2dp" />

</LinearLayout>
