<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_spatial_rotation" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_spatial_rotation.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_spatial_rotation_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="257" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/trial_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/shape_type_text" view="TextView"><Expressions/><location startLine="103" startOffset="4" endLine="113" endOffset="34"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="116" startOffset="4" endLine="125" endOffset="33"/></Target><Target id="@+id/original_shape_display" view="TextView"><Expressions/><location startLine="154" startOffset="12" endLine="166" endOffset="74"/></Target><Target id="@+id/rotated_shape_display" view="TextView"><Expressions/><location startLine="188" startOffset="12" endLine="200" endOffset="59"/></Target><Target id="@+id/rotation_info_text" view="TextView"><Expressions/><location startLine="207" startOffset="4" endLine="217" endOffset="36"/></Target><Target id="@+id/btn_match" view="Button"><Expressions/><location startLine="227" startOffset="8" endLine="239" endOffset="37"/></Target><Target id="@+id/btn_no_match" view="Button"><Expressions/><location startLine="241" startOffset="8" endLine="253" endOffset="37"/></Target></Targets></Layout>