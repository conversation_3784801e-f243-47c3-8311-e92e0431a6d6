<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_my_results" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_my_results.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_my_results_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="583" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="50"/></Target><Target id="@+id/empty_state_container" view="LinearLayout"><Expressions/><location startLine="30" startOffset="12" endLine="72" endOffset="26"/></Target><Target id="@+id/content_container" view="LinearLayout"><Expressions/><location startLine="75" startOffset="12" endLine="577" endOffset="26"/></Target><Target id="@+id/total_tests_text" view="TextView"><Expressions/><location startLine="121" startOffset="32" endLine="128" endOffset="62"/></Target><Target id="@+id/cognitive_tests_text" view="TextView"><Expressions/><location startLine="149" startOffset="32" endLine="156" endOffset="62"/></Target><Target id="@+id/personality_tests_text" view="TextView"><Expressions/><location startLine="177" startOffset="32" endLine="184" endOffset="62"/></Target><Target id="@+id/cognitive_score_container" view="LinearLayout"><Expressions/><location startLine="207" startOffset="28" endLine="234" endOffset="42"/></Target><Target id="@+id/avg_cognitive_score_text" view="TextView"><Expressions/><location startLine="225" startOffset="32" endLine="232" endOffset="62"/></Target><Target id="@+id/personality_score_container" view="LinearLayout"><Expressions/><location startLine="237" startOffset="28" endLine="264" endOffset="42"/></Target><Target id="@+id/avg_personality_score_text" view="TextView"><Expressions/><location startLine="255" startOffset="32" endLine="262" endOffset="62"/></Target><Target id="@+id/overall_progress_bar" view="ProgressBar"><Expressions/><location startLine="295" startOffset="24" endLine="302" endOffset="47"/></Target><Target id="@+id/overall_progress_text" view="TextView"><Expressions/><location startLine="304" startOffset="24" endLine="311" endOffset="64"/></Target><Target id="@+id/improving_tests_text" view="TextView"><Expressions/><location startLine="327" startOffset="32" endLine="334" endOffset="62"/></Target><Target id="@+id/stable_tests_text" view="TextView"><Expressions/><location startLine="352" startOffset="32" endLine="359" endOffset="62"/></Target><Target id="@+id/declining_tests_text" view="TextView"><Expressions/><location startLine="377" startOffset="32" endLine="384" endOffset="62"/></Target><Target id="@+id/insights_container" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="402" startOffset="16" endLine="461" endOffset="67"/></Target><Target id="@+id/insight_priority_indicator" view="View"><Expressions/><location startLine="417" startOffset="24" endLine="422" endOffset="76"/></Target><Target id="@+id/insight_title" view="TextView"><Expressions/><location startLine="429" startOffset="28" endLine="437" endOffset="67"/></Target><Target id="@+id/insight_description" view="TextView"><Expressions/><location startLine="439" startOffset="28" endLine="446" endOffset="67"/></Target><Target id="@+id/insight_action" view="TextView"><Expressions/><location startLine="448" startOffset="28" endLine="455" endOffset="60"/></Target><Target id="@+id/test_progress_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="486" startOffset="24" endLine="489" endOffset="66"/></Target><Target id="@+id/recent_activity_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="518" startOffset="24" endLine="521" endOffset="66"/></Target><Target id="@+id/btn_view_all_results" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="533" startOffset="20" endLine="541" endOffset="48"/></Target><Target id="@+id/btn_analytics" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="549" startOffset="24" endLine="559" endOffset="73"/></Target><Target id="@+id/btn_export_data" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="561" startOffset="24" endLine="571" endOffset="73"/></Target></Targets></Layout>