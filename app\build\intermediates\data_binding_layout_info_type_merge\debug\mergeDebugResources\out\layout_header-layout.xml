<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="layout_header" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\layout_header.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/layout_header_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="83" endOffset="14"/></Target><Target id="@+id/header_title" view="TextView"><Expressions/><location startLine="10" startOffset="4" endLine="14" endOffset="45"/></Target><Target id="@+id/header_premium" view="TextView"><Expressions/><location startLine="24" startOffset="8" endLine="38" endOffset="38"/></Target><Target id="@+id/header_streak" view="LinearLayout"><Expressions/><location startLine="41" startOffset="8" endLine="67" endOffset="22"/></Target><Target id="@+id/streak_count" view="TextView"><Expressions/><location startLine="58" startOffset="12" endLine="65" endOffset="42"/></Target><Target id="@+id/header_settings" view="ImageView"><Expressions/><location startLine="70" startOffset="8" endLine="79" endOffset="38"/></Target></Targets></Layout>