<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_game_result" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_game_result.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_game_result_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="232" endOffset="14"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="13" startOffset="4" endLine="22" endOffset="36"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="25" startOffset="4" endLine="33" endOffset="40"/></Target><Target id="@+id/performance_message" view="TextView"><Expressions/><location startLine="50" startOffset="12" endLine="60" endOffset="61"/></Target><Target id="@+id/accuracy_text" view="TextView"><Expressions/><location startLine="78" startOffset="16" endLine="86" endOffset="38"/></Target><Target id="@+id/accuracy_progress" view="ProgressBar"><Expressions/><location startLine="91" startOffset="12" endLine="100" endOffset="79"/></Target><Target id="@+id/time_text" view="TextView"><Expressions/><location startLine="118" startOffset="16" endLine="125" endOffset="39"/></Target><Target id="@+id/score_text" view="TextView"><Expressions/><location startLine="145" startOffset="16" endLine="153" endOffset="37"/></Target><Target id="@+id/rounds_text" view="TextView"><Expressions/><location startLine="172" startOffset="16" endLine="179" endOffset="38"/></Target><Target id="@+id/next_level_button" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="195" startOffset="8" endLine="205" endOffset="41"/></Target><Target id="@+id/replay_button" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="208" startOffset="8" endLine="217" endOffset="39"/></Target><Target id="@+id/back_to_games_button" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="220" startOffset="8" endLine="228" endOffset="40"/></Target></Targets></Layout>