package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityEstimationBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.math.abs
import kotlin.math.pow
import kotlin.random.Random

/**
 * Estimation Game
 * Players estimate quantities, sizes, and calculations without exact counting
 * Tests number sense and approximation skills
 */
class EstimationActivity : AppCompatActivity() {

    private lateinit var binding: ActivityEstimationBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "math_4"

    // Estimation specific
    private var currentEstimationType = EstimationType.DOT_COUNT
    private var correctAnswer = 0
    private var problemStartTime = 0L
    private var currentTrial = 0
    private var trialsPerRound = 10
    private var reactionTimes = mutableListOf<Long>()
    private var estimationAccuracies = mutableListOf<Double>()

    // Estimation types
    private enum class EstimationType {
        DOT_COUNT,          // Count dots in a grid
        LINE_LENGTH,        // Estimate line length
        ANGLE_SIZE,         // Estimate angle degrees
        CALCULATION,        // Estimate calculation result
        TIME_DURATION,      // Estimate time passage
        QUANTITY_COMPARE    // Compare quantities
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEstimationBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.estimation)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Make your best estimate!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup number pad
            setupNumberPad()

            // Setup control buttons
            btnClear.setOnClickListener {
                clearInput()
            }

            btnSubmit.setOnClickListener {
                submitAnswer()
            }
        }
    }

    private fun setupNumberPad() {
        binding.apply {
            btn0.setOnClickListener { addDigit("0") }
            btn1.setOnClickListener { addDigit("1") }
            btn2.setOnClickListener { addDigit("2") }
            btn3.setOnClickListener { addDigit("3") }
            btn4.setOnClickListener { addDigit("4") }
            btn5.setOnClickListener { addDigit("5") }
            btn6.setOnClickListener { addDigit("6") }
            btn7.setOnClickListener { addDigit("7") }
            btn8.setOnClickListener { addDigit("8") }
            btn9.setOnClickListener { addDigit("9") }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentTrial = 0
        reactionTimes.clear()
        estimationAccuracies.clear()
        trialsPerRound = getTrialsPerRound(currentLevel)
        
        binding.trialText.text = "Estimation 1/$trialsPerRound"
        
        generateNextEstimation()
    }

    private fun getTrialsPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 10      // 10 estimations per round
            in 6..10 -> 12     // 12 estimations per round
            in 11..15 -> 15    // 15 estimations per round
            in 16..20 -> 18    // 18 estimations per round
            else -> 20         // 20 estimations per round
        }
    }

    private fun generateNextEstimation() {
        if (currentTrial >= trialsPerRound) {
            roundComplete()
            return
        }

        currentTrial++
        binding.trialText.text = "Estimation $currentTrial/$trialsPerRound"
        
        val availableTypes = getAvailableEstimationTypes(currentLevel)
        currentEstimationType = availableTypes.random()
        
        generateEstimationProblem()
        clearInput()
        problemStartTime = System.currentTimeMillis()
    }

    private fun getAvailableEstimationTypes(level: Int): List<EstimationType> {
        return when (level) {
            in 1..3 -> listOf(EstimationType.DOT_COUNT)
            in 4..6 -> listOf(EstimationType.DOT_COUNT, EstimationType.LINE_LENGTH)
            in 7..10 -> listOf(EstimationType.DOT_COUNT, EstimationType.LINE_LENGTH, EstimationType.CALCULATION)
            in 11..15 -> listOf(EstimationType.DOT_COUNT, EstimationType.LINE_LENGTH, EstimationType.CALCULATION, EstimationType.ANGLE_SIZE)
            in 16..20 -> listOf(EstimationType.DOT_COUNT, EstimationType.LINE_LENGTH, EstimationType.CALCULATION, EstimationType.ANGLE_SIZE, EstimationType.TIME_DURATION)
            else -> EstimationType.values().toList()
        }
    }

    private fun generateEstimationProblem() {
        when (currentEstimationType) {
            EstimationType.DOT_COUNT -> generateDotCountProblem()
            EstimationType.LINE_LENGTH -> generateLineLengthProblem()
            EstimationType.ANGLE_SIZE -> generateAngleSizeProblem()
            EstimationType.CALCULATION -> generateCalculationProblem()
            EstimationType.TIME_DURATION -> generateTimeDurationProblem()
            EstimationType.QUANTITY_COMPARE -> generateQuantityCompareProblem()
        }
    }

    private fun generateDotCountProblem() {
        val difficulty = getDifficulty(currentLevel)
        correctAnswer = when (difficulty) {
            1 -> Random.nextInt(15, 35)    // 15-35 dots
            2 -> Random.nextInt(30, 60)    // 30-60 dots
            3 -> Random.nextInt(50, 100)   // 50-100 dots
            4 -> Random.nextInt(80, 150)   // 80-150 dots
            5 -> Random.nextInt(120, 200)  // 120-200 dots
            else -> Random.nextInt(150, 300) // 150-300 dots
        }
        
        binding.apply {
            estimationDisplay.text = generateDotPattern(correctAnswer)
            instructionText.text = "How many dots do you see? (Don't count exactly!)"
            estimationTypeText.text = "DOT COUNTING"
        }
    }

    private fun generateDotPattern(count: Int): String {
        // Generate a visual representation of dots
        val rows = when {
            count <= 50 -> 8
            count <= 100 -> 12
            count <= 200 -> 16
            else -> 20
        }
        
        val cols = (count + rows - 1) / rows
        val pattern = StringBuilder()
        
        var dotsPlaced = 0
        for (i in 0 until rows) {
            for (j in 0 until cols) {
                if (dotsPlaced < count && Random.nextFloat() < 0.8f) {
                    pattern.append("● ")
                    dotsPlaced++
                } else {
                    pattern.append("  ")
                }
            }
            pattern.append("\n")
        }
        
        // Ensure we have the right number of dots
        while (dotsPlaced < count) {
            pattern.append("● ")
            dotsPlaced++
        }
        
        return pattern.toString()
    }

    private fun generateLineLengthProblem() {
        val difficulty = getDifficulty(currentLevel)
        correctAnswer = when (difficulty) {
            1 -> Random.nextInt(5, 15)     // 5-15 cm
            2 -> Random.nextInt(10, 25)    // 10-25 cm
            3 -> Random.nextInt(20, 50)    // 20-50 cm
            4 -> Random.nextInt(40, 80)    // 40-80 cm
            5 -> Random.nextInt(60, 120)   // 60-120 cm
            else -> Random.nextInt(100, 200) // 100-200 cm
        }
        
        binding.apply {
            estimationDisplay.text = generateLineVisualization(correctAnswer)
            instructionText.text = "Estimate the length of this line in centimeters:"
            estimationTypeText.text = "LINE LENGTH"
        }
    }

    private fun generateLineVisualization(length: Int): String {
        val lineLength = (length / 2).coerceIn(10, 50) // Scale for display
        return "━".repeat(lineLength)
    }

    private fun generateAngleSizeProblem() {
        correctAnswer = Random.nextInt(15, 165) // 15-165 degrees
        
        binding.apply {
            estimationDisplay.text = generateAngleVisualization(correctAnswer)
            instructionText.text = "Estimate the angle size in degrees:"
            estimationTypeText.text = "ANGLE SIZE"
        }
    }

    private fun generateAngleVisualization(degrees: Int): String {
        // Simple text representation of angle
        return when {
            degrees < 30 -> "  /\n /"
            degrees < 60 -> " /\n/"
            degrees < 90 -> "|\n\\"
            degrees < 120 -> "|\n—"
            degrees < 150 -> "—\n\\"
            else -> "—\n—"
        }
    }

    private fun generateCalculationProblem() {
        val difficulty = getDifficulty(currentLevel)
        val (a, b, operation) = when (difficulty) {
            1 -> Triple(Random.nextInt(10, 50), Random.nextInt(10, 50), "+")
            2 -> Triple(Random.nextInt(20, 100), Random.nextInt(20, 100), if (Random.nextBoolean()) "+" else "-")
            3 -> Triple(Random.nextInt(10, 30), Random.nextInt(10, 30), "×")
            4 -> Triple(Random.nextInt(50, 200), Random.nextInt(50, 200), if (Random.nextBoolean()) "+" else "×")
            5 -> Triple(Random.nextInt(100, 500), Random.nextInt(100, 500), listOf("+", "-", "×").random())
            else -> Triple(Random.nextInt(200, 1000), Random.nextInt(200, 1000), listOf("+", "-", "×").random())
        }
        
        correctAnswer = when (operation) {
            "+" -> a + b
            "-" -> a - b
            "×" -> a * b
            else -> a + b
        }
        
        binding.apply {
            estimationDisplay.text = "$a $operation $b = ?"
            instructionText.text = "Estimate the result (don't calculate exactly!):"
            estimationTypeText.text = "CALCULATION"
        }
    }

    private fun generateTimeDurationProblem() {
        correctAnswer = Random.nextInt(3, 15) // 3-15 seconds
        
        binding.apply {
            estimationDisplay.text = "⏱️ Timer will start..."
            instructionText.text = "Estimate how long the timer runs (in seconds):"
            estimationTypeText.text = "TIME DURATION"
        }
        
        // Start the timer
        Handler(Looper.getMainLooper()).postDelayed({
            binding.estimationDisplay.text = "⏱️ Timer running..."
        }, 1000)
        
        Handler(Looper.getMainLooper()).postDelayed({
            binding.estimationDisplay.text = "⏱️ Timer stopped!"
        }, correctAnswer * 1000L)
    }

    private fun generateQuantityCompareProblem() {
        val quantity1 = Random.nextInt(20, 100)
        val quantity2 = Random.nextInt(20, 100)
        correctAnswer = if (quantity1 > quantity2) 1 else 2
        
        binding.apply {
            estimationDisplay.text = """
                Group A: ${"●".repeat(quantity1 / 5)}
                Group B: ${"●".repeat(quantity2 / 5)}
            """.trimIndent()
            instructionText.text = "Which group has more? (1 for A, 2 for B)"
            estimationTypeText.text = "QUANTITY COMPARE"
        }
    }

    private fun getDifficulty(level: Int): Int {
        return when (level) {
            in 1..3 -> 1
            in 4..6 -> 2
            in 7..10 -> 3
            in 11..15 -> 4
            in 16..20 -> 5
            else -> 6
        }
    }

    private fun addDigit(digit: String) {
        val currentInput = binding.answerInput.text.toString()
        if (currentInput.length < 6) {
            binding.answerInput.text = currentInput + digit
        }
    }

    private fun clearInput() {
        binding.answerInput.text = ""
    }

    private fun submitAnswer() {
        val userInput = binding.answerInput.text.toString()
        if (userInput.isEmpty()) return
        
        val userAnswer = userInput.toIntOrNull() ?: return
        val reactionTime = System.currentTimeMillis() - problemStartTime
        
        totalAttempts++
        val accuracy = calculateEstimationAccuracy(userAnswer, correctAnswer)
        val isCorrect = accuracy >= 0.7 // 70% accuracy threshold
        
        if (isCorrect) {
            totalCorrect++
        }
        
        reactionTimes.add(reactionTime)
        estimationAccuracies.add(accuracy)
        
        showFeedback(userAnswer, accuracy, reactionTime)
        
        // Continue to next estimation after delay
        Handler(Looper.getMainLooper()).postDelayed({
            generateNextEstimation()
        }, 2000)
    }

    private fun calculateEstimationAccuracy(estimate: Int, actual: Int): Double {
        if (actual == 0) return if (estimate == 0) 1.0 else 0.0
        val error = abs(estimate - actual).toDouble() / actual
        return (1.0 - error).coerceIn(0.0, 1.0)
    }

    private fun showFeedback(userAnswer: Int, accuracy: Double, reactionTime: Long) {
        val accuracyPercent = (accuracy * 100).toInt()
        val feedback = """
            Your estimate: $userAnswer
            Actual: $correctAnswer
            Accuracy: $accuracyPercent%
            Time: ${reactionTime}ms
        """.trimIndent()
        
        binding.instructionText.text = feedback
        binding.instructionText.setTextColor(
            ContextCompat.getColor(this, if (accuracy >= 0.7) R.color.stroop_green else R.color.stroop_red)
        )
        
        // Reset color after delay
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.setTextColor(
                ContextCompat.getColor(this, R.color.text_secondary)
            )
        }, 2000)
    }

    private fun roundComplete() {
        val avgReactionTime = if (reactionTimes.isNotEmpty()) {
            reactionTimes.average().toLong()
        } else 0L
        
        val avgAccuracy = if (estimationAccuracies.isNotEmpty()) {
            estimationAccuracies.average()
        } else 1.0
        
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Avg Accuracy: ${(avgAccuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentTrial = 0
            reactionTimes.clear()
            estimationAccuracies.clear()
            
            Handler(Looper.getMainLooper()).postDelayed({
                generateNextEstimation()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val overallAccuracy = if (estimationAccuracies.isNotEmpty()) {
            estimationAccuracies.average()
        } else 1.0
        val avgReactionTime = if (reactionTimes.isNotEmpty()) reactionTimes.average().toLong() else 0L

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = overallAccuracy > 0.7,
                timeSpentMs = totalTime / maxRounds,
                attempts = trialsPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = overallAccuracy,
            score = (overallAccuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Avg Accuracy: ${(overallAccuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
                Estimations: $totalAttempts

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(overallAccuracy * 100).toInt()}%
                Total Estimations: $totalAttempts

                Estimation Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Make accurate estimates without exact counting

                📋 ESTIMATION TYPES:
                • Dot counting: Estimate number of dots
                • Line length: Estimate length in centimeters
                • Angle size: Estimate angle in degrees
                • Calculations: Estimate math results
                • Time duration: Estimate elapsed time
                • Quantity compare: Compare group sizes

                💡 TIPS:
                • Don't count exactly - use your intuition
                • Look for patterns and groupings
                • Practice improves number sense
                • Close estimates are better than exact counting

                🏆 SCORING:
                • Accuracy = how close your estimate is
                • 70%+ accuracy counts as correct
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
