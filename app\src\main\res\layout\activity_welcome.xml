<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_white">

    <!-- ViewPager for welcome pages -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Tab Layout (dots indicator) -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        app:tabBackground="@drawable/tab_selector"
        app:tabGravity="center"
        app:tabIndicatorHeight="0dp"
        app:tabMaxWidth="12dp"
        app:tabMinWidth="12dp" />

    <!-- Navigation buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="24dp"
        android:gravity="center_vertical">

        <!-- Previous button -->
        <Button
            android:id="@+id/btnPrevious"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Previous"
            android:background="@drawable/button_secondary"
            android:textColor="@color/primary_light_blue"
            android:visibility="invisible" />

        <!-- Skip button -->
        <Button
            android:id="@+id/btnSkip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Skip"
            android:background="@android:color/transparent"
            android:textColor="@color/text_secondary"
            android:gravity="center" />

        <!-- Next/Get Started button -->
        <Button
            android:id="@+id/btnNext"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Next"
            android:background="@drawable/button_primary"
            android:textColor="@color/text_white" />

    </LinearLayout>

</LinearLayout>
