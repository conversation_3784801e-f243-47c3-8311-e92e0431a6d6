<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.games.SpatialRotationActivity">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/primary_light_blue"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Quit Button -->
        <ImageButton
            android:id="@+id/btn_quit"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="Quit game"
            app:tint="@color/text_white" />

        <!-- Game Title -->
        <TextView
            android:id="@+id/game_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Spatial Rotation"
            android:textColor="@color/text_white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Menu Button -->
        <ImageButton
            android:id="@+id/btn_menu"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_menu"
            android:contentDescription="Game menu"
            app:tint="@color/text_white" />

    </LinearLayout>

    <!-- Game Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:elevation="2dp"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Level -->
        <TextView
            android:id="@+id/level_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Level 1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Round -->
        <TextView
            android:id="@+id/round_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Round 1/3"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Trial -->
        <TextView
            android:id="@+id/trial_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Shape 1/10"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Shape Type -->
    <TextView
        android:id="@+id/shape_type_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_light_blue_light"
        android:gravity="center"
        android:padding="12dp"
        android:text="CUBE"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold" />

    <!-- Instructions -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:padding="16dp"
        android:text="Do these shapes match when rotated?"
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />

    <!-- Shape Comparison -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/surface_white"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Original Shape -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginEnd="8dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="ORIGINAL"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/original_shape_display"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/spatial_shape_background"
                android:fontFamily="monospace"
                android:gravity="center"
                android:padding="16dp"
                android:text="┌─────┐\n│     │\n│     │\n│     │\n└─────┘"
                android:textColor="@color/text_primary"
                android:textSize="12sp"
                tools:text="┌─────┐\n│     │\n│     │\n│     │\n└─────┘" />

        </LinearLayout>

        <!-- Rotated Shape -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:layout_marginStart="8dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="ROTATED"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/rotated_shape_display"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="@drawable/spatial_shape_background"
                android:fontFamily="monospace"
                android:gravity="center"
                android:padding="16dp"
                android:text="┌─┐\n│ │\n│ │\n│ │\n│ │\n└─┘"
                android:textColor="@color/text_primary"
                android:textSize="12sp"
                tools:text="┌─┐\n│ │\n│ │\n│ │\n│ │\n└─┘" />

        </LinearLayout>

    </LinearLayout>

    <!-- Rotation Info -->
    <TextView
        android:id="@+id/rotation_info_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:padding="12dp"
        android:text="Rotation: 90° X-axis"
        android:textColor="@color/text_secondary"
        android:textSize="14sp"
        android:textStyle="italic" />

    <!-- Answer Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:orientation="horizontal"
        android:padding="16dp">

        <Button
            android:id="@+id/btn_match"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:text="MATCH"
            android:textSize="16sp"
            android:textStyle="bold"
            android:backgroundTint="@color/stroop_green"
            android:textColor="@color/text_white"
            app:cornerRadius="12dp"
            android:elevation="4dp" />

        <Button
            android:id="@+id/btn_no_match"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:text="NO MATCH"
            android:textSize="16sp"
            android:textStyle="bold"
            android:backgroundTint="@color/stroop_red"
            android:textColor="@color/text_white"
            app:cornerRadius="12dp"
            android:elevation="4dp" />

    </LinearLayout>

</LinearLayout>
