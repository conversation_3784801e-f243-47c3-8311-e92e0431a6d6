<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <!-- Date and Time -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginEnd="16dp">

            <TextView
                android:id="@+id/date_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Dec 15"
                android:textColor="@color/text_primary"
                android:textSize="12sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/time_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="14:30"
                android:textColor="@color/text_secondary"
                android:textSize="10sp" />

        </LinearLayout>

        <!-- Result Details -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- Score with Icon -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="4dp">

                <TextView
                    android:id="@+id/score_icon"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="⭐"
                    android:textSize="16sp"
                    android:layout_marginEnd="4dp" />

                <TextView
                    android:id="@+id/score_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="85%"
                    android:textColor="@color/success_green"
                    android:textSize="18sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Metrics -->
            <TextView
                android:id="@+id/accuracy_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Accuracy: 85%"
                android:textColor="@color/text_secondary"
                android:textSize="11sp" />

            <TextView
                android:id="@+id/response_time_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Avg Response: 2.5s"
                android:textColor="@color/text_secondary"
                android:textSize="11sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/total_time_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Total: 5m 30s"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp" />

                <TextView
                    android:id="@+id/questions_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="12 questions"
                    android:textColor="@color/text_secondary"
                    android:textSize="11sp" />

            </LinearLayout>

            <!-- Insight -->
            <TextView
                android:id="@+id/insight_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Great improvement in working memory tasks"
                android:textColor="@color/primary_light_blue"
                android:textSize="11sp"
                android:textStyle="italic"
                android:layout_marginTop="4dp"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
