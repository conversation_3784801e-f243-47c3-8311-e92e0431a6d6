<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_number_memory" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_number_memory.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_number_memory_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="319" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="91" startOffset="4" endLine="100" endOffset="33"/></Target><Target id="@+id/number_display" view="TextView"><Expressions/><location startLine="110" startOffset="8" endLine="121" endOffset="36"/></Target><Target id="@+id/input_display" view="TextView"><Expressions/><location startLine="124" startOffset="8" endLine="135" endOffset="39"/></Target><Target id="@+id/number_pad_container" view="LinearLayout"><Expressions/><location startLine="140" startOffset="4" endLine="304" endOffset="18"/></Target><Target id="@+id/btn_1" view="Button"><Expressions/><location startLine="157" startOffset="12" endLine="168" endOffset="41"/></Target><Target id="@+id/btn_2" view="Button"><Expressions/><location startLine="170" startOffset="12" endLine="181" endOffset="41"/></Target><Target id="@+id/btn_3" view="Button"><Expressions/><location startLine="183" startOffset="12" endLine="194" endOffset="41"/></Target><Target id="@+id/btn_4" view="Button"><Expressions/><location startLine="197" startOffset="12" endLine="208" endOffset="41"/></Target><Target id="@+id/btn_5" view="Button"><Expressions/><location startLine="210" startOffset="12" endLine="221" endOffset="41"/></Target><Target id="@+id/btn_6" view="Button"><Expressions/><location startLine="223" startOffset="12" endLine="234" endOffset="41"/></Target><Target id="@+id/btn_7" view="Button"><Expressions/><location startLine="237" startOffset="12" endLine="248" endOffset="41"/></Target><Target id="@+id/btn_8" view="Button"><Expressions/><location startLine="250" startOffset="12" endLine="261" endOffset="41"/></Target><Target id="@+id/btn_9" view="Button"><Expressions/><location startLine="263" startOffset="12" endLine="274" endOffset="41"/></Target><Target id="@+id/btn_0" view="Button"><Expressions/><location startLine="283" startOffset="12" endLine="294" endOffset="41"/></Target><Target id="@+id/btn_clear" view="Button"><Expressions/><location startLine="307" startOffset="4" endLine="311" endOffset="35"/></Target><Target id="@+id/btn_submit" view="Button"><Expressions/><location startLine="313" startOffset="4" endLine="317" endOffset="35"/></Target></Targets></Layout>