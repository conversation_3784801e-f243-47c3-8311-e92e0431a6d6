package com.leapiq.braintraining.ui.games.model

/**
 * Represents a tube containing colored balls
 */
data class Tube(
    val balls: MutableList<Ball>
) {
    fun isEmpty(): Boolean = balls.isEmpty()
    fun isFull(): Boolean = balls.size >= 4
    fun getTopBall(): Ball? = balls.lastOrNull()
    fun canAcceptBall(ball: Ball): Boolean {
        return !isFull() && (isEmpty() || getTopBall()?.color == ball.color)
    }
}
