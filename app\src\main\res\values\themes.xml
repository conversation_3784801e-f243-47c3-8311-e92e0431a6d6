<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Theme.LeapIQ" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary brand color. -->
        <item name="colorPrimary">@color/primary_light_blue</item>
        <item name="colorPrimaryVariant">@color/primary_light_blue_dark</item>
        <item name="colorOnPrimary">@color/text_white</item>
        
        <!-- Secondary brand color. -->
        <item name="colorSecondary">@color/secondary_blue</item>
        <item name="colorSecondaryVariant">@color/secondary_blue_variant</item>
        <item name="colorOnSecondary">@color/text_primary</item>
        
        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_white</item>
        <item name="colorSurface">@color/surface_white</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>
        
        <!-- Status bar -->
        <item name="android:statusBarColor">@color/primary_light_blue_dark</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/background_white</item>
        <item name="android:windowLightNavigationBar">true</item>
    </style>
    
    <!-- Custom styles for LeapIQ components -->
    <style name="LeapIQ">
        <!-- Base style for LeapIQ components -->
    </style>

    <style name="LeapIQ.Header">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:background">@color/primary_light_blue</item>
        <item name="android:elevation">4dp</item>
    </style>
    
    <style name="LeapIQ.HeaderText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_white</item>
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
    </style>
    
    <style name="LeapIQ.GameCard">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:background">@drawable/game_card_background</item>
        <item name="android:elevation">2dp</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>
    
    <style name="LeapIQ.GameTitle">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:padding">8dp</item>
    </style>

    <!-- Splash Screen Theme -->
    <style name="Theme.LeapIQ.Splash" parent="Theme.Material3.DayNight.NoActionBar">
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:statusBarColor">@color/background_white</item>
        <item name="android:navigationBarColor">@color/background_white</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">false</item>
    </style>
</resources>
