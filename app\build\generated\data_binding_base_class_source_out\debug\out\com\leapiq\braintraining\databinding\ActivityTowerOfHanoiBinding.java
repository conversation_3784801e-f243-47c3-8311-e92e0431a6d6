// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTowerOfHanoiBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnHint;

  @NonNull
  public final ImageButton btnMenu;

  @NonNull
  public final ImageButton btnQuit;

  @NonNull
  public final Button btnReset;

  @NonNull
  public final TextView disksText;

  @NonNull
  public final TextView gameTitle;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final TextView levelText;

  @NonNull
  public final TextView minMovesText;

  @NonNull
  public final TextView movesText;

  @NonNull
  public final TextView puzzleText;

  @NonNull
  public final TextView roundText;

  @NonNull
  public final RecyclerView towersRecyclerView;

  private ActivityTowerOfHanoiBinding(@NonNull LinearLayout rootView, @NonNull Button btnHint,
      @NonNull ImageButton btnMenu, @NonNull ImageButton btnQuit, @NonNull Button btnReset,
      @NonNull TextView disksText, @NonNull TextView gameTitle, @NonNull TextView instructionText,
      @NonNull TextView levelText, @NonNull TextView minMovesText, @NonNull TextView movesText,
      @NonNull TextView puzzleText, @NonNull TextView roundText,
      @NonNull RecyclerView towersRecyclerView) {
    this.rootView = rootView;
    this.btnHint = btnHint;
    this.btnMenu = btnMenu;
    this.btnQuit = btnQuit;
    this.btnReset = btnReset;
    this.disksText = disksText;
    this.gameTitle = gameTitle;
    this.instructionText = instructionText;
    this.levelText = levelText;
    this.minMovesText = minMovesText;
    this.movesText = movesText;
    this.puzzleText = puzzleText;
    this.roundText = roundText;
    this.towersRecyclerView = towersRecyclerView;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTowerOfHanoiBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTowerOfHanoiBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_tower_of_hanoi, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTowerOfHanoiBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_hint;
      Button btnHint = ViewBindings.findChildViewById(rootView, id);
      if (btnHint == null) {
        break missingId;
      }

      id = R.id.btn_menu;
      ImageButton btnMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnMenu == null) {
        break missingId;
      }

      id = R.id.btn_quit;
      ImageButton btnQuit = ViewBindings.findChildViewById(rootView, id);
      if (btnQuit == null) {
        break missingId;
      }

      id = R.id.btn_reset;
      Button btnReset = ViewBindings.findChildViewById(rootView, id);
      if (btnReset == null) {
        break missingId;
      }

      id = R.id.disks_text;
      TextView disksText = ViewBindings.findChildViewById(rootView, id);
      if (disksText == null) {
        break missingId;
      }

      id = R.id.game_title;
      TextView gameTitle = ViewBindings.findChildViewById(rootView, id);
      if (gameTitle == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.level_text;
      TextView levelText = ViewBindings.findChildViewById(rootView, id);
      if (levelText == null) {
        break missingId;
      }

      id = R.id.min_moves_text;
      TextView minMovesText = ViewBindings.findChildViewById(rootView, id);
      if (minMovesText == null) {
        break missingId;
      }

      id = R.id.moves_text;
      TextView movesText = ViewBindings.findChildViewById(rootView, id);
      if (movesText == null) {
        break missingId;
      }

      id = R.id.puzzle_text;
      TextView puzzleText = ViewBindings.findChildViewById(rootView, id);
      if (puzzleText == null) {
        break missingId;
      }

      id = R.id.round_text;
      TextView roundText = ViewBindings.findChildViewById(rootView, id);
      if (roundText == null) {
        break missingId;
      }

      id = R.id.towers_recycler_view;
      RecyclerView towersRecyclerView = ViewBindings.findChildViewById(rootView, id);
      if (towersRecyclerView == null) {
        break missingId;
      }

      return new ActivityTowerOfHanoiBinding((LinearLayout) rootView, btnHint, btnMenu, btnQuit,
          btnReset, disksText, gameTitle, instructionText, levelText, minMovesText, movesText,
          puzzleText, roundText, towersRecyclerView);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
