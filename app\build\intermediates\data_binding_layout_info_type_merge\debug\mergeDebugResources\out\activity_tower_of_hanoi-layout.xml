<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_tower_of_hanoi" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_tower_of_hanoi.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_tower_of_hanoi_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="207" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/puzzle_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/disks_text" view="TextView"><Expressions/><location startLine="111" startOffset="8" endLine="119" endOffset="37"/></Target><Target id="@+id/moves_text" view="TextView"><Expressions/><location startLine="122" startOffset="8" endLine="130" endOffset="37"/></Target><Target id="@+id/min_moves_text" view="TextView"><Expressions/><location startLine="133" startOffset="8" endLine="141" endOffset="37"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="146" startOffset="4" endLine="155" endOffset="33"/></Target><Target id="@+id/towers_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="164" startOffset="8" endLine="169" endOffset="55"/></Target><Target id="@+id/btn_hint" view="Button"><Expressions/><location startLine="181" startOffset="8" endLine="191" endOffset="36"/></Target><Target id="@+id/btn_reset" view="Button"><Expressions/><location startLine="193" startOffset="8" endLine="203" endOffset="36"/></Target></Targets></Layout>