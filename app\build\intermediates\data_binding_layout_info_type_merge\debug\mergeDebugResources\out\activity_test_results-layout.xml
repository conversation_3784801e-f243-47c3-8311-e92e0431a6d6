<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_test_results" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_test_results.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.ScrollView"><Targets><Target tag="layout/activity_test_results_0" view="ScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="429" endOffset="12"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="14" startOffset="8" endLine="22" endOffset="54"/></Target><Target id="@+id/test_name" view="TextView"><Expressions/><location startLine="45" startOffset="20" endLine="53" endOffset="50"/></Target><Target id="@+id/test_type" view="TextView"><Expressions/><location startLine="55" startOffset="20" endLine="63" endOffset="50"/></Target><Target id="@+id/score_value" view="TextView"><Expressions/><location startLine="73" startOffset="24" endLine="80" endOffset="54"/></Target><Target id="@+id/score_label" view="TextView"><Expressions/><location startLine="82" startOffset="24" endLine="88" endOffset="53"/></Target><Target id="@+id/performance_level_text" view="TextView"><Expressions/><location startLine="93" startOffset="20" endLine="102" endOffset="50"/></Target><Target id="@+id/time_taken" view="TextView"><Expressions/><location startLine="159" startOffset="32" endLine="166" endOffset="62"/></Target><Target id="@+id/accuracy_container" view="LinearLayout"><Expressions/><location startLine="171" startOffset="28" endLine="193" endOffset="42"/></Target><Target id="@+id/accuracy_value" view="TextView"><Expressions/><location startLine="184" startOffset="32" endLine="191" endOffset="62"/></Target><Target id="@+id/questions_answered" view="TextView"><Expressions/><location startLine="218" startOffset="32" endLine="225" endOffset="62"/></Target><Target id="@+id/avg_response_time" view="TextView"><Expressions/><location startLine="242" startOffset="32" endLine="249" endOffset="62"/></Target><Target id="@+id/completion_date" view="TextView"><Expressions/><location startLine="266" startOffset="20" endLine="272" endOffset="49"/></Target><Target id="@+id/detailed_scores_container" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="279" startOffset="12" endLine="310" endOffset="63"/></Target><Target id="@+id/detailed_scores_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="303" startOffset="20" endLine="306" endOffset="62"/></Target><Target id="@+id/insights_container" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="313" startOffset="12" endLine="347" endOffset="63"/></Target><Target id="@+id/insights_text" view="TextView"><Expressions/><location startLine="337" startOffset="20" endLine="343" endOffset="56"/></Target><Target id="@+id/recommendations_container" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="350" startOffset="12" endLine="384" endOffset="63"/></Target><Target id="@+id/recommendations_text" view="TextView"><Expressions/><location startLine="374" startOffset="20" endLine="380" endOffset="56"/></Target><Target id="@+id/btn_view_progress" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="393" startOffset="16" endLine="401" endOffset="44"/></Target><Target id="@+id/btn_retake_test" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="403" startOffset="16" endLine="412" endOffset="65"/></Target><Target id="@+id/btn_back_to_tests" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="414" startOffset="16" endLine="421" endOffset="44"/></Target></Targets></Layout>