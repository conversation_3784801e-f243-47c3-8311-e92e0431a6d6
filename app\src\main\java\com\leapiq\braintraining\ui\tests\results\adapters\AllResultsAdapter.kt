package com.leapiq.braintraining.ui.tests.results.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ItemAllResultsBinding
import com.leapiq.braintraining.ui.tests.results.TestResultItem
import com.leapiq.braintraining.data.model.TestType
import java.text.SimpleDateFormat
import java.util.*

/**
 * Adapter for displaying all test results
 */
class AllResultsAdapter(
    private val onItemClick: (com.leapiq.braintraining.data.model.TestResult) -> Unit
) : ListAdapter<TestResultItem, AllResultsAdapter.AllResultsViewHolder>(DiffCallback()) {
    
    private val dateFormat = SimpleDateFormat("MMM dd, yyyy 'at' HH:mm", Locale.getDefault())
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AllResultsViewHolder {
        val binding = ItemAllResultsBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return AllResultsViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: AllResultsViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class AllResultsViewHolder(
        private val binding: ItemAllResultsBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(item: TestResultItem) {
            binding.apply {
                testNameText.text = item.testName
                scoreText.text = "${item.result.score}%"
                dateText.text = dateFormat.format(item.result.completedAt)
                
                // Set test type indicator
                val typeIcon = when (item.result.testType) {
                    TestType.COGNITIVE -> "🧠"
                    TestType.PERSONALITY -> "👤"
                }
                testTypeIcon.text = typeIcon
                
                // Set score color
                val scoreColor = when {
                    item.result.score >= 80 -> R.color.success_green
                    item.result.score >= 60 -> R.color.warning_orange
                    else -> R.color.error_red
                }
                scoreText.setTextColor(ContextCompat.getColor(root.context, scoreColor))
                
                // Set accuracy for cognitive tests
                if (item.result.testType == TestType.COGNITIVE) {
                    accuracyText.text = "Accuracy: ${item.result.accuracyPercentage}%"
                    accuracyText.visibility = android.view.View.VISIBLE
                } else {
                    accuracyText.visibility = android.view.View.GONE
                }
                
                // Set time taken
                val timeSeconds = item.result.totalTimeMs / 1000
                val minutes = timeSeconds / 60
                val seconds = timeSeconds % 60
                
                timeText.text = if (minutes > 0) {
                    "Time: ${minutes}m ${seconds}s"
                } else {
                    "Time: ${seconds}s"
                }
                
                // Set click listener
                root.setOnClickListener {
                    onItemClick(item.result)
                }
            }
        }
    }
    
    private class DiffCallback : DiffUtil.ItemCallback<TestResultItem>() {
        override fun areItemsTheSame(oldItem: TestResultItem, newItem: TestResultItem): Boolean {
            return oldItem.result.completedAt == newItem.result.completedAt && 
                   oldItem.testId == newItem.testId
        }
        
        override fun areContentsTheSame(oldItem: TestResultItem, newItem: TestResultItem): Boolean {
            return oldItem == newItem
        }
    }
}
