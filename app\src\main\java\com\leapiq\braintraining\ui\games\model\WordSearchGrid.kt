package com.leapiq.braintraining.ui.games.model

/**
 * Represents the letter grid for word search game
 */
class WordSearchGrid(val size: Int, private val cols: Int = size) {
    private val grid = Array(size) { CharArray(cols) { ' ' } }
    
    fun getCell(row: Int, col: Int): Char {
        return if (row in 0 until size && col in 0 until cols) {
            grid[row][col]
        } else ' '
    }
    
    fun setCell(row: Int, col: Int, letter: Char) {
        if (row in 0 until size && col in 0 until cols) {
            grid[row][col] = letter
        }
    }
    
    fun getAllCells(): List<List<Char>> {
        return grid.map { it.toList() }
    }
}
