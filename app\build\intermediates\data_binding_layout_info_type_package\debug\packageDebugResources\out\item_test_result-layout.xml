<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_test_result" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\item_test_result.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_test_result_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="137" endOffset="51"/></Target><Target id="@+id/date_text" view="TextView"><Expressions/><location startLine="27" startOffset="12" endLine="34" endOffset="42"/></Target><Target id="@+id/time_text" view="TextView"><Expressions/><location startLine="36" startOffset="12" endLine="42" endOffset="41"/></Target><Target id="@+id/score_icon" view="TextView"><Expressions/><location startLine="61" startOffset="16" endLine="67" endOffset="52"/></Target><Target id="@+id/score_text" view="TextView"><Expressions/><location startLine="69" startOffset="16" endLine="76" endOffset="46"/></Target><Target id="@+id/accuracy_text" view="TextView"><Expressions/><location startLine="81" startOffset="12" endLine="87" endOffset="41"/></Target><Target id="@+id/response_time_text" view="TextView"><Expressions/><location startLine="89" startOffset="12" endLine="95" endOffset="41"/></Target><Target id="@+id/total_time_text" view="TextView"><Expressions/><location startLine="102" startOffset="16" endLine="109" endOffset="45"/></Target><Target id="@+id/questions_text" view="TextView"><Expressions/><location startLine="111" startOffset="16" endLine="117" endOffset="45"/></Target><Target id="@+id/insight_text" view="TextView"><Expressions/><location startLine="122" startOffset="12" endLine="131" endOffset="43"/></Target></Targets></Layout>