<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp"
    android:background="@color/background_white">

    <!-- Notification Icon -->
    <ImageView
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginBottom="32dp"
        android:src="@drawable/ic_notifications"
        android:tint="@color/primary_light_blue"
        android:contentDescription="Notification icon" />

    <!-- Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="Stay Motivated"
        android:textColor="@color/text_primary"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center" />

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:text="Get daily reminders and motivational messages to keep your brain training on track."
        android:textColor="@color/text_secondary"
        android:textSize="16sp"
        android:lineSpacingExtra="4dp"
        android:gravity="center" />

    <!-- Benefits list -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="• Daily challenge reminders"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:drawablePadding="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="• Progress milestone celebrations"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:drawablePadding="8dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="• Motivational tips and insights"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            android:drawablePadding="8dp" />

    </LinearLayout>

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <!-- Not Now button -->
        <Button
            android:id="@+id/btnNotNow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:text="Not Now"
            android:background="@drawable/button_secondary"
            android:textColor="@color/primary_light_blue" />

        <!-- Allow button -->
        <Button
            android:id="@+id/btnAllow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Allow Notifications"
            android:background="@drawable/button_primary"
            android:textColor="@color/text_white" />

    </LinearLayout>

</LinearLayout>
