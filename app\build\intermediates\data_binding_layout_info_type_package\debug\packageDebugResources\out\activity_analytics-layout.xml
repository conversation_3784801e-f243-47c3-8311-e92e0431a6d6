<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_analytics" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_analytics.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_analytics_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="486" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="50"/></Target><Target id="@+id/overall_progress_bar" view="ProgressBar"><Expressions/><location startLine="53" startOffset="20" endLine="60" endOffset="43"/></Target><Target id="@+id/overall_progress_text" view="TextView"><Expressions/><location startLine="62" startOffset="20" endLine="68" endOffset="49"/></Target><Target id="@+id/top_performing_test_text" view="TextView"><Expressions/><location startLine="109" startOffset="28" endLine="116" endOffset="58"/></Target><Target id="@+id/top_performing_score_text" view="TextView"><Expressions/><location startLine="120" startOffset="24" endLine="127" endOffset="54"/></Target><Target id="@+id/cognitive_avg_text" view="TextView"><Expressions/><location startLine="188" startOffset="28" endLine="195" endOffset="58"/></Target><Target id="@+id/personality_avg_text" view="TextView"><Expressions/><location startLine="223" startOffset="28" endLine="230" endOffset="58"/></Target><Target id="@+id/preferred_type_text" view="TextView"><Expressions/><location startLine="236" startOffset="20" endLine="244" endOffset="57"/></Target><Target id="@+id/insight1_title" view="TextView"><Expressions/><location startLine="280" startOffset="24" endLine="288" endOffset="63"/></Target><Target id="@+id/insight1_description" view="TextView"><Expressions/><location startLine="290" startOffset="24" endLine="296" endOffset="53"/></Target><Target id="@+id/insight2_container" view="LinearLayout"><Expressions/><location startLine="301" startOffset="20" endLine="327" endOffset="34"/></Target><Target id="@+id/insight2_title" view="TextView"><Expressions/><location startLine="309" startOffset="24" endLine="317" endOffset="63"/></Target><Target id="@+id/insight2_description" view="TextView"><Expressions/><location startLine="319" startOffset="24" endLine="325" endOffset="53"/></Target><Target id="@+id/insight3_container" view="LinearLayout"><Expressions/><location startLine="330" startOffset="20" endLine="355" endOffset="34"/></Target><Target id="@+id/insight3_title" view="TextView"><Expressions/><location startLine="337" startOffset="24" endLine="345" endOffset="63"/></Target><Target id="@+id/insight3_description" view="TextView"><Expressions/><location startLine="347" startOffset="24" endLine="353" endOffset="53"/></Target><Target id="@+id/strength_area_text" view="TextView"><Expressions/><location startLine="392" startOffset="24" endLine="400" endOffset="63"/></Target><Target id="@+id/strength_score_text" view="TextView"><Expressions/><location startLine="402" startOffset="24" endLine="410" endOffset="63"/></Target><Target id="@+id/strength_consistency_text" view="TextView"><Expressions/><location startLine="412" startOffset="24" endLine="418" endOffset="53"/></Target><Target id="@+id/improvement_area_text" view="TextView"><Expressions/><location startLine="448" startOffset="24" endLine="456" endOffset="63"/></Target><Target id="@+id/improvement_score_text" view="TextView"><Expressions/><location startLine="458" startOffset="24" endLine="466" endOffset="63"/></Target><Target id="@+id/improvement_action_text" view="TextView"><Expressions/><location startLine="468" startOffset="24" endLine="474" endOffset="53"/></Target></Targets></Layout>