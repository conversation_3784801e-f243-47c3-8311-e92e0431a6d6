package com.leapiq.braintraining.ui.progress.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.databinding.ItemCategoryAccuracyBinding
import com.leapiq.braintraining.data.model.CategoryAccuracy

class CategoryAccuracyAdapter : ListAdapter<CategoryAccuracy, CategoryAccuracyAdapter.AccuracyViewHolder>(DiffCallback) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): AccuracyViewHolder {
        val binding = ItemCategoryAccuracyBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return AccuracyViewHolder(binding)
    }

    override fun onBindViewHolder(holder: AccuracyViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class AccuracyViewHolder(
        private val binding: ItemCategoryAccuracyBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(categoryAccuracyData: CategoryAccuracy) {
            binding.apply {
                categoryName.text = categoryAccuracyData.categoryName
                categoryAccuracy.text = "${categoryAccuracyData.accuracy}%"
            }
        }
    }

    companion object {
        private val DiffCallback = object : DiffUtil.ItemCallback<CategoryAccuracy>() {
            override fun areItemsTheSame(oldItem: CategoryAccuracy, newItem: CategoryAccuracy): Boolean {
                return oldItem.categoryName == newItem.categoryName
            }

            override fun areContentsTheSame(oldItem: CategoryAccuracy, newItem: CategoryAccuracy): Boolean {
                return oldItem == newItem
            }
        }
    }
}
