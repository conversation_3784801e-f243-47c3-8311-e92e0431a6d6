<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light_gray">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Toolbar -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/surface_white"
            android:elevation="4dp"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:title="Test Results"
            app:titleTextColor="@color/text_primary" />

        <!-- Main Content -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Test Header -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <TextView
                        android:id="@+id/test_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Memory Assessment"
                        android:textColor="@color/text_primary"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:gravity="center" />

                    <TextView
                        android:id="@+id/test_type"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="Cognitive Test"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        android:gravity="center" />

                    <!-- Main Score -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20dp"
                        android:gravity="center"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/score_value"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="85"
                            android:textColor="@color/primary_light_blue"
                            android:textSize="48sp"
                            android:textStyle="bold" />

                        <TextView
                            android:id="@+id/score_label"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Accuracy"
                            android:textColor="@color/text_secondary"
                            android:textSize="16sp" />

                    </LinearLayout>

                    <!-- Performance Level -->
                    <TextView
                        android:id="@+id/performance_level_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:text="Very Good"
                        android:textColor="@color/primary_light_blue"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:gravity="center" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Performance Metrics -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Performance Metrics"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <!-- Metrics Grid -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="2">

                        <!-- Left Column -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <!-- Time Taken -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="12dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Time Taken"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/time_taken"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="5m 23s"
                                    android:textColor="@color/text_primary"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <!-- Accuracy -->
                            <LinearLayout
                                android:id="@+id/accuracy_container"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Accuracy"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/accuracy_value"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="85%"
                                    android:textColor="@color/text_primary"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                        </LinearLayout>

                        <!-- Right Column -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <!-- Questions Answered -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginBottom="12dp"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Questions"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/questions_answered"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="20"
                                    android:textColor="@color/text_primary"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                            <!-- Average Response Time -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="Avg Response"
                                    android:textColor="@color/text_secondary"
                                    android:textSize="12sp" />

                                <TextView
                                    android:id="@+id/avg_response_time"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:text="2.1s"
                                    android:textColor="@color/text_primary"
                                    android:textSize="16sp"
                                    android:textStyle="bold" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Completion Date -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:text="Completed on"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp" />

                    <TextView
                        android:id="@+id/completion_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Dec 15, 2024 at 14:30"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Detailed Scores (for multi-dimensional tests) -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/detailed_scores_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Detailed Scores"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/detailed_scores_recycler"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Insights -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/insights_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Insights"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/insights_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Recommendations -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/recommendations_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Recommendations"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/recommendations_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textColor="@color/text_primary"
                        android:textSize="14sp"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- Action Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="vertical">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_view_progress"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="View Progress"
                    android:textColor="@color/surface_white"
                    app:backgroundTint="@color/primary_light_blue"
                    app:cornerRadius="8dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_retake_test"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="Retake Test"
                    android:textColor="@color/primary_light_blue"
                    app:cornerRadius="8dp"
                    app:strokeColor="@color/primary_light_blue" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_back_to_tests"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Back to Tests"
                    android:textColor="@color/text_secondary"
                    app:cornerRadius="8dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</ScrollView>
