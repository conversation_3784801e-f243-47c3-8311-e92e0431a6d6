<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.games.VocabularyActivity">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/primary_light_blue"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Quit Button -->
        <ImageButton
            android:id="@+id/btn_quit"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="Quit game"
            app:tint="@color/text_white" />

        <!-- Game Title -->
        <TextView
            android:id="@+id/game_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Vocabulary"
            android:textColor="@color/text_white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Menu Button -->
        <ImageButton
            android:id="@+id/btn_menu"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_menu"
            android:contentDescription="Game menu"
            app:tint="@color/text_white" />

    </LinearLayout>

    <!-- Game Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:elevation="2dp"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Level -->
        <TextView
            android:id="@+id/level_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Level 1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Round -->
        <TextView
            android:id="@+id/round_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Round 1/3"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Trial -->
        <TextView
            android:id="@+id/trial_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Word 1/10"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Mode Display -->
    <TextView
        android:id="@+id/mode_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_light_blue_light"
        android:gravity="center"
        android:padding="12dp"
        android:text="WORD → DEFINITION"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold" />

    <!-- Instructions -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:padding="16dp"
        android:text="Match the word with its definition!"
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />

    <!-- Prompt Display -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@color/surface_white"
        android:padding="16dp">

        <TextView
            android:id="@+id/prompt_display"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/vocabulary_prompt_background"
            android:gravity="center"
            android:padding="20dp"
            android:text="HAPPY"
            android:textColor="@color/text_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            tools:text="feeling joy or pleasure" />

    </ScrollView>

    <!-- Question -->
    <TextView
        android:id="@+id/question_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:padding="12dp"
        android:text="What does this word mean?"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold" />

    <!-- Answer Options -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/surface_white"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- First Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btn_option1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="feeling joy or pleasure"
                android:textSize="14sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp"
                android:elevation="2dp"
                tools:text="feeling joy or pleasure" />

            <Button
                android:id="@+id/btn_option2"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="of great size or extent"
                android:textSize="14sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp"
                android:elevation="2dp"
                tools:text="of great size or extent" />

        </LinearLayout>

        <!-- Second Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_option3"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="moving fast or quickly"
                android:textSize="14sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp"
                android:elevation="2dp"
                tools:text="moving fast or quickly" />

            <Button
                android:id="@+id/btn_option4"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="giving out much light"
                android:textSize="14sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp"
                android:elevation="2dp"
                tools:text="giving out much light" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
