<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_test_question_base" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_test_question_base.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_test_question_base_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="144" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="4" endLine="18" endOffset="50"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="30" startOffset="8" endLine="39" endOffset="31"/></Target><Target id="@+id/progress_text" view="TextView"><Expressions/><location startLine="42" startOffset="8" endLine="49" endOffset="45"/></Target><Target id="@+id/question_text" view="TextView"><Expressions/><location startLine="82" startOffset="20" endLine="90" endOffset="60"/></Target><Target id="@+id/question_content" view="LinearLayout"><Expressions/><location startLine="93" startOffset="20" endLine="97" endOffset="56"/></Target><Target id="@+id/btn_previous" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="117" startOffset="8" endLine="128" endOffset="39"/></Target><Target id="@+id/btn_next" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="131" startOffset="8" endLine="140" endOffset="37"/></Target></Targets></Layout>