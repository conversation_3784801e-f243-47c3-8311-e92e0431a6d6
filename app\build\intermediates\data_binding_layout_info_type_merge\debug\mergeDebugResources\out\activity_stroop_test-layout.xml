<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_stroop_test" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_stroop_test.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_stroop_test_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="218" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/trial_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="103" startOffset="4" endLine="112" endOffset="33"/></Target><Target id="@+id/word_display" view="TextView"><Expressions/><location startLine="122" startOffset="8" endLine="133" endOffset="49"/></Target><Target id="@+id/color_buttons_container" view="LinearLayout"><Expressions/><location startLine="138" startOffset="4" endLine="216" endOffset="18"/></Target><Target id="@+id/btn_red" view="Button"><Expressions/><location startLine="154" startOffset="12" endLine="165" endOffset="41"/></Target><Target id="@+id/btn_blue" view="Button"><Expressions/><location startLine="167" startOffset="12" endLine="178" endOffset="41"/></Target><Target id="@+id/btn_green" view="Button"><Expressions/><location startLine="188" startOffset="12" endLine="199" endOffset="41"/></Target><Target id="@+id/btn_yellow" view="Button"><Expressions/><location startLine="201" startOffset="12" endLine="212" endOffset="41"/></Target></Targets></Layout>