// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentWelcomePageBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView welcomeDescription;

  @NonNull
  public final ImageView welcomeIcon;

  @NonNull
  public final TextView welcomeTitle;

  private FragmentWelcomePageBinding(@NonNull LinearLayout rootView,
      @NonNull TextView welcomeDescription, @NonNull ImageView welcomeIcon,
      @NonNull TextView welcomeTitle) {
    this.rootView = rootView;
    this.welcomeDescription = welcomeDescription;
    this.welcomeIcon = welcomeIcon;
    this.welcomeTitle = welcomeTitle;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentWelcomePageBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentWelcomePageBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_welcome_page, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentWelcomePageBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.welcomeDescription;
      TextView welcomeDescription = ViewBindings.findChildViewById(rootView, id);
      if (welcomeDescription == null) {
        break missingId;
      }

      id = R.id.welcomeIcon;
      ImageView welcomeIcon = ViewBindings.findChildViewById(rootView, id);
      if (welcomeIcon == null) {
        break missingId;
      }

      id = R.id.welcomeTitle;
      TextView welcomeTitle = ViewBindings.findChildViewById(rootView, id);
      if (welcomeTitle == null) {
        break missingId;
      }

      return new FragmentWelcomePageBinding((LinearLayout) rootView, welcomeDescription,
          welcomeIcon, welcomeTitle);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
