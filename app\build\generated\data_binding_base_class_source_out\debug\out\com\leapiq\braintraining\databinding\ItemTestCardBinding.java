// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTestCardBinding implements ViewBinding {
  @NonNull
  private final CardView rootView;

  @NonNull
  public final ImageView completionIcon;

  @NonNull
  public final TextView durationBadge;

  @NonNull
  public final TextView lastScore;

  @NonNull
  public final TextView testCategory;

  @NonNull
  public final ImageView testImage;

  @NonNull
  public final TextView testName;

  private ItemTestCardBinding(@NonNull CardView rootView, @NonNull ImageView completionIcon,
      @NonNull TextView durationBadge, @NonNull TextView lastScore, @NonNull TextView testCategory,
      @NonNull ImageView testImage, @NonNull TextView testName) {
    this.rootView = rootView;
    this.completionIcon = completionIcon;
    this.durationBadge = durationBadge;
    this.lastScore = lastScore;
    this.testCategory = testCategory;
    this.testImage = testImage;
    this.testName = testName;
  }

  @Override
  @NonNull
  public CardView getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTestCardBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTestCardBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_test_card, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTestCardBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.completion_icon;
      ImageView completionIcon = ViewBindings.findChildViewById(rootView, id);
      if (completionIcon == null) {
        break missingId;
      }

      id = R.id.duration_badge;
      TextView durationBadge = ViewBindings.findChildViewById(rootView, id);
      if (durationBadge == null) {
        break missingId;
      }

      id = R.id.last_score;
      TextView lastScore = ViewBindings.findChildViewById(rootView, id);
      if (lastScore == null) {
        break missingId;
      }

      id = R.id.test_category;
      TextView testCategory = ViewBindings.findChildViewById(rootView, id);
      if (testCategory == null) {
        break missingId;
      }

      id = R.id.test_image;
      ImageView testImage = ViewBindings.findChildViewById(rootView, id);
      if (testImage == null) {
        break missingId;
      }

      id = R.id.test_name;
      TextView testName = ViewBindings.findChildViewById(rootView, id);
      if (testName == null) {
        break missingId;
      }

      return new ItemTestCardBinding((CardView) rootView, completionIcon, durationBadge, lastScore,
          testCategory, testImage, testName);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
