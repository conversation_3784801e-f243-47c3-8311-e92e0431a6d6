package com.leapiq.braintraining.ui.games.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.ui.games.model.Category
import com.leapiq.braintraining.ui.games.model.CellState

/**
 * Adapter for the Logic Puzzles grid
 * Displays a grid where players can mark X (impossible) or O (confirmed)
 */
class LogicGridAdapter(
    private val categories: List<Category>,
    private val onCellClicked: (row: Int, col: Int, currentState: CellState) -> Unit
) : RecyclerView.Adapter<LogicGridAdapter.GridRowViewHolder>() {

    private val gridState = mutableMapOf<Pair<Int, Int>, CellState>()
    private val gridSize = categories.size

    class GridRowViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val rowContainer: ViewGroup = itemView.findViewById(R.id.row_container)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): GridRowViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_logic_grid_row, parent, false)
        return GridRowViewHolder(view)
    }

    override fun onBindViewHolder(holder: GridRowViewHolder, position: Int) {
        val context = holder.itemView.context
        holder.rowContainer.removeAllViews()

        // Add row header (category item name)
        if (position < categories.size) {
            val rowHeaderView = LayoutInflater.from(context)
                .inflate(R.layout.item_logic_grid_header, holder.rowContainer, false) as TextView
            
            rowHeaderView.text = categories[position].items.getOrNull(0) ?: ""
            rowHeaderView.setBackgroundColor(ContextCompat.getColor(context, R.color.primary_light_blue_light))
            holder.rowContainer.addView(rowHeaderView)

            // Add cells for this row
            for (col in 0 until gridSize) {
                val cellView = LayoutInflater.from(context)
                    .inflate(R.layout.item_logic_grid_cell, holder.rowContainer, false) as TextView
                
                val cellState = gridState[Pair(position, col)] ?: CellState.EMPTY
                updateCellAppearance(cellView, cellState)
                
                cellView.setOnClickListener {
                    onCellClicked(position, col, cellState)
                }
                
                holder.rowContainer.addView(cellView)
            }
        }
    }

    override fun getItemCount(): Int = gridSize

    fun updateCellState(row: Int, col: Int, state: CellState) {
        gridState[Pair(row, col)] = state
        notifyItemChanged(row)
    }

    fun resetGrid() {
        gridState.clear()
        notifyDataSetChanged()
    }

    private fun updateCellAppearance(cellView: TextView, state: CellState) {
        val context = cellView.context
        
        when (state) {
            CellState.EMPTY -> {
                cellView.text = ""
                cellView.setBackgroundColor(ContextCompat.getColor(context, R.color.surface_white))
                cellView.setTextColor(ContextCompat.getColor(context, R.color.text_primary))
            }
            CellState.MARKED_X -> {
                cellView.text = "✗"
                cellView.setBackgroundColor(ContextCompat.getColor(context, R.color.stroop_red_light))
                cellView.setTextColor(ContextCompat.getColor(context, R.color.stroop_red))
            }
            CellState.MARKED_O -> {
                cellView.text = "✓"
                cellView.setBackgroundColor(ContextCompat.getColor(context, R.color.stroop_green_light))
                cellView.setTextColor(ContextCompat.getColor(context, R.color.stroop_green))
            }
        }
    }
}
