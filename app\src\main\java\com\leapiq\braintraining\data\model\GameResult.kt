package com.leapiq.braintraining.data.model

import java.util.Date

/**
 * Represents the result of a single round within a game level
 */
data class RoundResult(
    val roundNumber: Int,
    val isCorrect: Boolean,
    val timeSpentMs: Long,
    val attempts: Int = 1
)

/**
 * Represents the result of a complete game level (5 rounds)
 */
data class LevelResult(
    val gameId: String,
    val level: Int,
    val rounds: List<RoundResult>,
    val totalTimeMs: Long,
    val accuracy: Double, // 0.0 to 1.0
    val score: Int, // Based on accuracy (0-100)
    val completedAt: Date = Date()
) {
    val correctRounds: Int
        get() = rounds.count { it.isCorrect }
    
    val totalRounds: Int
        get() = rounds.size
    
    val accuracyPercentage: Int
        get() = (accuracy * 100).toInt()
}

/**
 * Represents overall progress for a specific game
 */
data class GameProgress(
    val gameId: String,
    val currentLevel: Int,
    val highestLevel: Int,
    val totalLevelsCompleted: Int,
    val bestAccuracy: Double,
    val averageAccuracy: Double,
    val totalTimePlayed: Long,
    val lastPlayedAt: Date?,
    val levelResults: List<LevelResult> = emptyList()
) {
    val bestAccuracyPercentage: Int
        get() = (bestAccuracy * 100).toInt()
    
    val averageAccuracyPercentage: Int
        get() = (averageAccuracy * 100).toInt()
}

/**
 * Represents overall user progress across all games
 */
data class UserProgress(
    val totalScore: Int,
    val overallAccuracy: Double,
    val currentStreak: Int,
    val maxStreak: Int,
    val userLevel: Int,
    val gamesPlayedThisWeek: Int,
    val categoryAccuracies: Map<GameCategory, Double>,
    val gameProgresses: Map<String, GameProgress>,
    val lastUpdated: Date = Date()
) {
    val overallAccuracyPercentage: Int
        get() = (overallAccuracy * 100).toInt()
}
