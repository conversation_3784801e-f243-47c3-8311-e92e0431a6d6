<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_anagrams" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_anagrams.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_anagrams_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="230" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/trial_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="103" startOffset="4" endLine="112" endOffset="33"/></Target><Target id="@+id/scrambled_word_display" view="TextView"><Expressions/><location startLine="121" startOffset="8" endLine="132" endOffset="34"/></Target><Target id="@+id/user_input_display" view="TextView"><Expressions/><location startLine="143" startOffset="8" endLine="154" endOffset="34"/></Target><Target id="@+id/letters_container" view="LinearLayout"><Expressions/><location startLine="159" startOffset="4" endLine="170" endOffset="18"/></Target><Target id="@+id/btn_clear" view="Button"><Expressions/><location startLine="189" startOffset="12" endLine="199" endOffset="41"/></Target><Target id="@+id/btn_hint" view="Button"><Expressions/><location startLine="201" startOffset="12" endLine="211" endOffset="41"/></Target><Target id="@+id/btn_submit" view="Button"><Expressions/><location startLine="216" startOffset="8" endLine="226" endOffset="37"/></Target></Targets></Layout>