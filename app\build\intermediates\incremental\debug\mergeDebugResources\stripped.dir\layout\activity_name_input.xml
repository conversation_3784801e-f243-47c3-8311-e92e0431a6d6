<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp"
    android:background="@color/background_white">

    <!-- Welcome Icon -->
    <ImageView
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_marginBottom="32dp"
        android:src="@drawable/ic_person"
        android:tint="@color/primary_light_blue"
        android:contentDescription="Person icon" />

    <!-- Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="What's your name?"
        android:textColor="@color/text_primary"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center" />

    <!-- Description -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:text="We'll use this to personalize your experience and track your progress."
        android:textColor="@color/text_secondary"
        android:textSize="16sp"
        android:gravity="center" />

    <!-- Name Input -->
    <com.google.android.material.textfield.TextInputLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="32dp"
        android:hint="Enter your name">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPersonName"
            android:maxLength="30"
            android:singleLine="true" />

    </com.google.android.material.textfield.TextInputLayout>

    <!-- Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <!-- Skip button -->
        <Button
            android:id="@+id/btnSkip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="16dp"
            android:text="Skip"
            android:background="@drawable/button_secondary"
            android:textColor="@color/primary_light_blue" />

        <!-- Continue button -->
        <Button
            android:id="@+id/btnContinue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Continue"
            android:background="@drawable/button_primary"
            android:textColor="@color/text_white" />

    </LinearLayout>

</LinearLayout>
