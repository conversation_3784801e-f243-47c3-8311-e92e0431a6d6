package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityTowerOfHanoiBinding
import com.leapiq.braintraining.ui.games.adapter.HanoiTowerAdapter
import com.leapiq.braintraining.ui.games.model.HanoiTower
import com.leapiq.braintraining.ui.games.model.HanoiDisk
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.math.pow

/**
 * Tower of Hanoi Game
 * Classic recursive puzzle: Move disks between pegs following rules
 * Tests planning and recursive thinking skills
 */
class TowerOfHanoiActivity : AppCompatActivity() {

    private lateinit var binding: ActivityTowerOfHanoiBinding
    private lateinit var adapter: HanoiTowerAdapter
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val roundAccuracies = mutableListOf<Double>()
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "logic_4"

    // Tower of Hanoi specific
    private var towers = mutableListOf<HanoiTower>()
    private var selectedTowerIndex = -1
    private var moveCount = 0
    private var puzzleStartTime = 0L
    private var currentPuzzle = 1
    private var puzzlesPerRound = 2
    private var diskCount = 3
    private var minMoves = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTowerOfHanoiBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.tower_of_hanoi)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Move all disks to the rightmost tower!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup reset button
            btnReset.setOnClickListener {
                resetPuzzle()
            }

            // Setup hint button
            btnHint.setOnClickListener {
                showHint()
            }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentPuzzle = 1
        puzzlesPerRound = getPuzzlesPerRound(currentLevel)
        diskCount = getDiskCount(currentLevel)
        
        binding.puzzleText.text = "Puzzle $currentPuzzle/$puzzlesPerRound"
        
        setupTowers()
        generatePuzzle()
    }

    private fun getPuzzlesPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 2       // 2 puzzles per round
            in 6..10 -> 3      // 3 puzzles per round
            in 11..15 -> 3     // 3 puzzles per round
            in 16..20 -> 4     // 4 puzzles per round
            else -> 4          // 4 puzzles per round
        }
    }

    private fun getDiskCount(level: Int): Int {
        return when (level) {
            in 1..3 -> 3       // 3 disks
            in 4..6 -> 4       // 4 disks
            in 7..10 -> 5      // 5 disks
            in 11..15 -> 6     // 6 disks
            in 16..20 -> 7     // 7 disks
            else -> 8          // 8 disks (maximum)
        }
    }

    private fun setupTowers() {
        adapter = HanoiTowerAdapter(towers) { towerIndex ->
            onTowerClicked(towerIndex)
        }

        binding.towersRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@TowerOfHanoiActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = <EMAIL>
        }
    }

    private fun generatePuzzle() {
        puzzleStartTime = System.currentTimeMillis()
        moveCount = 0
        selectedTowerIndex = -1
        minMoves = (2.0.pow(diskCount.toDouble()) - 1).toInt()
        
        towers.clear()
        
        // Create 3 towers
        towers.add(HanoiTower("A", mutableListOf()))
        towers.add(HanoiTower("B", mutableListOf()))
        towers.add(HanoiTower("C", mutableListOf()))
        
        // Add disks to first tower (largest to smallest from bottom to top)
        for (size in diskCount downTo 1) {
            towers[0].disks.add(HanoiDisk(size))
        }
        
        adapter.notifyDataSetChanged()
        updateUI()
        
        binding.instructionText.text = "Move all disks to tower C. Larger disks cannot go on smaller ones!"
    }

    private fun onTowerClicked(towerIndex: Int) {
        if (selectedTowerIndex == -1) {
            // Select source tower
            if (towers[towerIndex].disks.isNotEmpty()) {
                selectedTowerIndex = towerIndex
                adapter.setSelectedTower(towerIndex)
                binding.instructionText.text = "Now select destination tower"
            } else {
                showMessage("Tower is empty!")
            }
        } else {
            // Try to move disk
            if (towerIndex == selectedTowerIndex) {
                // Deselect
                selectedTowerIndex = -1
                adapter.setSelectedTower(-1)
                binding.instructionText.text = "Move all disks to tower C. Larger disks cannot go on smaller ones!"
            } else {
                attemptMove(selectedTowerIndex, towerIndex)
            }
        }
    }

    private fun attemptMove(fromIndex: Int, toIndex: Int) {
        val fromTower = towers[fromIndex]
        val toTower = towers[toIndex]
        
        if (fromTower.disks.isEmpty()) {
            showMessage("Source tower is empty!")
            return
        }
        
        val diskToMove = fromTower.disks.last()
        
        if (toTower.disks.isNotEmpty() && toTower.disks.last().size < diskToMove.size) {
            showMessage("Cannot place larger disk on smaller disk!")
            return
        }
        
        // Valid move
        fromTower.disks.removeAt(fromTower.disks.size - 1)
        toTower.disks.add(diskToMove)
        moveCount++
        
        selectedTowerIndex = -1
        adapter.setSelectedTower(-1)
        adapter.notifyDataSetChanged()
        
        binding.instructionText.text = "Move all disks to tower C. Larger disks cannot go on smaller ones!"
        updateUI()
        
        // Check if puzzle is solved
        if (isPuzzleSolved()) {
            puzzleComplete()
        }
    }

    private fun isPuzzleSolved(): Boolean {
        // All disks should be on the rightmost tower (tower C)
        return towers[2].disks.size == diskCount && towers[0].disks.isEmpty() && towers[1].disks.isEmpty()
    }

    private fun puzzleComplete() {
        val puzzleTime = System.currentTimeMillis() - puzzleStartTime
        val efficiency = if (moveCount <= minMoves * 1.5) "Excellent" else if (moveCount <= minMoves * 2) "Good" else "Practice more"
        
        totalCorrect++
        totalAttempts++
        
        binding.instructionText.text = """
            Puzzle solved!
            Moves: $moveCount (Min: $minMoves)
            Time: ${puzzleTime / 1000}s
            Efficiency: $efficiency
        """.trimIndent()
        
        currentPuzzle++
        
        if (currentPuzzle > puzzlesPerRound) {
            // Round complete
            Handler(Looper.getMainLooper()).postDelayed({
                roundComplete()
            }, 3000)
        } else {
            // Next puzzle
            binding.puzzleText.text = "Puzzle $currentPuzzle/$puzzlesPerRound"
            
            Handler(Looper.getMainLooper()).postDelayed({
                generatePuzzle()
            }, 3000)
        }
    }

    private fun resetPuzzle() {
        generatePuzzle()
    }

    private fun showHint() {
        val hint = when (moveCount) {
            0 -> "Start by moving the smallest disk to an empty tower."
            1, 2 -> "Remember: only move one disk at a time, and never place a larger disk on a smaller one."
            else -> {
                if (towers[2].disks.isEmpty()) {
                    "Try to get the largest disk to tower C first."
                } else {
                    "Work on moving the remaining disks systematically."
                }
            }
        }
        
        AlertDialog.Builder(this)
            .setTitle("Hint")
            .setMessage(hint)
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun updateUI() {
        binding.apply {
            movesText.text = "Moves: $moveCount"
            minMovesText.text = "Min: $minMoves"
            disksText.text = "Disks: $diskCount"
        }
    }

    private fun showMessage(message: String) {
        binding.instructionText.text = message
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.text = "Move all disks to tower C. Larger disks cannot go on smaller ones!"
        }, 2000)
    }

    private fun roundComplete() {
        val roundAccuracy = if (puzzlesPerRound > 0) {
            (totalCorrect.toDouble() / puzzlesPerRound)
        } else 1.0

        // Store round accuracy for later use
        roundAccuracies.add(roundAccuracy)

        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Puzzles solved: $totalCorrect/$puzzlesPerRound
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentPuzzle = 1
            totalCorrect = 0
            
            Handler(Looper.getMainLooper()).postDelayed({
                generatePuzzle()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0

        // Save level result using actual round data
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            val roundAcc = if (i <= roundAccuracies.size) roundAccuracies[i-1] else accuracy
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = roundAcc > 0.7,
                timeSpentMs = totalTime / maxRounds,
                attempts = puzzlesPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Puzzles Solved: $totalCorrect
                Disk Count: $diskCount
                Total Time: ${totalTime / 1000}s

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Total Puzzles: $totalCorrect
                Max Disks: $diskCount

                Hanoi Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        roundAccuracies.clear()

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        roundAccuracies.clear()
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Move all disks from tower A to tower C

                📋 RULES:
                • Move only one disk at a time
                • Only move the top disk from a tower
                • Never place a larger disk on a smaller disk
                • Use tower B as temporary storage

                💡 STRATEGY:
                • Think recursively: to move n disks, first move n-1 disks to the middle tower
                • Then move the largest disk to the destination
                • Finally move the n-1 disks from middle to destination

                🏆 SCORING:
                • Minimum moves for n disks = 2^n - 1
                • Fewer moves = better efficiency rating
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
