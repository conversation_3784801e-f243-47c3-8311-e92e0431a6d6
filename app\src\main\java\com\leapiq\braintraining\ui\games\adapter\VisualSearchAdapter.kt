package com.leapiq.braintraining.ui.games.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.ui.games.model.SearchItem

/**
 * Adapter for the visual search grid
 * Displays shapes with different colors for the search task
 */
class VisualSearchAdapter(
    private val items: List<SearchItem>,
    private val onItemClicked: (Int) -> Unit
) : RecyclerView.Adapter<VisualSearchAdapter.SearchItemViewHolder>() {

    class SearchItemViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val shapeView: View = itemView.findViewById(R.id.search_shape)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchItemViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_search_shape, parent, false)
        return SearchItemViewHolder(view)
    }

    override fun onBindViewHolder(holder: SearchItemViewHolder, position: Int) {
        val item = items[position]
        
        // Set shape drawable based on shape type
        val shapeDrawable = when (item.shape) {
            "circle" -> R.drawable.search_shape_circle
            "square" -> R.drawable.search_shape_square
            "triangle" -> R.drawable.search_shape_triangle
            "diamond" -> R.drawable.search_shape_diamond
            else -> R.drawable.search_shape_circle
        }
        
        // Set color based on color name
        val colorRes = when (item.color) {
            "red" -> R.color.stroop_red
            "blue" -> R.color.stroop_blue
            "green" -> R.color.stroop_green
            "yellow" -> R.color.stroop_yellow
            "purple" -> R.color.search_purple
            "orange" -> R.color.search_orange
            else -> R.color.stroop_blue
        }
        
        // Apply shape and color
        holder.shapeView.setBackgroundResource(shapeDrawable)
        val color = ContextCompat.getColor(holder.itemView.context, colorRes)
        holder.shapeView.backgroundTintList = android.content.res.ColorStateList.valueOf(color)
        
        // Set click listener
        holder.itemView.setOnClickListener {
            onItemClicked(position)
        }
    }

    override fun getItemCount() = items.size
}
