// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityNotificationPermissionBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnAllow;

  @NonNull
  public final Button btnNotNow;

  private ActivityNotificationPermissionBinding(@NonNull LinearLayout rootView,
      @NonNull Button btnAllow, @NonNull Button btnNotNow) {
    this.rootView = rootView;
    this.btnAllow = btnAllow;
    this.btnNotNow = btnNotNow;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityNotificationPermissionBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityNotificationPermissionBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_notification_permission, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityNotificationPermissionBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btnAllow;
      Button btnAllow = ViewBindings.findChildViewById(rootView, id);
      if (btnAllow == null) {
        break missingId;
      }

      id = R.id.btnNotNow;
      Button btnNotNow = ViewBindings.findChildViewById(rootView, id);
      if (btnNotNow == null) {
        break missingId;
      }

      return new ActivityNotificationPermissionBinding((LinearLayout) rootView, btnAllow,
          btnNotNow);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
