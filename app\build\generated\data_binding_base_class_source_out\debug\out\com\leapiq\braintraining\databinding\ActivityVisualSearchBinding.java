// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityVisualSearchBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final ImageButton btnMenu;

  @NonNull
  public final ImageButton btnQuit;

  @NonNull
  public final TextView gameTitle;

  @NonNull
  public final TextView instructionText;

  @NonNull
  public final TextView levelText;

  @NonNull
  public final TextView roundText;

  @NonNull
  public final RecyclerView searchGrid;

  @NonNull
  public final TextView targetDisplay;

  @NonNull
  public final TextView trialText;

  private ActivityVisualSearchBinding(@NonNull LinearLayout rootView, @NonNull ImageButton btnMenu,
      @NonNull ImageButton btnQuit, @NonNull TextView gameTitle, @NonNull TextView instructionText,
      @NonNull TextView levelText, @NonNull TextView roundText, @NonNull RecyclerView searchGrid,
      @NonNull TextView targetDisplay, @NonNull TextView trialText) {
    this.rootView = rootView;
    this.btnMenu = btnMenu;
    this.btnQuit = btnQuit;
    this.gameTitle = gameTitle;
    this.instructionText = instructionText;
    this.levelText = levelText;
    this.roundText = roundText;
    this.searchGrid = searchGrid;
    this.targetDisplay = targetDisplay;
    this.trialText = trialText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityVisualSearchBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityVisualSearchBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_visual_search, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityVisualSearchBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_menu;
      ImageButton btnMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnMenu == null) {
        break missingId;
      }

      id = R.id.btn_quit;
      ImageButton btnQuit = ViewBindings.findChildViewById(rootView, id);
      if (btnQuit == null) {
        break missingId;
      }

      id = R.id.game_title;
      TextView gameTitle = ViewBindings.findChildViewById(rootView, id);
      if (gameTitle == null) {
        break missingId;
      }

      id = R.id.instruction_text;
      TextView instructionText = ViewBindings.findChildViewById(rootView, id);
      if (instructionText == null) {
        break missingId;
      }

      id = R.id.level_text;
      TextView levelText = ViewBindings.findChildViewById(rootView, id);
      if (levelText == null) {
        break missingId;
      }

      id = R.id.round_text;
      TextView roundText = ViewBindings.findChildViewById(rootView, id);
      if (roundText == null) {
        break missingId;
      }

      id = R.id.search_grid;
      RecyclerView searchGrid = ViewBindings.findChildViewById(rootView, id);
      if (searchGrid == null) {
        break missingId;
      }

      id = R.id.target_display;
      TextView targetDisplay = ViewBindings.findChildViewById(rootView, id);
      if (targetDisplay == null) {
        break missingId;
      }

      id = R.id.trial_text;
      TextView trialText = ViewBindings.findChildViewById(rootView, id);
      if (trialText == null) {
        break missingId;
      }

      return new ActivityVisualSearchBinding((LinearLayout) rootView, btnMenu, btnQuit, gameTitle,
          instructionText, levelText, roundText, searchGrid, targetDisplay, trialText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
