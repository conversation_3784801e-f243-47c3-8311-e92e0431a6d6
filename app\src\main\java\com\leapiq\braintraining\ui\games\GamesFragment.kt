package com.leapiq.braintraining.ui.games

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.FragmentGamesBinding
import com.leapiq.braintraining.ui.games.adapter.GameAdapter
import com.leapiq.braintraining.data.model.Game
import com.leapiq.braintraining.data.model.GameCategory
import com.leapiq.braintraining.data.model.GameType
import com.leapiq.braintraining.data.GameProgressManager

class GamesFragment : Fragment() {

    private var _binding: FragmentGamesBinding? = null
    private val binding get() = _binding!!

    private lateinit var gameAdapter: GameAdapter
    private lateinit var progressManager: GameProgressManager
    private var allGames: List<Game> = emptyList()
    private var currentCategory = GameCategory.ALL

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentGamesBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        progressManager = GameProgressManager.getInstance(requireContext())

        setupHeader()
        setupGamesGrid()
        setupCategoryTabs()
        loadGames()
    }
    
    private fun setupHeader() {
        // Update header title to "Games"
        binding.root.findViewById<android.widget.TextView>(R.id.header_title)?.text = 
            getString(R.string.nav_games)
    }

    private fun setupGamesGrid() {
        gameAdapter = GameAdapter { game ->
            onGameClicked(game)
        }
        
        binding.gamesRecycler.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = gameAdapter
        }
    }
    
    private fun setupCategoryTabs() {
        binding.apply {
            tabAll.setOnClickListener {
                selectCategory(GameCategory.ALL)
                scrollToTab(tabAll)
            }
            tabMemory.setOnClickListener {
                selectCategory(GameCategory.MEMORY)
                scrollToTab(tabMemory)
            }
            tabAttention.setOnClickListener {
                selectCategory(GameCategory.ATTENTION)
                scrollToTab(tabAttention)
            }
            tabMath.setOnClickListener {
                selectCategory(GameCategory.MATH)
                scrollToTab(tabMath)
            }
            tabLogic.setOnClickListener {
                selectCategory(GameCategory.LOGIC)
                scrollToTab(tabLogic)
            }
            tabLanguage.setOnClickListener {
                selectCategory(GameCategory.LANGUAGE)
                scrollToTab(tabLanguage)
            }
        }

        // Select "All" by default
        selectCategory(GameCategory.ALL)
    }
    
    private fun selectCategory(category: GameCategory) {
        currentCategory = category
        updateTabSelection()
        filterGames()
    }
    
    private fun updateTabSelection() {
        val selectedTextColor = resources.getColor(R.color.text_white, null)
        val unselectedTextColor = resources.getColor(R.color.text_secondary, null)
        val selectedBgColor = resources.getColor(R.color.primary_light_blue, null)
        val unselectedBgColor = resources.getColor(R.color.surface_white, null)

        binding.apply {
            // Update All tab
            updateTabStyle(tabAll, currentCategory == GameCategory.ALL, selectedTextColor, unselectedTextColor, selectedBgColor, unselectedBgColor)
            // Update Memory tab
            updateTabStyle(tabMemory, currentCategory == GameCategory.MEMORY, selectedTextColor, unselectedTextColor, selectedBgColor, unselectedBgColor)
            // Update Attention tab
            updateTabStyle(tabAttention, currentCategory == GameCategory.ATTENTION, selectedTextColor, unselectedTextColor, selectedBgColor, unselectedBgColor)
            // Update Math tab
            updateTabStyle(tabMath, currentCategory == GameCategory.MATH, selectedTextColor, unselectedTextColor, selectedBgColor, unselectedBgColor)
            // Update Logic tab
            updateTabStyle(tabLogic, currentCategory == GameCategory.LOGIC, selectedTextColor, unselectedTextColor, selectedBgColor, unselectedBgColor)
            // Update Language tab
            updateTabStyle(tabLanguage, currentCategory == GameCategory.LANGUAGE, selectedTextColor, unselectedTextColor, selectedBgColor, unselectedBgColor)
        }
    }

    private fun updateTabStyle(
        tab: com.google.android.material.button.MaterialButton,
        isSelected: Boolean,
        selectedTextColor: Int,
        unselectedTextColor: Int,
        selectedBgColor: Int,
        unselectedBgColor: Int
    ) {
        tab.setTextColor(if (isSelected) selectedTextColor else unselectedTextColor)
        tab.backgroundTintList = android.content.res.ColorStateList.valueOf(
            if (isSelected) selectedBgColor else unselectedBgColor
        )
        tab.elevation = if (isSelected) 4f else 1f
    }

    private fun scrollToTab(tab: com.google.android.material.button.MaterialButton) {
        binding.categoryScrollView.post {
            val scrollX = tab.left - (binding.categoryScrollView.width / 2) + (tab.width / 2)
            binding.categoryScrollView.smoothScrollTo(scrollX, 0)
        }
    }
    
    private fun filterGames() {
        val filteredGames = if (currentCategory == GameCategory.ALL) {
            allGames
        } else {
            allGames.filter { it.category == currentCategory }
        }
        gameAdapter.submitList(filteredGames)
    }
    
    private fun loadGames() {
        allGames = createGamesList()
        filterGames()
    }
    
    private fun createGamesList(): List<Game> {
        return listOf(
            // Memory Games - Load real progress data
            createGameWithProgress("memory_1", getString(R.string.card_matching), GameCategory.MEMORY, "Match pairs of cards", "ic_memory"),
            createGameWithProgress("memory_2", getString(R.string.sequence_recall), GameCategory.MEMORY, "Remember sequences", "ic_memory"),
            createGameWithProgress("memory_3", getString(R.string.pattern_memory), GameCategory.MEMORY, "Remember patterns", "ic_memory"),
            createGameWithProgress("memory_4", getString(R.string.number_memory), GameCategory.MEMORY, "Remember numbers", "ic_memory"),
            
            // Attention Games
            Game("attention_1", getString(R.string.stroop_test), GameCategory.ATTENTION, "Color-word test", "ic_attention", completedLevels = 4),
            Game("attention_2", getString(R.string.reaction_time), GameCategory.ATTENTION, "Test reaction speed", "ic_attention", completedLevels = 7),
            Game("attention_3", getString(R.string.visual_search), GameCategory.ATTENTION, "Find targets", "ic_attention", completedLevels = 1),
            Game("attention_4", getString(R.string.focus_challenge), GameCategory.ATTENTION, "Focus training", "ic_attention", completedLevels = 0),
            
            // Math Games
            Game("math_1", getString(R.string.mental_arithmetic), GameCategory.MATH, "Quick calculations", "ic_math", completedLevels = 5),
            Game("math_2", getString(R.string.number_sequences), GameCategory.MATH, "Find patterns", "ic_math", completedLevels = 2),
            Game("math_3", getString(R.string.speed_math), GameCategory.MATH, "Fast math", "ic_math", completedLevels = 8),
            Game("math_4", getString(R.string.estimation), GameCategory.MATH, "Estimate quantities", "ic_math", completedLevels = 0),
            
            // Logic Games
            Game("logic_1", getString(R.string.pattern_completion), GameCategory.LOGIC, "Complete patterns", "ic_logic", completedLevels = 3),
            Game("logic_2", getString(R.string.logical_reasoning), GameCategory.LOGIC, "Deductive reasoning", "ic_logic", completedLevels = 1),
            Game("logic_3", getString(R.string.spatial_rotation), GameCategory.LOGIC, "Rotate 3D objects", "ic_logic", completedLevels = 0),
            Game("logic_4", getString(R.string.tower_of_hanoi), GameCategory.LOGIC, "Move disks puzzle", "ic_logic", completedLevels = 4),
            Game("logic_5", getString(R.string.tube_sort), GameCategory.LOGIC, "Sort colored tubes", "ic_logic", completedLevels = 2),
            Game("logic_6", getString(R.string.logic_puzzles), GameCategory.LOGIC, "Grid constraint puzzles", "ic_logic", completedLevels = 0),

            // Language Games
            Game("language_1", getString(R.string.word_association), GameCategory.LANGUAGE, "Associate words", "ic_language", completedLevels = 6),
            Game("language_2", getString(R.string.anagrams), GameCategory.LANGUAGE, "Rearrange letters", "ic_language", completedLevels = 0),
            Game("language_3", getString(R.string.vocabulary), GameCategory.LANGUAGE, "Word definitions", "ic_language", completedLevels = 3),
            Game("language_4", getString(R.string.word_search), GameCategory.LANGUAGE, "Find words", "ic_language", completedLevels = 5)
        )
    }
    
    private fun onGameClicked(game: Game) {
        if (!game.isUnlocked) {
            // Show unlock dialog or navigate to premium
            return
        }

        // Navigate to specific game
        val intent = when (game.id) {
            "memory_1" -> Intent(requireContext(), CardMatchingActivity::class.java)
            "memory_2" -> Intent(requireContext(), SequenceRecallActivity::class.java)
            "memory_3" -> Intent(requireContext(), PatternMemoryActivity::class.java)
            "memory_4" -> Intent(requireContext(), NumberMemoryActivity::class.java)
            "attention_1" -> Intent(requireContext(), StroopTestActivity::class.java)
            "attention_2" -> Intent(requireContext(), ReactionTimeActivity::class.java)
            "attention_3" -> Intent(requireContext(), VisualSearchActivity::class.java)
            "attention_4" -> Intent(requireContext(), FocusChallengeActivity::class.java)
            "math_1" -> Intent(requireContext(), MentalArithmeticActivity::class.java)
            "math_2" -> Intent(requireContext(), NumberSequencesActivity::class.java)
            "math_3" -> Intent(requireContext(), SpeedMathActivity::class.java)
            "math_4" -> Intent(requireContext(), EstimationActivity::class.java)
            "logic_1" -> Intent(requireContext(), PatternCompletionActivity::class.java)
            "logic_2" -> Intent(requireContext(), LogicalReasoningActivity::class.java)
            "logic_3" -> Intent(requireContext(), SpatialRotationActivity::class.java)
            "logic_4" -> Intent(requireContext(), TowerOfHanoiActivity::class.java)
            "logic_5" -> Intent(requireContext(), TubeSortActivity::class.java)
            "logic_6" -> Intent(requireContext(), LogicPuzzlesActivity::class.java)
            "language_1" -> Intent(requireContext(), WordAssociationActivity::class.java)
            "language_2" -> Intent(requireContext(), AnagramsActivity::class.java)
            "language_3" -> Intent(requireContext(), VocabularyActivity::class.java)
            "language_4" -> Intent(requireContext(), WordSearchActivity::class.java)
            // Add other games here as they are implemented
            else -> {
                // Show "Coming Soon" message for unimplemented games
                return
            }
        }

        // Simple approach - no complex extras needed

        startActivity(intent)
    }

    private fun createGameWithProgress(gameId: String, name: String, category: GameCategory, description: String, imageResource: String): Game {
        val gameProgress = progressManager.getGameProgress(gameId)
        return Game(
            id = gameId,
            name = name,
            category = category,
            description = description,
            imageResource = imageResource,
            totalLevels = 10, // Default total levels
            completedLevels = gameProgress.totalLevelsCompleted,
            gameType = GameType.FREE,
            isUnlocked = true
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
