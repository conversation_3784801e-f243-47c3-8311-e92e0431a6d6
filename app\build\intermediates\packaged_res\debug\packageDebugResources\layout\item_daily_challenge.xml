<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Game Icon/Image -->
        <ImageView
            android:id="@+id/challenge_icon"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_gravity="center"
            android:layout_marginBottom="8dp"
            android:background="@drawable/game_card_background"
            android:padding="8dp"
            android:src="@drawable/ic_games"
            android:tint="@color/primary_light_blue" />

        <!-- Game Name -->
        <TextView
            android:id="@+id/challenge_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="Card Matching"
            android:textColor="@color/text_primary"
            android:textSize="14sp"
            android:textStyle="bold" />

        <!-- Category -->
        <TextView
            android:id="@+id/challenge_category"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:gravity="center"
            android:text="Memory"
            android:textColor="@color/text_secondary"
            android:textSize="12sp" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
