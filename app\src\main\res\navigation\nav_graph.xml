<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/navigation_today">

    <fragment
        android:id="@+id/navigation_today"
        android:name="com.leapiq.braintraining.ui.today.TodayFragment"
        android:label="@string/nav_today"
        tools:layout="@layout/fragment_today">

        <action
            android:id="@+id/action_today_to_games"
            app:destination="@id/navigation_games"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
        <action
            android:id="@+id/action_today_to_tests"
            app:destination="@id/navigation_tests"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
        <action
            android:id="@+id/action_today_to_progress"
            app:destination="@id/navigation_progress"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
    </fragment>

    <fragment
        android:id="@+id/navigation_games"
        android:name="com.leapiq.braintraining.ui.games.GamesFragment"
        android:label="@string/nav_games"
        tools:layout="@layout/fragment_games">

        <action
            android:id="@+id/action_games_to_today"
            app:destination="@id/navigation_today"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
        <action
            android:id="@+id/action_games_to_tests"
            app:destination="@id/navigation_tests"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
        <action
            android:id="@+id/action_games_to_progress"
            app:destination="@id/navigation_progress"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
    </fragment>

    <fragment
        android:id="@+id/navigation_tests"
        android:name="com.leapiq.braintraining.ui.tests.TestsFragment"
        android:label="@string/nav_tests"
        tools:layout="@layout/fragment_tests">

        <action
            android:id="@+id/action_tests_to_today"
            app:destination="@id/navigation_today"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
        <action
            android:id="@+id/action_tests_to_games"
            app:destination="@id/navigation_games"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
        <action
            android:id="@+id/action_tests_to_progress"
            app:destination="@id/navigation_progress"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
    </fragment>

    <fragment
        android:id="@+id/navigation_progress"
        android:name="com.leapiq.braintraining.ui.progress.ProgressFragment"
        android:label="@string/nav_progress"
        tools:layout="@layout/fragment_progress">

        <action
            android:id="@+id/action_progress_to_today"
            app:destination="@id/navigation_today"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
        <action
            android:id="@+id/action_progress_to_games"
            app:destination="@id/navigation_games"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
        <action
            android:id="@+id/action_progress_to_tests"
            app:destination="@id/navigation_tests"
            app:enterAnim="@anim/fade_in"
            app:exitAnim="@anim/fade_out" />
    </fragment>

</navigation>
