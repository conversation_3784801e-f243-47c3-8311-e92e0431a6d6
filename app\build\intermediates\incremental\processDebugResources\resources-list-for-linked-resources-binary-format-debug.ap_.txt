C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_fade_in.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\anim_fade_out.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_anagram_input_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_anagram_scrambled_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_button_danger.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_button_primary.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_button_primary_rounded.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_button_secondary.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_card_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_circle_primary.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_dialog_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_estimation_answer_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_estimation_display_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_focus_stimulus_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_game_card_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_gradient_learning_style.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_gradient_problem_solving.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_gradient_stress_response.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hanoi_disk_blue.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hanoi_disk_cyan.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hanoi_disk_green.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hanoi_disk_orange.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hanoi_disk_pink.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hanoi_disk_purple.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hanoi_disk_red.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hanoi_disk_yellow.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hanoi_tower_base.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hanoi_tower_normal_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hanoi_tower_peg.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_hanoi_tower_selected_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_header_icon_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_analytics.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_apple.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_arrow_back.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_car.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_card_back.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_check.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_circle.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_close.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_diamond.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_favorite.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_games.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_heart.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_home.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_learning.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_lock.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_menu.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_music.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_person.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_pets.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_problem_solving.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_progress.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_school.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_settings.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_sports.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_square.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_star.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_streak.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_stress.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_test_placeholder.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_tests.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_timer.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_today.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_triangle.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_ic_trophy.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_logic_premises_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_logic_question_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_logical_reasoning.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_math_answer_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_math_problem_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_memory_grid_default.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_memory_grid_highlighted.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_memory_grid_pattern.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_memory_grid_selected.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_number_display_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_number_input_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_pattern_cell_empty.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_pattern_cell_filled.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_pattern_display_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_pattern_question_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_premium_button_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_reaction_stimulus_circle.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_rounded_background_light.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_search_shape_circle.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_search_shape_diamond.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_search_shape_square.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_search_shape_triangle.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_sequence_answer_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_sequence_button_blue.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_sequence_button_green.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_sequence_button_red.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_sequence_button_yellow.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_sequence_display_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_spatial_rotation.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_spatial_shape_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_speed_math_answer_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_speed_math_problem_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_stroop_word_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tower_of_hanoi.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tube_ball_blue.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tube_ball_cyan.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tube_ball_green.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tube_ball_orange.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tube_ball_pink.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tube_ball_purple.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tube_ball_red.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tube_ball_yellow.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tube_empty_slot.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tube_normal_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_tube_selected_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_vocabulary_prompt_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_word_search_cell_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_word_search_grid_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_word_search_list_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\drawable_word_target_background.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_all_results.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_anagrams.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_analytics.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_card_matching.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_estimation.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_focus_challenge.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_game_result.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_learning_style.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_logic_puzzles.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_logical_reasoning.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_main.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_mental_arithmetic.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_my_results.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_number_memory.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_number_sequences.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_pattern_completion.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_pattern_memory.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_problem_solving_style.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_reaction_time.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_sequence_recall.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_spatial_rotation.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_speed_math.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_stress_response.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_stroop_test.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_test_info.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_test_progress.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_test_question_base.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_test_results.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_tower_of_hanoi.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_tube_sort.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_visual_search.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_vocabulary.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_word_association.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_activity_word_search.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_dialog_game_menu.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_games.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_profile.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_progress.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_tests.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_fragment_today.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_achievement.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_all_results.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_category_accuracy.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_daily_challenge.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_detailed_score.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_game_card.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_hanoi_tower.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_key_point.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_learning_style_option.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_logic_clue.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_logic_grid_cell.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_logic_grid_header.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_logic_grid_row.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_memory_card.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_memory_grid.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_pattern_cell.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_problem_solving_option.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_recent_activity.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_search_shape.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_stress_response_option.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_test_card.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_test_instruction.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_test_progress.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_test_result.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_tube.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_word_search_cell.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_item_word_search_word.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\layout_layout_header.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\menu_bottom_nav_menu.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\navigation_nav_graph.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-af_values-af.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-am_values-am.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ar_values-ar.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-as_values-as.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-az_values-az.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-b+es+419_values-b+es+419.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-b+sr+Latn_values-b+sr+Latn.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-be_values-be.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-bg_values-bg.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-bn_values-bn.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-bs_values-bs.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ca_values-ca.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-cs_values-cs.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-da_values-da.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-de_values-de.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-el_values-el.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rAU_values-en-rAU.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rCA_values-en-rCA.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rGB_values-en-rGB.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rIN_values-en-rIN.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-en-rXC_values-en-rXC.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-es-rUS_values-es-rUS.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-es_values-es.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-et_values-et.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-eu_values-eu.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fa_values-fa.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fi_values-fi.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fr-rCA_values-fr-rCA.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-fr_values-fr.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-gl_values-gl.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-gu_values-gu.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h320dp-port-v13_values-h320dp-port-v13.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h360dp-land-v13_values-h360dp-land-v13.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h480dp-land-v13_values-h480dp-land-v13.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h550dp-port-v13_values-h550dp-port-v13.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-h720dp-v13_values-h720dp-v13.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hdpi-v4_values-hdpi-v4.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hi_values-hi.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hr_values-hr.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hu_values-hu.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-hy_values-hy.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-in_values-in.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-is_values-is.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-it_values-it.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-iw_values-iw.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ja_values-ja.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ka_values-ka.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-kk_values-kk.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-km_values-km.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-kn_values-kn.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ko_values-ko.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ky_values-ky.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-land_values-land.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-large-v4_values-large-v4.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ldltr-v21_values-ldltr-v21.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ldrtl-v17_values-ldrtl-v17.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-lo_values-lo.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-lt_values-lt.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-lv_values-lv.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-mk_values-mk.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ml_values-ml.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-mn_values-mn.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-mr_values-mr.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ms_values-ms.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-my_values-my.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-nb_values-nb.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ne_values-ne.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-night-v8_values-night-v8.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-nl_values-nl.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-or_values-or.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pa_values-pa.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pl_values-pl.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-port_values-port.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pt-rBR_values-pt-rBR.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pt-rPT_values-pt-rPT.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-pt_values-pt.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ro_values-ro.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ru_values-ru.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-si_values-si.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sk_values-sk.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sl_values-sl.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-small-v4_values-small-v4.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sq_values-sq.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sr_values-sr.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sv_values-sv.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sw600dp-v13_values-sw600dp-v13.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-sw_values-sw.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ta_values-ta.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-te_values-te.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-th_values-th.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-tl_values-tl.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-tr_values-tr.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-uk_values-uk.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-ur_values-ur.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-uz_values-uz.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v16_values-v16.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v17_values-v17.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v18_values-v18.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v21_values-v21.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v22_values-v22.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v23_values-v23.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v24_values-v24.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v25_values-v25.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v26_values-v26.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v28_values-v28.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v31_values-v31.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-v34_values-v34.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-vi_values-vi.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w320dp-land-v13_values-w320dp-land-v13.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w360dp-port-v13_values-w360dp-port-v13.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w400dp-port-v13_values-w400dp-port-v13.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-w600dp-land-v13_values-w600dp-land-v13.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-watch-v20_values-watch-v20.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-watch-v21_values-watch-v21.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-xlarge-v4_values-xlarge-v4.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zh-rCN_values-zh-rCN.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zh-rHK_values-zh-rHK.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zh-rTW_values-zh-rTW.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values-zu_values-zu.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\values_values.arsc.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\xml_backup_rules.xml.flat C:\Projects\LeapIQ\app\build\intermediates\merged_res\debug\mergeDebugResources\xml_data_extraction_rules.xml.flat 