<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    style="@style/LeapIQ.Header"
    android:orientation="horizontal"
    android:gravity="center_vertical"
    android:paddingStart="16dp"
    android:paddingEnd="16dp">

    <!-- Page Title (Left side) -->
    <TextView
        android:id="@+id/header_title"
        style="@style/LeapIQ.HeaderText"
        android:layout_weight="1"
        android:text="@string/welcome_user" />

    <!-- Right side icons container -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- Premium Button -->
        <TextView
            android:id="@+id/header_premium"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/premium_button_background"
            android:gravity="center"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:text="@string/premium"
            android:textColor="@color/text_primary"
            android:textSize="12sp"
            android:textStyle="bold"
            android:clickable="true"
            android:focusable="true" />

        <!-- Streak Icon -->
        <LinearLayout
            android:id="@+id/header_streak"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp"
            android:orientation="vertical"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true"
            android:padding="4dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_streak"
                android:tint="@color/text_white" />

            <TextView
                android:id="@+id/streak_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="7"
                android:textColor="@color/text_white"
                android:textSize="10sp"
                android:textStyle="bold" />

        </LinearLayout>

        <!-- Settings Gear Icon -->
        <ImageView
            android:id="@+id/header_settings"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:background="@drawable/header_icon_background"
            android:padding="6dp"
            android:src="@drawable/ic_settings"
            android:tint="@color/primary_light_blue_dark"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

</LinearLayout>
