<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="dialog_game_menu" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\dialog_game_menu.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/dialog_game_menu_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="57" endOffset="14"/></Target><Target id="@+id/btn_continue" view="Button"><Expressions/><location startLine="18" startOffset="4" endLine="26" endOffset="32"/></Target><Target id="@+id/btn_restart" view="Button"><Expressions/><location startLine="28" startOffset="4" endLine="36" endOffset="32"/></Target><Target id="@+id/btn_how_to_play" view="Button"><Expressions/><location startLine="38" startOffset="4" endLine="46" endOffset="32"/></Target><Target id="@+id/btn_quit_menu" view="Button"><Expressions/><location startLine="48" startOffset="4" endLine="55" endOffset="32"/></Target></Targets></Layout>