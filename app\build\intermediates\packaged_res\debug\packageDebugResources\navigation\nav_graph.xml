<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/navigation_today">

    <fragment
        android:id="@+id/navigation_today"
        android:name="com.leapiq.braintraining.ui.today.TodayFragment"
        android:label="@string/nav_today"
        tools:layout="@layout/fragment_today" />

    <fragment
        android:id="@+id/navigation_games"
        android:name="com.leapiq.braintraining.ui.games.GamesFragment"
        android:label="@string/nav_games"
        tools:layout="@layout/fragment_games" />

    <fragment
        android:id="@+id/navigation_tests"
        android:name="com.leapiq.braintraining.ui.tests.TestsFragment"
        android:label="@string/nav_tests"
        tools:layout="@layout/fragment_tests" />

    <fragment
        android:id="@+id/navigation_progress"
        android:name="com.leapiq.braintraining.ui.progress.ProgressFragment"
        android:label="@string/nav_progress"
        tools:layout="@layout/fragment_progress" />

</navigation>
