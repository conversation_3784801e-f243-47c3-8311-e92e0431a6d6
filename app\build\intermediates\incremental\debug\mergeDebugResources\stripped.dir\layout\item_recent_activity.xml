<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="120dp"
    android:layout_height="wrap_content"
    android:layout_marginEnd="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp"
        android:gravity="center">

        <!-- Activity Icon -->
        <TextView
            android:id="@+id/activity_icon"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="✅"
            android:textSize="24sp"
            android:layout_marginBottom="8dp" />

        <!-- Test Name -->
        <TextView
            android:id="@+id/test_name_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Memory Test"
            android:textColor="@color/text_primary"
            android:textSize="12sp"
            android:textStyle="bold"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end"
            android:layout_marginBottom="4dp" />

        <!-- Score -->
        <TextView
            android:id="@+id/score_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="85%"
            android:textColor="@color/success_green"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="4dp" />

        <!-- Date -->
        <TextView
            android:id="@+id/date_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Dec 15"
            android:textColor="@color/text_secondary"
            android:textSize="10sp" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
