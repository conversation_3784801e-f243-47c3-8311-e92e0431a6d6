package com.leapiq.braintraining.ui.games.model

/**
 * Represents a tower (peg) in the Tower of Hanoi game
 */
data class HanoiTower(
    val name: String, // "A", "B", "C"
    val disks: MutableList<HanoiDisk>
) {
    fun isEmpty(): Boolean = disks.isEmpty()
    fun getTopDisk(): HanoiDisk? = disks.lastOrNull()
    fun canAcceptDisk(disk: HanoiDisk): Boolean {
        return isEmpty() || getTopDisk()!!.size > disk.size
    }
    fun getHeight(): Int = disks.size
}
