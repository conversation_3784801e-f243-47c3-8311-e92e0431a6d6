package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityTubeSortBinding
import com.leapiq.braintraining.ui.games.adapter.TubeAdapter
import com.leapiq.braintraining.ui.games.model.Tube
import com.leapiq.braintraining.ui.games.model.Ball
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Tube Sort Game (Ball Sort/Color Sort)
 * Players sort colored balls into separate tubes
 * Tests logical planning and problem-solving skills
 */
class TubeSortActivity : AppCompatActivity() {

    private lateinit var binding: ActivityTubeSortBinding
    private lateinit var adapter: TubeAdapter
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private var currentDifficulty = 1
    private val roundAccuracies = mutableListOf<Double>()
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "logic_1"

    // Tube sort specific
    private var tubes = mutableListOf<Tube>()
    private var selectedTubeIndex = -1
    private var moveCount = 0
    private var puzzleStartTime = 0L
    private var currentPuzzle = 1
    private var puzzlesPerRound = 3

    // Available colors
    private val ballColors = listOf(
        Ball.Color.RED, Ball.Color.BLUE, Ball.Color.GREEN, Ball.Color.YELLOW,
        Ball.Color.PURPLE, Ball.Color.ORANGE, Ball.Color.PINK, Ball.Color.CYAN
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTubeSortBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.tube_sort)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Sort the balls so each tube has only one color!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup reset button
            btnReset.setOnClickListener {
                resetPuzzle()
            }

            // Setup undo button
            btnUndo.setOnClickListener {
                undoLastMove()
            }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentPuzzle = 1
        puzzlesPerRound = getPuzzlesPerRound(currentLevel)
        
        binding.puzzleText.text = "Puzzle $currentPuzzle/$puzzlesPerRound"
        
        setupGrid()
        generatePuzzle()
    }

    private fun getPuzzlesPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 3       // 3 puzzles per round
            in 6..10 -> 4      // 4 puzzles per round
            in 11..15 -> 5     // 5 puzzles per round
            in 16..20 -> 6     // 6 puzzles per round
            else -> 7          // 7 puzzles per round
        }
    }

    private fun setupGrid() {
        adapter = TubeAdapter(tubes) { tubeIndex ->
            onTubeClicked(tubeIndex)
        }

        val tubeCount = getTubeCount(currentLevel)
        val columns = when (tubeCount) {
            in 1..4 -> 2
            in 5..6 -> 3
            in 7..9 -> 3
            else -> 4
        }

        binding.tubesGrid.apply {
            layoutManager = GridLayoutManager(this@TubeSortActivity, columns)
            adapter = <EMAIL>
        }
    }

    private fun generatePuzzle() {
        puzzleStartTime = System.currentTimeMillis()
        moveCount = 0
        selectedTubeIndex = -1
        
        currentDifficulty = getDifficulty(currentLevel)
        val colorCount = getColorCount(currentLevel)
        val tubeCount = getTubeCount(currentLevel)
        val ballsPerColor = getBallsPerColor(currentLevel)
        
        tubes.clear()
        
        // Create tubes
        repeat(tubeCount) {
            tubes.add(Tube(mutableListOf()))
        }
        
        // Generate balls
        val allBalls = mutableListOf<Ball>()
        for (i in 0 until colorCount) {
            val color = ballColors[i]
            repeat(ballsPerColor) {
                allBalls.add(Ball(color))
            }
        }
        
        // Shuffle and distribute balls
        allBalls.shuffle()
        
        // Fill tubes (leave some empty for sorting)
        val fillableTubes = tubeCount - 2 // Leave 2 empty tubes for sorting
        var ballIndex = 0
        
        for (tubeIndex in 0 until fillableTubes) {
            val ballsInTube = minOf(4, allBalls.size - ballIndex) // Max 4 balls per tube
            for (i in 0 until ballsInTube) {
                if (ballIndex < allBalls.size) {
                    tubes[tubeIndex].balls.add(allBalls[ballIndex])
                    ballIndex++
                }
            }
        }
        
        // Ensure puzzle is solvable by checking if we have enough empty space
        ensureSolvable()
        
        adapter.notifyDataSetChanged()
        updateUI()
    }

    private fun getDifficulty(level: Int): Int {
        return when (level) {
            in 1..5 -> 1       // Easy
            in 6..10 -> 2      // Medium
            in 11..15 -> 3     // Hard
            in 16..20 -> 4     // Very Hard
            else -> 5          // Expert
        }
    }

    private fun getColorCount(level: Int): Int {
        return when (level) {
            in 1..3 -> 3       // 3 colors
            in 4..6 -> 4       // 4 colors
            in 7..10 -> 5      // 5 colors
            in 11..15 -> 6     // 6 colors
            in 16..20 -> 7     // 7 colors
            else -> 8          // 8 colors
        }
    }

    private fun getTubeCount(level: Int): Int {
        return when (level) {
            in 1..3 -> 5       // 5 tubes (3 colors + 2 empty)
            in 4..6 -> 6       // 6 tubes (4 colors + 2 empty)
            in 7..10 -> 7      // 7 tubes (5 colors + 2 empty)
            in 11..15 -> 8     // 8 tubes (6 colors + 2 empty)
            in 16..20 -> 9     // 9 tubes (7 colors + 2 empty)
            else -> 10         // 10 tubes (8 colors + 2 empty)
        }
    }

    private fun getBallsPerColor(level: Int): Int {
        return when (level) {
            in 1..5 -> 3       // 3 balls per color
            in 6..15 -> 4      // 4 balls per color
            else -> 4          // 4 balls per color (max)
        }
    }

    private fun ensureSolvable() {
        // Basic solvability check - ensure we have enough empty tubes
        val emptyTubes = tubes.count { it.balls.isEmpty() }
        if (emptyTubes < 2) {
            // Make sure we have at least 2 empty tubes
            while (tubes.count { it.balls.isEmpty() } < 2) {
                val nonEmptyTube = tubes.find { it.balls.isNotEmpty() }
                nonEmptyTube?.balls?.clear()
            }
        }
    }

    private fun onTubeClicked(tubeIndex: Int) {
        if (selectedTubeIndex == -1) {
            // Select source tube
            if (tubes[tubeIndex].balls.isNotEmpty()) {
                selectedTubeIndex = tubeIndex
                adapter.setSelectedTube(tubeIndex)
                binding.instructionText.text = "Now select destination tube"
            }
        } else {
            // Try to move ball
            if (tubeIndex == selectedTubeIndex) {
                // Deselect
                selectedTubeIndex = -1
                adapter.setSelectedTube(-1)
                binding.instructionText.text = "Sort the balls so each tube has only one color!"
            } else {
                attemptMove(selectedTubeIndex, tubeIndex)
            }
        }
    }

    private fun attemptMove(fromIndex: Int, toIndex: Int) {
        val fromTube = tubes[fromIndex]
        val toTube = tubes[toIndex]
        
        if (fromTube.balls.isEmpty()) {
            showMessage("Source tube is empty!")
            return
        }
        
        if (toTube.balls.size >= 4) {
            showMessage("Destination tube is full!")
            return
        }
        
        val ballToMove = fromTube.balls.last()
        
        if (toTube.balls.isNotEmpty() && toTube.balls.last().color != ballToMove.color) {
            showMessage("Can only place same colors together!")
            return
        }
        
        // Valid move
        fromTube.balls.removeAt(fromTube.balls.size - 1)
        toTube.balls.add(ballToMove)
        moveCount++
        
        selectedTubeIndex = -1
        adapter.setSelectedTube(-1)
        adapter.notifyDataSetChanged()
        
        binding.instructionText.text = "Sort the balls so each tube has only one color!"
        updateUI()
        
        // Check if puzzle is solved
        if (isPuzzleSolved()) {
            puzzleComplete()
        }
    }

    private fun isPuzzleSolved(): Boolean {
        for (tube in tubes) {
            if (tube.balls.isEmpty()) continue
            
            val firstColor = tube.balls.first().color
            if (!tube.balls.all { it.color == firstColor }) {
                return false
            }
        }
        return true
    }

    private fun puzzleComplete() {
        val puzzleTime = System.currentTimeMillis() - puzzleStartTime
        totalCorrect++
        totalAttempts++
        
        binding.instructionText.text = "Puzzle solved in $moveCount moves! (${puzzleTime / 1000}s)"
        
        currentPuzzle++
        
        if (currentPuzzle > puzzlesPerRound) {
            // Round complete
            Handler(Looper.getMainLooper()).postDelayed({
                roundComplete()
            }, 2000)
        } else {
            // Next puzzle
            binding.puzzleText.text = "Puzzle $currentPuzzle/$puzzlesPerRound"
            
            Handler(Looper.getMainLooper()).postDelayed({
                generatePuzzle()
            }, 2000)
        }
    }

    private fun resetPuzzle() {
        generatePuzzle()
    }

    private fun undoLastMove() {
        // Simple undo - just reset the puzzle for now
        // In a full implementation, you'd maintain a move history
        showMessage("Undo not implemented - use Reset instead")
    }

    private fun updateUI() {
        binding.movesText.text = "Moves: $moveCount"
    }

    private fun showMessage(message: String) {
        binding.instructionText.text = message
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.text = "Sort the balls so each tube has only one color!"
        }, 2000)
    }

    private fun roundComplete() {
        val roundAccuracy = if (puzzlesPerRound > 0) {
            (totalCorrect.toDouble() / puzzlesPerRound)
        } else 1.0

        // Store round accuracy and difficulty for scoring
        roundAccuracies.add(roundAccuracy)

        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Puzzles solved: $totalCorrect/$puzzlesPerRound
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentPuzzle = 1
            totalCorrect = 0
            
            Handler(Looper.getMainLooper()).postDelayed({
                generatePuzzle()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7,
                timeSpentMs = totalTime / maxRounds,
                attempts = puzzlesPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Puzzles Solved: $totalCorrect
                Total Time: ${totalTime / 1000}s

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Total Puzzles: $totalCorrect
                Logic Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Sort colored balls so each tube contains only one color

                📋 RULES:
                • Tap a tube to select it, then tap destination tube
                • Can only move the top ball from a tube
                • Can only place balls on same color or empty tube
                • Each tube holds maximum 4 balls

                💡 TIPS:
                • Plan your moves carefully
                • Use empty tubes as temporary storage
                • Work backwards from the goal
                • Sometimes you need to move balls out to access others

                🏆 SCORING:
                • Solve puzzles to advance levels
                • Fewer moves = better performance
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
