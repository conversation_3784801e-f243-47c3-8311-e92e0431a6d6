<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/surface_white"
        android:elevation="4dp"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="All Results"
        app:titleTextColor="@color/text_primary" />

    <!-- Empty State -->
    <LinearLayout
        android:id="@+id/empty_state_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="32dp"
        android:gravity="center"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="📋"
            android:textSize="64sp"
            android:layout_marginBottom="16dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="No Results Yet"
            android:textColor="@color/text_primary"
            android:textSize="24sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Complete some tests to see your detailed results here"
            android:textColor="@color/text_secondary"
            android:textSize="16sp"
            android:gravity="center" />

    </LinearLayout>

    <!-- Results List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/results_recycler"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="8dp"
        android:clipToPadding="false" />

</LinearLayout>
