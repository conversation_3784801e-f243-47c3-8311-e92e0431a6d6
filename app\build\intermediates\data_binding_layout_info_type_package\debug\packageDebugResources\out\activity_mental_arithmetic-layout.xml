<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_mental_arithmetic" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_mental_arithmetic.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_mental_arithmetic_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="338" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/trial_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="103" startOffset="4" endLine="112" endOffset="33"/></Target><Target id="@+id/problem_display" view="TextView"><Expressions/><location startLine="122" startOffset="8" endLine="132" endOffset="38"/></Target><Target id="@+id/answer_input" view="TextView"><Expressions/><location startLine="153" startOffset="8" endLine="163" endOffset="38"/></Target><Target id="@+id/btn_1" view="Button"><Expressions/><location startLine="184" startOffset="12" endLine="193" endOffset="40"/></Target><Target id="@+id/btn_2" view="Button"><Expressions/><location startLine="195" startOffset="12" endLine="204" endOffset="40"/></Target><Target id="@+id/btn_3" view="Button"><Expressions/><location startLine="206" startOffset="12" endLine="215" endOffset="40"/></Target><Target id="@+id/btn_4" view="Button"><Expressions/><location startLine="218" startOffset="12" endLine="227" endOffset="40"/></Target><Target id="@+id/btn_5" view="Button"><Expressions/><location startLine="229" startOffset="12" endLine="238" endOffset="40"/></Target><Target id="@+id/btn_6" view="Button"><Expressions/><location startLine="240" startOffset="12" endLine="249" endOffset="40"/></Target><Target id="@+id/btn_7" view="Button"><Expressions/><location startLine="252" startOffset="12" endLine="261" endOffset="40"/></Target><Target id="@+id/btn_8" view="Button"><Expressions/><location startLine="263" startOffset="12" endLine="272" endOffset="40"/></Target><Target id="@+id/btn_9" view="Button"><Expressions/><location startLine="274" startOffset="12" endLine="283" endOffset="40"/></Target><Target id="@+id/btn_negative" view="Button"><Expressions/><location startLine="286" startOffset="12" endLine="295" endOffset="40"/></Target><Target id="@+id/btn_0" view="Button"><Expressions/><location startLine="297" startOffset="12" endLine="306" endOffset="40"/></Target><Target id="@+id/btn_clear" view="Button"><Expressions/><location startLine="308" startOffset="12" endLine="317" endOffset="40"/></Target><Target id="@+id/btn_submit" view="Button"><Expressions/><location startLine="320" startOffset="12" endLine="332" endOffset="40"/></Target></Targets></Layout>