package com.leapiq.braintraining.ui.today

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import com.leapiq.braintraining.databinding.FragmentTodayBinding
import com.leapiq.braintraining.ui.today.adapter.DailyChallengeAdapter
import com.leapiq.braintraining.ui.games.*
import com.leapiq.braintraining.data.model.DailyChallenge

class TodayFragment : Fragment() {

    private var _binding: FragmentTodayBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var dailyChallengeAdapter: DailyChallengeAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentTodayBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        setupDailyChallenges()
        setupClickListeners()
    }

    private fun setupDailyChallenges() {
        dailyChallengeAdapter = DailyChallengeAdapter { challenge ->
            // Handle challenge click
            onChallengeClicked(challenge)
        }
        
        binding.dailyChallengesRecycler.apply {
            layoutManager = GridLayoutManager(context, 2)
            adapter = dailyChallengeAdapter
        }
        
        // Load today's challenges (5 games from different categories)
        loadDailyChallenges()
    }
    
    private fun setupClickListeners() {
        binding.startChallengeButton.setOnClickListener {
            // Start the daily challenge sequence
            startDailyChallenge()
        }
    }
    
    private fun loadDailyChallenges() {
        // Generate 5 random games from different categories
        val challenges = listOf(
            DailyChallenge("Card Matching", "Memory", "ic_memory"),
            DailyChallenge("Stroop Test", "Attention", "ic_attention"),
            DailyChallenge("Mental Arithmetic", "Math", "ic_math"),
            DailyChallenge("Pattern Completion", "Logic", "ic_logic"),
            DailyChallenge("Word Association", "Language", "ic_language")
        )
        
        dailyChallengeAdapter.submitList(challenges)
    }
    
    private fun onChallengeClicked(challenge: DailyChallenge) {
        // Navigate to specific game based on challenge name
        val intent = when (challenge.name) {
            "Card Matching" -> Intent(requireContext(), CardMatchingActivity::class.java)
            "Mental Arithmetic" -> Intent(requireContext(), SpeedMathActivity::class.java)
            "Stroop Test" -> Intent(requireContext(), StroopTestActivity::class.java)
            "Word Association" -> Intent(requireContext(), VocabularyActivity::class.java)
            "Pattern Completion" -> Intent(requireContext(), WordSearchActivity::class.java)
            else -> {
                Toast.makeText(requireContext(), "Game not yet implemented", Toast.LENGTH_SHORT).show()
                return
            }
        }
        startActivity(intent)
    }
    
    private fun startDailyChallenge() {
        // Start the complete daily challenge sequence
        // TODO: Implement daily challenge flow
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
