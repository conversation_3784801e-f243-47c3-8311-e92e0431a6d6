package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityNumberSequencesBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Number Sequences Game
 * Players identify patterns in number sequences and complete missing elements
 * Tests pattern recognition and mathematical reasoning
 */
class NumberSequencesActivity : AppCompatActivity() {

    private lateinit var binding: ActivityNumberSequencesBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "math_2"

    // Number sequences specific
    private var currentSequence = mutableListOf<Int>()
    private var missingIndex = 0
    private var correctAnswer = 0
    private var problemStartTime = 0L
    private var currentTrial = 0
    private var trialsPerRound = 8
    private var reactionTimes = mutableListOf<Long>()

    // Pattern types
    private enum class PatternType {
        ARITHMETIC,     // +n each step (e.g., 2, 4, 6, 8)
        GEOMETRIC,      // ×n each step (e.g., 2, 4, 8, 16)
        FIBONACCI,      // sum of previous two (e.g., 1, 1, 2, 3, 5)
        SQUARES,        // perfect squares (e.g., 1, 4, 9, 16)
        PRIMES,         // prime numbers (e.g., 2, 3, 5, 7)
        ALTERNATING,    // two patterns alternating
        POLYNOMIAL      // quadratic/cubic patterns
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityNumberSequencesBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.number_sequences)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Find the pattern and complete the sequence!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup number pad
            setupNumberPad()

            // Setup control buttons
            btnClear.setOnClickListener {
                clearInput()
            }

            btnSubmit.setOnClickListener {
                submitAnswer()
            }

            btnNegative.setOnClickListener {
                toggleNegative()
            }
        }
    }

    private fun setupNumberPad() {
        binding.apply {
            btn0.setOnClickListener { addDigit("0") }
            btn1.setOnClickListener { addDigit("1") }
            btn2.setOnClickListener { addDigit("2") }
            btn3.setOnClickListener { addDigit("3") }
            btn4.setOnClickListener { addDigit("4") }
            btn5.setOnClickListener { addDigit("5") }
            btn6.setOnClickListener { addDigit("6") }
            btn7.setOnClickListener { addDigit("7") }
            btn8.setOnClickListener { addDigit("8") }
            btn9.setOnClickListener { addDigit("9") }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentTrial = 0
        reactionTimes.clear()
        trialsPerRound = getTrialsPerRound(currentLevel)
        
        binding.trialText.text = "Sequence 1/$trialsPerRound"
        
        generateNextSequence()
    }

    private fun getTrialsPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 8       // 8 sequences per round
            in 6..10 -> 10     // 10 sequences per round
            in 11..15 -> 12    // 12 sequences per round
            in 16..20 -> 15    // 15 sequences per round
            else -> 18         // 18 sequences per round
        }
    }

    private fun generateNextSequence() {
        if (currentTrial >= trialsPerRound) {
            roundComplete()
            return
        }

        currentTrial++
        binding.trialText.text = "Sequence $currentTrial/$trialsPerRound"
        
        val availablePatterns = getAvailablePatterns(currentLevel)
        val patternType = availablePatterns.random()
        val sequenceLength = getSequenceLength(currentLevel)
        
        generateSequence(patternType, sequenceLength)
        displaySequence()
        clearInput()
        problemStartTime = System.currentTimeMillis()
    }

    private fun getAvailablePatterns(level: Int): List<PatternType> {
        return when (level) {
            in 1..3 -> listOf(PatternType.ARITHMETIC)
            in 4..6 -> listOf(PatternType.ARITHMETIC, PatternType.GEOMETRIC)
            in 7..10 -> listOf(PatternType.ARITHMETIC, PatternType.GEOMETRIC, PatternType.SQUARES)
            in 11..15 -> listOf(PatternType.ARITHMETIC, PatternType.GEOMETRIC, PatternType.SQUARES, PatternType.FIBONACCI)
            in 16..20 -> listOf(PatternType.ARITHMETIC, PatternType.GEOMETRIC, PatternType.SQUARES, PatternType.FIBONACCI, PatternType.PRIMES)
            else -> PatternType.values().toList()
        }
    }

    private fun getSequenceLength(level: Int): Int {
        return when (level) {
            in 1..5 -> Random.nextInt(4, 6)    // 4-5 elements
            in 6..10 -> Random.nextInt(5, 7)   // 5-6 elements
            in 11..15 -> Random.nextInt(6, 8)  // 6-7 elements
            in 16..20 -> Random.nextInt(7, 9)  // 7-8 elements
            else -> Random.nextInt(8, 11)      // 8-10 elements
        }
    }

    private fun generateSequence(patternType: PatternType, length: Int) {
        currentSequence.clear()
        
        when (patternType) {
            PatternType.ARITHMETIC -> generateArithmeticSequence(length)
            PatternType.GEOMETRIC -> generateGeometricSequence(length)
            PatternType.FIBONACCI -> generateFibonacciSequence(length)
            PatternType.SQUARES -> generateSquaresSequence(length)
            PatternType.PRIMES -> generatePrimesSequence(length)
            PatternType.ALTERNATING -> generateAlternatingSequence(length)
            PatternType.POLYNOMIAL -> generatePolynomialSequence(length)
        }
        
        // Choose random position to hide (not first or last for easier recognition)
        missingIndex = if (length > 3) Random.nextInt(1, length - 1) else Random.nextInt(0, length)
        correctAnswer = currentSequence[missingIndex]
    }

    private fun generateArithmeticSequence(length: Int) {
        val start = Random.nextInt(1, 20)
        val diff = Random.nextInt(1, 15)
        
        for (i in 0 until length) {
            currentSequence.add(start + i * diff)
        }
    }

    private fun generateGeometricSequence(length: Int) {
        val start = Random.nextInt(1, 10)
        val ratio = Random.nextInt(2, 4) // Keep ratios small to avoid huge numbers
        
        var current = start
        for (i in 0 until length) {
            currentSequence.add(current)
            current *= ratio
        }
    }

    private fun generateFibonacciSequence(length: Int) {
        val start1 = Random.nextInt(1, 5)
        val start2 = Random.nextInt(1, 5)
        
        currentSequence.add(start1)
        if (length > 1) currentSequence.add(start2)
        
        for (i in 2 until length) {
            currentSequence.add(currentSequence[i-1] + currentSequence[i-2])
        }
    }

    private fun generateSquaresSequence(length: Int) {
        val start = Random.nextInt(1, 5)
        
        for (i in 0 until length) {
            val n = start + i
            currentSequence.add(n * n)
        }
    }

    private fun generatePrimesSequence(length: Int) {
        val primes = listOf(2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47)
        val startIndex = Random.nextInt(0, primes.size - length)
        
        for (i in 0 until length) {
            currentSequence.add(primes[startIndex + i])
        }
    }

    private fun generateAlternatingSequence(length: Int) {
        val base1 = Random.nextInt(1, 10)
        val base2 = Random.nextInt(1, 10)
        val diff1 = Random.nextInt(2, 8)
        val diff2 = Random.nextInt(2, 8)
        
        for (i in 0 until length) {
            if (i % 2 == 0) {
                currentSequence.add(base1 + (i / 2) * diff1)
            } else {
                currentSequence.add(base2 + (i / 2) * diff2)
            }
        }
    }

    private fun generatePolynomialSequence(length: Int) {
        // Simple quadratic: an² + bn + c
        val a = Random.nextInt(1, 4)
        val b = Random.nextInt(1, 6)
        val c = Random.nextInt(1, 10)
        
        for (i in 0 until length) {
            val n = i + 1
            currentSequence.add(a * n * n + b * n + c)
        }
    }

    private fun displaySequence() {
        val sequenceText = currentSequence.mapIndexed { index, value ->
            if (index == missingIndex) "?" else value.toString()
        }.joinToString(", ")
        
        binding.sequenceDisplay.text = sequenceText
        binding.instructionText.text = "What number should replace the '?'?"
    }

    private fun addDigit(digit: String) {
        val currentInput = binding.answerInput.text.toString()
        if (currentInput.length < 6) { // Limit input length
            binding.answerInput.text = currentInput + digit
        }
    }

    private fun clearInput() {
        binding.answerInput.text = ""
    }

    private fun toggleNegative() {
        val currentInput = binding.answerInput.text.toString()
        if (currentInput.isNotEmpty()) {
            if (currentInput.startsWith("-")) {
                binding.answerInput.text = currentInput.substring(1)
            } else {
                binding.answerInput.text = "-$currentInput"
            }
        }
    }

    private fun submitAnswer() {
        val userInput = binding.answerInput.text.toString()
        if (userInput.isEmpty()) return
        
        val userAnswer = userInput.toIntOrNull() ?: return
        val reactionTime = System.currentTimeMillis() - problemStartTime
        
        totalAttempts++
        val isCorrect = userAnswer == correctAnswer
        
        if (isCorrect) {
            totalCorrect++
            reactionTimes.add(reactionTime)
            showFeedback("Correct! (${reactionTime}ms)", true)
        } else {
            showFeedback("Wrong! Answer was $correctAnswer", false)
        }
        
        // Show complete sequence briefly
        val completeSequence = currentSequence.joinToString(", ")
        binding.sequenceDisplay.text = completeSequence
        
        // Continue to next sequence after delay
        Handler(Looper.getMainLooper()).postDelayed({
            generateNextSequence()
        }, 2000)
    }

    private fun showFeedback(message: String, isCorrect: Boolean) {
        binding.instructionText.text = message
        binding.instructionText.setTextColor(
            resources.getColor(
                if (isCorrect) R.color.stroop_green else R.color.stroop_red,
                null
            )
        )
        
        // Reset color after delay
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.setTextColor(
                resources.getColor(R.color.text_secondary, null)
            )
        }, 2000)
    }

    private fun roundComplete() {
        val avgReactionTime = if (reactionTimes.isNotEmpty()) {
            reactionTimes.average().toLong()
        } else 0L
        
        val roundAccuracy = if (trialsPerRound > 0) {
            (totalCorrect.toDouble() / trialsPerRound)
        } else 1.0
        
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Accuracy: ${(roundAccuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentTrial = 0
            reactionTimes.clear()
            
            Handler(Looper.getMainLooper()).postDelayed({
                generateNextSequence()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0
        val avgReactionTime = if (reactionTimes.isNotEmpty()) reactionTimes.average().toLong() else 0L

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7, // 70% threshold
                timeSpentMs = totalTime / maxRounds,
                attempts = trialsPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = if (avgReactionTime > 0) (accuracy * 1000 / avgReactionTime * 100).toInt() else (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
                Patterns Solved: $totalAttempts

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Best Avg Time: ${avgReactionTime}ms

                Pattern Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Find patterns in number sequences

                📋 RULES:
                • Look at the sequence of numbers
                • Identify the mathematical pattern
                • Enter the missing number (marked with ?)
                • Complete sequences to advance levels

                💡 PATTERN TYPES:
                • Arithmetic: +n each step (2, 4, 6, 8...)
                • Geometric: ×n each step (2, 4, 8, 16...)
                • Squares: 1², 2², 3²... (1, 4, 9, 16...)
                • Fibonacci: sum of previous two (1, 1, 2, 3, 5...)
                • And more complex patterns!

                🏆 SCORING:
                • Accuracy = correct patterns / total sequences
                • Speed = average recognition time
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
