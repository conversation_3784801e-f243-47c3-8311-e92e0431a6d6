<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_test_progress" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\item_test_progress.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="com.google.android.material.card.MaterialCardView"><Targets><Target tag="layout/item_test_progress_0" view="com.google.android.material.card.MaterialCardView"><Expressions/><location startLine="1" startOffset="0" endLine="130" endOffset="51"/></Target><Target id="@+id/test_name_text" view="TextView"><Expressions/><location startLine="26" startOffset="12" endLine="34" endOffset="51"/></Target><Target id="@+id/times_completed_text" view="TextView"><Expressions/><location startLine="36" startOffset="12" endLine="43" endOffset="51"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="46" startOffset="12" endLine="53" endOffset="35"/></Target><Target id="@+id/trend_indicator" view="TextView"><Expressions/><location startLine="66" startOffset="12" endLine="73" endOffset="42"/></Target><Target id="@+id/best_score_text" view="TextView"><Expressions/><location startLine="90" startOffset="16" endLine="97" endOffset="46"/></Target><Target id="@+id/average_score_text" view="TextView"><Expressions/><location startLine="115" startOffset="16" endLine="122" endOffset="46"/></Target></Targets></Layout>