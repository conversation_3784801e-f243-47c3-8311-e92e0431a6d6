<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.games.SequenceRecallActivity">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/primary_light_blue"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Quit Button -->
        <ImageButton
            android:id="@+id/btn_quit"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="Quit game"
            app:tint="@color/text_white" />

        <!-- Game Title -->
        <TextView
            android:id="@+id/game_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Sequence Recall"
            android:textColor="@color/text_white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Menu Button -->
        <ImageButton
            android:id="@+id/btn_menu"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_menu"
            android:contentDescription="Game menu"
            app:tint="@color/text_white" />

    </LinearLayout>

    <!-- Game Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:elevation="2dp"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Level -->
        <TextView
            android:id="@+id/level_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Level 1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Round -->
        <TextView
            android:id="@+id/round_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Round 1/3"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Instructions -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:padding="16dp"
        android:text="Watch the sequence, then repeat it!"
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />

    <!-- Game Area -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="32dp">

        <!-- Color Buttons Grid -->
        <GridLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:columnCount="2"
            android:rowCount="2"
            android:columnOrderPreserved="false"
            android:rowOrderPreserved="false">

            <!-- Red Button -->
            <Button
                android:id="@+id/btn_red"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_margin="8dp"
                android:background="@drawable/sequence_button_red"
                android:elevation="4dp"
                android:alpha="0.7"
                android:layout_row="0"
                android:layout_column="0" />

            <!-- Blue Button -->
            <Button
                android:id="@+id/btn_blue"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_margin="8dp"
                android:background="@drawable/sequence_button_blue"
                android:elevation="4dp"
                android:alpha="0.7"
                android:layout_row="0"
                android:layout_column="1" />

            <!-- Green Button -->
            <Button
                android:id="@+id/btn_green"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_margin="8dp"
                android:background="@drawable/sequence_button_green"
                android:elevation="4dp"
                android:alpha="0.7"
                android:layout_row="1"
                android:layout_column="0" />

            <!-- Yellow Button -->
            <Button
                android:id="@+id/btn_yellow"
                android:layout_width="120dp"
                android:layout_height="120dp"
                android:layout_margin="8dp"
                android:background="@drawable/sequence_button_yellow"
                android:elevation="4dp"
                android:alpha="0.7"
                android:layout_row="1"
                android:layout_column="1" />

        </GridLayout>

    </FrameLayout>

</LinearLayout>
