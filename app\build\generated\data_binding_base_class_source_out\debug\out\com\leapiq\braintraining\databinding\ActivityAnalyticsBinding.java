// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAnalyticsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView cognitiveAvgText;

  @NonNull
  public final TextView improvementActionText;

  @NonNull
  public final TextView improvementAreaText;

  @NonNull
  public final TextView improvementScoreText;

  @NonNull
  public final TextView insight1Description;

  @NonNull
  public final TextView insight1Title;

  @NonNull
  public final LinearLayout insight2Container;

  @NonNull
  public final TextView insight2Description;

  @NonNull
  public final TextView insight2Title;

  @NonNull
  public final LinearLayout insight3Container;

  @NonNull
  public final TextView insight3Description;

  @NonNull
  public final TextView insight3Title;

  @NonNull
  public final ProgressBar overallProgressBar;

  @NonNull
  public final TextView overallProgressText;

  @NonNull
  public final TextView personalityAvgText;

  @NonNull
  public final TextView preferredTypeText;

  @NonNull
  public final TextView strengthAreaText;

  @NonNull
  public final TextView strengthConsistencyText;

  @NonNull
  public final TextView strengthScoreText;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView topPerformingScoreText;

  @NonNull
  public final TextView topPerformingTestText;

  private ActivityAnalyticsBinding(@NonNull LinearLayout rootView,
      @NonNull TextView cognitiveAvgText, @NonNull TextView improvementActionText,
      @NonNull TextView improvementAreaText, @NonNull TextView improvementScoreText,
      @NonNull TextView insight1Description, @NonNull TextView insight1Title,
      @NonNull LinearLayout insight2Container, @NonNull TextView insight2Description,
      @NonNull TextView insight2Title, @NonNull LinearLayout insight3Container,
      @NonNull TextView insight3Description, @NonNull TextView insight3Title,
      @NonNull ProgressBar overallProgressBar, @NonNull TextView overallProgressText,
      @NonNull TextView personalityAvgText, @NonNull TextView preferredTypeText,
      @NonNull TextView strengthAreaText, @NonNull TextView strengthConsistencyText,
      @NonNull TextView strengthScoreText, @NonNull Toolbar toolbar,
      @NonNull TextView topPerformingScoreText, @NonNull TextView topPerformingTestText) {
    this.rootView = rootView;
    this.cognitiveAvgText = cognitiveAvgText;
    this.improvementActionText = improvementActionText;
    this.improvementAreaText = improvementAreaText;
    this.improvementScoreText = improvementScoreText;
    this.insight1Description = insight1Description;
    this.insight1Title = insight1Title;
    this.insight2Container = insight2Container;
    this.insight2Description = insight2Description;
    this.insight2Title = insight2Title;
    this.insight3Container = insight3Container;
    this.insight3Description = insight3Description;
    this.insight3Title = insight3Title;
    this.overallProgressBar = overallProgressBar;
    this.overallProgressText = overallProgressText;
    this.personalityAvgText = personalityAvgText;
    this.preferredTypeText = preferredTypeText;
    this.strengthAreaText = strengthAreaText;
    this.strengthConsistencyText = strengthConsistencyText;
    this.strengthScoreText = strengthScoreText;
    this.toolbar = toolbar;
    this.topPerformingScoreText = topPerformingScoreText;
    this.topPerformingTestText = topPerformingTestText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAnalyticsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAnalyticsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_analytics, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAnalyticsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cognitive_avg_text;
      TextView cognitiveAvgText = ViewBindings.findChildViewById(rootView, id);
      if (cognitiveAvgText == null) {
        break missingId;
      }

      id = R.id.improvement_action_text;
      TextView improvementActionText = ViewBindings.findChildViewById(rootView, id);
      if (improvementActionText == null) {
        break missingId;
      }

      id = R.id.improvement_area_text;
      TextView improvementAreaText = ViewBindings.findChildViewById(rootView, id);
      if (improvementAreaText == null) {
        break missingId;
      }

      id = R.id.improvement_score_text;
      TextView improvementScoreText = ViewBindings.findChildViewById(rootView, id);
      if (improvementScoreText == null) {
        break missingId;
      }

      id = R.id.insight1_description;
      TextView insight1Description = ViewBindings.findChildViewById(rootView, id);
      if (insight1Description == null) {
        break missingId;
      }

      id = R.id.insight1_title;
      TextView insight1Title = ViewBindings.findChildViewById(rootView, id);
      if (insight1Title == null) {
        break missingId;
      }

      id = R.id.insight2_container;
      LinearLayout insight2Container = ViewBindings.findChildViewById(rootView, id);
      if (insight2Container == null) {
        break missingId;
      }

      id = R.id.insight2_description;
      TextView insight2Description = ViewBindings.findChildViewById(rootView, id);
      if (insight2Description == null) {
        break missingId;
      }

      id = R.id.insight2_title;
      TextView insight2Title = ViewBindings.findChildViewById(rootView, id);
      if (insight2Title == null) {
        break missingId;
      }

      id = R.id.insight3_container;
      LinearLayout insight3Container = ViewBindings.findChildViewById(rootView, id);
      if (insight3Container == null) {
        break missingId;
      }

      id = R.id.insight3_description;
      TextView insight3Description = ViewBindings.findChildViewById(rootView, id);
      if (insight3Description == null) {
        break missingId;
      }

      id = R.id.insight3_title;
      TextView insight3Title = ViewBindings.findChildViewById(rootView, id);
      if (insight3Title == null) {
        break missingId;
      }

      id = R.id.overall_progress_bar;
      ProgressBar overallProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (overallProgressBar == null) {
        break missingId;
      }

      id = R.id.overall_progress_text;
      TextView overallProgressText = ViewBindings.findChildViewById(rootView, id);
      if (overallProgressText == null) {
        break missingId;
      }

      id = R.id.personality_avg_text;
      TextView personalityAvgText = ViewBindings.findChildViewById(rootView, id);
      if (personalityAvgText == null) {
        break missingId;
      }

      id = R.id.preferred_type_text;
      TextView preferredTypeText = ViewBindings.findChildViewById(rootView, id);
      if (preferredTypeText == null) {
        break missingId;
      }

      id = R.id.strength_area_text;
      TextView strengthAreaText = ViewBindings.findChildViewById(rootView, id);
      if (strengthAreaText == null) {
        break missingId;
      }

      id = R.id.strength_consistency_text;
      TextView strengthConsistencyText = ViewBindings.findChildViewById(rootView, id);
      if (strengthConsistencyText == null) {
        break missingId;
      }

      id = R.id.strength_score_text;
      TextView strengthScoreText = ViewBindings.findChildViewById(rootView, id);
      if (strengthScoreText == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.top_performing_score_text;
      TextView topPerformingScoreText = ViewBindings.findChildViewById(rootView, id);
      if (topPerformingScoreText == null) {
        break missingId;
      }

      id = R.id.top_performing_test_text;
      TextView topPerformingTestText = ViewBindings.findChildViewById(rootView, id);
      if (topPerformingTestText == null) {
        break missingId;
      }

      return new ActivityAnalyticsBinding((LinearLayout) rootView, cognitiveAvgText,
          improvementActionText, improvementAreaText, improvementScoreText, insight1Description,
          insight1Title, insight2Container, insight2Description, insight2Title, insight3Container,
          insight3Description, insight3Title, overallProgressBar, overallProgressText,
          personalityAvgText, preferredTypeText, strengthAreaText, strengthConsistencyText,
          strengthScoreText, toolbar, topPerformingScoreText, topPerformingTestText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
