package com.leapiq.braintraining.ui.tests

import com.leapiq.braintraining.R

/**
 * Information page for Learning Style Test
 * Shows test description, instructions, and start button
 */
class LearningStyleInfoActivity : BaseTestInfoActivity() {
    
    override fun loadTestInfo() {
        setTestInfo(
            title = "Learning Style Assessment",
            subtitle = "Personality Test",
            description = "Discover your preferred learning style and how you best process information. Learn whether you prefer visual, auditory, or kinesthetic learning methods.",
            keyPoints = listOf(
                "Think about your natural learning preferences",
                "Choose how you typically behave when learning",
                "Consider your most effective study methods",
                "No right or wrong answers - be honest"
            ),
            estimatedMinutes = 10,
            iconResource = R.drawable.ic_learning
        )
    }
    
    override fun getTestActivityClass(): Class<*> {
        return LearningStyleActivity::class.java
    }
    
    override fun getHeroGradient(): Int {
        return R.drawable.gradient_learning_style
    }
}
