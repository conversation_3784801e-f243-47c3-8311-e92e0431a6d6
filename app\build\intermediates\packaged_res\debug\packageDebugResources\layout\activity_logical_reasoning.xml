<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.games.LogicalReasoningActivity">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/primary_light_blue"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Quit Button -->
        <ImageButton
            android:id="@+id/btn_quit"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="Quit game"
            app:tint="@color/text_white" />

        <!-- Game Title -->
        <TextView
            android:id="@+id/game_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Logical Reasoning"
            android:textColor="@color/text_white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Menu Button -->
        <ImageButton
            android:id="@+id/btn_menu"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_menu"
            android:contentDescription="Game menu"
            app:tint="@color/text_white" />

    </LinearLayout>

    <!-- Game Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:elevation="2dp"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Level -->
        <TextView
            android:id="@+id/level_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Level 1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Round -->
        <TextView
            android:id="@+id/round_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Round 1/3"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Trial -->
        <TextView
            android:id="@+id/trial_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Puzzle 1/8"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Puzzle Type -->
    <TextView
        android:id="@+id/puzzle_type_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/primary_light_blue_light"
        android:gravity="center"
        android:padding="12dp"
        android:text="SYLLOGISM"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold" />

    <!-- Instructions -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:padding="16dp"
        android:text="Read the premises and choose the logical conclusion!"
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />

    <!-- Premises Display -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/surface_white"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Premises -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="PREMISES:"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/premises_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/logic_premises_background"
                android:padding="16dp"
                android:text="All cats are animals.\nAll animals are living things."
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:lineSpacingExtra="4dp"
                android:layout_marginBottom="16dp"
                tools:text="All cats are animals.\nAll animals are living things." />

            <!-- Question -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="QUESTION:"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/question_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/logic_question_background"
                android:padding="16dp"
                android:text="What can we conclude?"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="What can we conclude?" />

        </LinearLayout>

    </ScrollView>

    <!-- Answer Options -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- First Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btn_option1"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="All cats are living things"
                android:textSize="12sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp"
                android:elevation="2dp"
                tools:text="All cats are living things" />

            <Button
                android:id="@+id/btn_option2"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="All living things are cats"
                android:textSize="12sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp"
                android:elevation="2dp"
                tools:text="All living things are cats" />

        </LinearLayout>

        <!-- Second Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_option3"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="Some cats are not living things"
                android:textSize="12sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp"
                android:elevation="2dp"
                tools:text="Some cats are not living things" />

            <Button
                android:id="@+id/btn_option4"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="No cats are living things"
                android:textSize="12sp"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="8dp"
                android:elevation="2dp"
                tools:text="No cats are living things" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
