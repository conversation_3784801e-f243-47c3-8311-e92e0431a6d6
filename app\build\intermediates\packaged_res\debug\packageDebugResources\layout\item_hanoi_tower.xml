<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tower_container"
    android:layout_width="0dp"
    android:layout_height="match_parent"
    android:layout_weight="1"
    android:layout_margin="8dp"
    android:background="@drawable/hanoi_tower_normal_background"
    android:clickable="true"
    android:focusable="true"
    android:orientation="vertical"
    android:padding="8dp">

    <!-- Tower Name -->
    <TextView
        android:id="@+id/tower_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="Tower A"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <!-- Disks Container (stacked from bottom to top) -->
    <LinearLayout
        android:id="@+id/disks_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center_horizontal|bottom"
        android:orientation="vertical" />

    <!-- Tower Peg (base) -->
    <View
        android:id="@+id/tower_peg"
        android:layout_width="8dp"
        android:layout_height="200dp"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/hanoi_tower_peg" />

    <!-- Tower Base -->
    <View
        android:layout_width="80dp"
        android:layout_height="8dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="4dp"
        android:background="@drawable/hanoi_tower_base" />

</LinearLayout>
