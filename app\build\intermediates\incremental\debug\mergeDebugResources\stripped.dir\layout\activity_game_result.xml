<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    android:padding="24dp"
    android:gravity="center"
    tools:context=".ui.games.GameResultActivity">

    <!-- Game Title -->
    <TextView
        android:id="@+id/game_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Card Matching"
        android:textColor="@color/text_primary"
        android:textSize="24sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp"
        tools:text="Card Matching" />

    <!-- Level -->
    <TextView
        android:id="@+id/level_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Level 1 Completed"
        android:textColor="@color/primary_light_blue"
        android:textSize="18sp"
        android:layout_marginBottom="32dp"
        tools:text="Level 1 Completed" />

    <!-- Results Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Performance Message -->
            <TextView
                android:id="@+id/performance_message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Great job! Very good accuracy!"
                android:textColor="@color/primary_light_blue"
                android:textSize="16sp"
                android:textStyle="bold"
                android:gravity="center"
                android:layout_marginBottom="24dp"
                tools:text="Great job! Very good accuracy!" />

            <!-- Accuracy -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Accuracy"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/accuracy_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="80%"
                    android:textColor="@color/primary_light_blue"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    tools:text="80%" />

            </LinearLayout>

            <!-- Accuracy Progress Bar -->
            <ProgressBar
                android:id="@+id/accuracy_progress"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:layout_marginBottom="16dp"
                android:progress="80"
                android:max="100"
                android:progressTint="@color/primary_light_blue"
                android:progressBackgroundTint="@color/background_light_gray" />

            <!-- Time -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Time Spent"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/time_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="2:45"
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp"
                    tools:text="2:45" />

            </LinearLayout>

            <!-- Score -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="12dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Score"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/score_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="80"
                    android:textColor="@color/primary_light_blue"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    tools:text="80" />

            </LinearLayout>

            <!-- Rounds -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Rounds Correct"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp" />

                <TextView
                    android:id="@+id/rounds_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="4/5"
                    android:textColor="@color/text_secondary"
                    android:textSize="16sp"
                    tools:text="4/5" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Action Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center">

        <!-- Next Level / Retry Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/next_level_button"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:layout_marginBottom="12dp"
            android:text="Next Level (2)"
            android:textSize="16sp"
            android:textStyle="bold"
            app:cornerRadius="28dp"
            style="@style/Widget.Material3.Button"
            tools:text="Next Level (2)" />

        <!-- Replay Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/replay_button"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="12dp"
            android:text="Replay Level"
            android:textSize="14sp"
            app:cornerRadius="24dp"
            style="@style/Widget.Material3.Button.OutlinedButton"
            tools:text="Replay Level" />

        <!-- Back to Games Button -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/back_to_games_button"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:text="Back to Games"
            android:textSize="14sp"
            app:cornerRadius="24dp"
            style="@style/Widget.Material3.Button.TextButton"
            tools:text="Back to Games" />

    </LinearLayout>

</LinearLayout>
