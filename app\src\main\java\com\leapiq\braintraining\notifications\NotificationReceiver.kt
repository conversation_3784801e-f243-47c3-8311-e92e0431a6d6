package com.leapiq.braintraining.notifications

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import java.util.*

class NotificationReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context, intent: Intent) {
        val action = intent.getIntExtra("action", -1)
        val sharedPreferences = context.getSharedPreferences("LeapIQ_Prefs", Context.MODE_PRIVATE)
        val userName = sharedPreferences.getString("user_name", "User") ?: "User"
        val notificationService = NotificationService(context)
        
        when (action) {
            NotificationScheduler.DAILY_REMINDER_REQUEST_CODE -> {
                notificationService.sendDailyReminder(userName)
            }
            
            NotificationScheduler.CHALLENGE_REMINDER_REQUEST_CODE -> {
                // Only send if user hasn't completed today's challenge
                if (!hasCompletedTodaysChallenge(sharedPreferences)) {
                    notificationService.sendChallengeReminder(userName)
                }
            }
            
            NotificationScheduler.MOTIVATIONAL_TIP_REQUEST_CODE -> {
                val tips = notificationService.getMotivationalTips()
                val randomTip = tips.random()
                notificationService.sendMotivationalTip(randomTip)
            }
        }
    }
    
    private fun hasCompletedTodaysChallenge(sharedPreferences: SharedPreferences): Boolean {
        val today = Calendar.getInstance()
        val todayString = "${today.get(Calendar.YEAR)}-${today.get(Calendar.MONTH)}-${today.get(Calendar.DAY_OF_MONTH)}"
        val lastChallengeDate = sharedPreferences.getString("last_challenge_date", "")
        
        return lastChallengeDate == todayString
    }
}
