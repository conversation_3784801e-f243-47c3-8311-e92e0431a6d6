<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_hanoi_tower" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\item_hanoi_tower.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout" rootNodeViewId="@+id/tower_container"><Targets><Target id="@+id/tower_container" tag="layout/item_hanoi_tower_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="50" endOffset="14"/></Target><Target id="@+id/tower_name" view="TextView"><Expressions/><location startLine="14" startOffset="4" endLine="23" endOffset="43"/></Target><Target id="@+id/disks_container" view="LinearLayout"><Expressions/><location startLine="26" startOffset="4" endLine="32" endOffset="40"/></Target><Target id="@+id/tower_peg" view="View"><Expressions/><location startLine="35" startOffset="4" endLine="40" endOffset="56"/></Target></Targets></Layout>