// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemDetailedScoreBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView categoryText;

  @NonNull
  public final ProgressBar scoreProgress;

  @NonNull
  public final TextView scoreText;

  private ItemDetailedScoreBinding(@NonNull LinearLayout rootView, @NonNull TextView categoryText,
      @NonNull ProgressBar scoreProgress, @NonNull TextView scoreText) {
    this.rootView = rootView;
    this.categoryText = categoryText;
    this.scoreProgress = scoreProgress;
    this.scoreText = scoreText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemDetailedScoreBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemDetailedScoreBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_detailed_score, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemDetailedScoreBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.categoryText;
      TextView categoryText = ViewBindings.findChildViewById(rootView, id);
      if (categoryText == null) {
        break missingId;
      }

      id = R.id.scoreProgress;
      ProgressBar scoreProgress = ViewBindings.findChildViewById(rootView, id);
      if (scoreProgress == null) {
        break missingId;
      }

      id = R.id.scoreText;
      TextView scoreText = ViewBindings.findChildViewById(rootView, id);
      if (scoreText == null) {
        break missingId;
      }

      return new ItemDetailedScoreBinding((LinearLayout) rootView, categoryText, scoreProgress,
          scoreText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
