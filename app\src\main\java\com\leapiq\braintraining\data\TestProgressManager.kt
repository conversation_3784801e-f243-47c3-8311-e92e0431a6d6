package com.leapiq.braintraining.data

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.leapiq.braintraining.data.model.TestResult
import com.leapiq.braintraining.data.model.TestProgress
import java.util.Date

/**
 * Manages test progress and results storage using SharedPreferences
 */
class TestProgressManager private constructor(context: Context) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    private val gson = Gson()
    
    companion object {
        private const val PREFS_NAME = "test_progress"
        private const val KEY_TEST_RESULTS = "test_results"
        private const val KEY_TEST_PROGRESSES = "test_progresses"
        
        @Volatile
        private var INSTANCE: TestProgressManager? = null
        
        fun getInstance(context: Context): TestProgressManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TestProgressManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    /**
     * Save a completed test result and update progress
     */
    fun saveTestResult(testResult: TestResult) {
        // Save the test result
        val existingResults = getTestResults(testResult.testId).toMutableList()
        existingResults.add(testResult)
        saveTestResults(testResult.testId, existingResults)
        
        // Update test progress
        updateTestProgress(testResult.testId, testResult)
    }
    
    /**
     * Get current progress for a specific test
     */
    fun getTestProgress(testId: String): TestProgress {
        val results = getTestResults(testId)
        
        return if (results.isNotEmpty()) {
            val bestScore = results.maxOf { it.score }
            val averageScore = results.map { it.score }.average()
            val totalTime = results.sumOf { it.totalTimeMs }
            val lastCompleted = results.maxByOrNull { it.completedAt }?.completedAt
            
            // Calculate improvement trend (last 3 vs previous 3)
            val improvementTrend = calculateImprovementTrend(results)
            
            TestProgress(
                testId = testId,
                timesCompleted = results.size,
                bestScore = bestScore,
                averageScore = averageScore,
                totalTimeTaken = totalTime,
                lastCompletedAt = lastCompleted,
                testResults = results,
                improvementTrend = improvementTrend
            )
        } else {
            TestProgress(
                testId = testId,
                timesCompleted = 0,
                bestScore = 0,
                averageScore = 0.0,
                totalTimeTaken = 0L,
                lastCompletedAt = null
            )
        }
    }
    
    /**
     * Get test results for a specific test
     */
    fun getTestResults(testId: String): List<TestResult> {
        val json = prefs.getString("${KEY_TEST_RESULTS}_$testId", null)
        return if (json != null) {
            try {
                val type = object : TypeToken<List<TestResult>>() {}.type
                gson.fromJson(json, type) ?: emptyList()
            } catch (e: Exception) {
                emptyList()
            }
        } else {
            emptyList()
        }
    }
    
    /**
     * Get all test progresses
     */
    fun getAllTestProgresses(): Map<String, TestProgress> {
        val testIds = listOf(
            "memory_assessment", "attention_test", "processing_speed",
            "learning_style", "stress_response", "problem_solving"
        )
        
        return testIds.associateWith { testId ->
            getTestProgress(testId)
        }
    }
    
    /**
     * Check if a test has been completed at least once
     */
    fun isTestCompleted(testId: String): Boolean {
        return getTestProgress(testId).timesCompleted > 0
    }
    
    /**
     * Get the last score for a test
     */
    fun getLastScore(testId: String): Int? {
        val results = getTestResults(testId)
        return results.lastOrNull()?.score
    }
    
    /**
     * Get the last completion date for a test
     */
    fun getLastCompletionDate(testId: String): Date? {
        return getTestProgress(testId).lastCompletedAt
    }
    
    /**
     * Save test results to SharedPreferences
     */
    private fun saveTestResults(testId: String, results: List<TestResult>) {
        val json = gson.toJson(results)
        prefs.edit {
            putString("${KEY_TEST_RESULTS}_$testId", json)
        }
    }
    
    /**
     * Update test progress based on new result
     */
    private fun updateTestProgress(testId: String, newResult: TestResult) {
        // Progress is calculated dynamically in getTestProgress()
        // Log the new result for potential analytics
        android.util.Log.d("TestProgress", "New result for $testId: score=${newResult.score}")

        // Could trigger notifications, achievements, etc. based on progress
        val progress = getTestProgress(testId)
        if (progress.timesCompleted == 1) {
            android.util.Log.d("TestProgress", "First completion of $testId!")
        }
    }
    
    /**
     * Calculate improvement trend based on recent results
     */
    private fun calculateImprovementTrend(results: List<TestResult>): Double {
        if (results.size < 6) return 0.0
        
        val sortedResults = results.sortedBy { it.completedAt }
        val recent = sortedResults.takeLast(3).map { it.score }
        val previous = sortedResults.dropLast(3).takeLast(3).map { it.score }
        
        val recentAvg = recent.average()
        val previousAvg = previous.average()
        
        return recentAvg - previousAvg
    }
    
    /**
     * Clear all test data (for testing purposes)
     */
    fun clearAllTestData() {
        prefs.edit {
            clear()
        }
    }
    
    /**
     * Get test results within a date range
     */
    fun getTestResultsInRange(testId: String, startDate: Date, endDate: Date): List<TestResult> {
        return getTestResults(testId).filter { result ->
            result.completedAt.after(startDate) && result.completedAt.before(endDate)
        }
    }

    /**
     * Get recent test results (last N results)
     */
    fun getRecentTestResults(testId: String, count: Int): List<TestResult> {
        return getTestResults(testId)
            .sortedByDescending { it.completedAt }
            .take(count)
    }

    /**
     * Get all test results across all tests
     */
    fun getAllTestResults(): Map<String, List<TestResult>> {
        val testIds = listOf(
            "memory_assessment", "attention_test", "processing_speed",
            "learning_style", "stress_response", "problem_solving"
        )

        return testIds.associateWith { testId ->
            getTestResults(testId)
        }.filterValues { it.isNotEmpty() }
    }

    /**
     * Get performance trends for a test
     */
    fun getPerformanceTrends(testId: String): PerformanceTrend {
        val results = getTestResults(testId).sortedBy { it.completedAt }

        if (results.size < 2) {
            return PerformanceTrend(
                testId = testId,
                trendDirection = TrendDirection.STABLE,
                improvementPercentage = 0.0,
                consistencyScore = 0.0,
                dataPoints = results.map { TrendDataPoint(it.completedAt, it.score.toDouble()) }
            )
        }

        // Calculate trend
        val scores = results.map { it.score.toDouble() }
        val trendDirection = calculateTrendDirection(scores)
        val improvementPercentage = calculateImprovementPercentage(scores)
        val consistencyScore = calculateConsistencyScore(scores)

        return PerformanceTrend(
            testId = testId,
            trendDirection = trendDirection,
            improvementPercentage = improvementPercentage,
            consistencyScore = consistencyScore,
            dataPoints = results.map { TrendDataPoint(it.completedAt, it.score.toDouble()) }
        )
    }

    /**
     * Get comprehensive test statistics
     */
    fun getTestStatistics(testId: String): TestStatistics {
        val results = getTestResults(testId)

        if (results.isEmpty()) {
            return TestStatistics(
                testId = testId,
                totalAttempts = 0,
                averageScore = 0.0,
                bestScore = 0,
                worstScore = 0,
                averageTime = 0L,
                fastestTime = 0L,
                slowestTime = 0L,
                firstAttemptDate = null,
                lastAttemptDate = null,
                streakCount = 0,
                improvementRate = 0.0
            )
        }

        val scores = results.map { it.score }
        val times = results.map { it.totalTimeMs }

        return TestStatistics(
            testId = testId,
            totalAttempts = results.size,
            averageScore = scores.average(),
            bestScore = scores.maxOrNull() ?: 0,
            worstScore = scores.minOrNull() ?: 0,
            averageTime = times.average().toLong(),
            fastestTime = times.minOrNull() ?: 0L,
            slowestTime = times.maxOrNull() ?: 0L,
            firstAttemptDate = results.minByOrNull { it.completedAt }?.completedAt,
            lastAttemptDate = results.maxByOrNull { it.completedAt }?.completedAt,
            streakCount = calculateCurrentStreak(results),
            improvementRate = calculateImprovementRate(results)
        )
    }

    /**
     * Export test data for backup
     */
    fun exportTestData(): String {
        val allData = mutableMapOf<String, Any>()

        val testIds = listOf(
            "memory_assessment", "attention_test", "processing_speed",
            "learning_style", "stress_response", "problem_solving"
        )

        testIds.forEach { testId ->
            val results = getTestResults(testId)
            if (results.isNotEmpty()) {
                allData[testId] = results
            }
        }

        // Add metadata
        allData["export_date"] = Date()
        allData["version"] = "1.0"

        return gson.toJson(allData)
    }

    /**
     * Import test data from backup
     */
    fun importTestData(jsonData: String): Boolean {
        return try {
            val type = object : TypeToken<Map<String, Any>>() {}.type
            val data: Map<String, Any> = gson.fromJson(jsonData, type)

            val testIds = listOf(
                "memory_assessment", "attention_test", "processing_speed",
                "learning_style", "stress_response", "problem_solving"
            )

            testIds.forEach { testId ->
                if (data.containsKey(testId)) {
                    val resultsJson = gson.toJson(data[testId])
                    val resultsType = object : TypeToken<List<TestResult>>() {}.type
                    val results: List<TestResult> = gson.fromJson(resultsJson, resultsType)
                    saveTestResults(testId, results)
                }
            }

            true
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Calculate trend direction from scores
     */
    private fun calculateTrendDirection(scores: List<Double>): TrendDirection {
        if (scores.size < 3) return TrendDirection.STABLE

        val recent = scores.takeLast(3).average()
        val previous = scores.dropLast(3).takeLast(3).average()

        return when {
            recent > previous + 5 -> TrendDirection.IMPROVING
            recent < previous - 5 -> TrendDirection.DECLINING
            else -> TrendDirection.STABLE
        }
    }

    /**
     * Calculate improvement percentage
     */
    private fun calculateImprovementPercentage(scores: List<Double>): Double {
        if (scores.size < 2) return 0.0

        val first = scores.first()
        val last = scores.last()

        return if (first > 0) ((last - first) / first) * 100 else 0.0
    }

    /**
     * Calculate consistency score (lower variance = higher consistency)
     */
    private fun calculateConsistencyScore(scores: List<Double>): Double {
        if (scores.size < 2) return 100.0

        val mean = scores.average()
        val variance = scores.map { (it - mean) * (it - mean) }.average()
        val standardDeviation = kotlin.math.sqrt(variance)

        // Convert to consistency score (0-100, higher is more consistent)
        return maxOf(0.0, 100.0 - (standardDeviation * 2))
    }

    /**
     * Calculate current streak of improving scores
     */
    private fun calculateCurrentStreak(results: List<TestResult>): Int {
        if (results.size < 2) return 0

        val sortedResults = results.sortedBy { it.completedAt }
        var streak = 0

        for (i in 1 until sortedResults.size) {
            if (sortedResults[i].score >= sortedResults[i-1].score) {
                streak++
            } else {
                streak = 0
            }
        }

        return streak
    }

    /**
     * Calculate improvement rate (score improvement per attempt)
     */
    private fun calculateImprovementRate(results: List<TestResult>): Double {
        if (results.size < 2) return 0.0

        val sortedResults = results.sortedBy { it.completedAt }
        val firstScore = sortedResults.first().score.toDouble()
        val lastScore = sortedResults.last().score.toDouble()
        val attempts = sortedResults.size

        return (lastScore - firstScore) / attempts
    }
}

/**
 * Data classes for enhanced test analytics
 */
data class PerformanceTrend(
    val testId: String,
    val trendDirection: TrendDirection,
    val improvementPercentage: Double,
    val consistencyScore: Double,
    val dataPoints: List<TrendDataPoint>
)

data class TrendDataPoint(
    val date: Date,
    val score: Double
)

enum class TrendDirection {
    IMPROVING,
    DECLINING,
    STABLE
}

data class TestStatistics(
    val testId: String,
    val totalAttempts: Int,
    val averageScore: Double,
    val bestScore: Int,
    val worstScore: Int,
    val averageTime: Long,
    val fastestTime: Long,
    val slowestTime: Long,
    val firstAttemptDate: Date?,
    val lastAttemptDate: Date?,
    val streakCount: Int,
    val improvementRate: Double
)
