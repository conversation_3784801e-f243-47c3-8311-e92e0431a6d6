<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray">

        <!-- Toolbar -->
        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/surface_white"
            android:elevation="4dp"
            app:navigationIcon="@drawable/ic_arrow_back"
            app:title="Test Information"
            app:titleTextColor="@color/text_primary" />

    <!-- Hero Section -->
    <LinearLayout
        android:id="@+id/hero_section"
        android:layout_width="match_parent"
        android:layout_height="180dp"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="20dp">

        <ImageView
            android:id="@+id/test_icon"
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:layout_marginBottom="12dp"
            android:src="@drawable/ic_test_placeholder"
            android:tint="@color/surface_white" />

        <TextView
            android:id="@+id/test_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Test Title"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@color/surface_white"
            android:gravity="center" />

        <TextView
            android:id="@+id/test_subtitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="Test Category"
            android:textSize="14sp"
            android:textColor="@color/surface_white"
            android:alpha="0.9" />

    </LinearLayout>

    <!-- Content Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- Single Condensed Info Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/surface_white">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="20dp">

                <!-- Description -->
                <TextView
                    android:id="@+id/test_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Test description will appear here..."
                    android:textSize="16sp"
                    android:textColor="@color/text_primary"
                    android:lineSpacingMultiplier="1.2"
                    android:layout_marginBottom="16dp" />

                <!-- Key Points -->
                <LinearLayout
                    android:id="@+id/key_points_list"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Time Badge -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="@drawable/rounded_background_light"
            android:padding="16dp"
            android:layout_marginBottom="24dp">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_timer"
                android:tint="@color/primary_light_blue"
                android:layout_marginEnd="8dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Estimated Time"
                android:textSize="14sp"
                android:textColor="@color/text_secondary" />

            <TextView
                android:id="@+id/estimated_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="8 minutes"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/primary_light_blue" />

        </LinearLayout>

    </LinearLayout>

    <!-- Start Test Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_start_test"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:layout_margin="20dp"
        android:text="Start Test"
        android:textSize="18sp"
        android:textStyle="bold"
        app:cornerRadius="28dp"
        app:backgroundTint="@color/primary_light_blue"
        android:textColor="@color/surface_white" />

</LinearLayout>
