package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityVocabularyBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Vocabulary Game
 * Players match words with their definitions
 * Tests vocabulary knowledge and word comprehension
 */
class VocabularyActivity : AppCompatActivity() {

    private lateinit var binding: ActivityVocabularyBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "language_3"

    // Vocabulary specific
    private var currentMode = VocabularyMode.WORD_TO_DEFINITION
    private var currentWord = ""
    private var correctDefinition = ""
    private var answerOptions = mutableListOf<String>()
    private var problemStartTime = 0L
    private var currentTrial = 0
    private var trialsPerRound = 10
    private var reactionTimes = mutableListOf<Long>()

    // Vocabulary modes
    private enum class VocabularyMode {
        WORD_TO_DEFINITION,    // Show word, choose definition
        DEFINITION_TO_WORD     // Show definition, choose word
    }

    // Vocabulary database
    private val vocabularyDatabase = mapOf(
        // Level 1-3: Basic vocabulary
        "happy" to "feeling joy or pleasure",
        "large" to "of great size or extent",
        "quick" to "moving fast or doing something in a short time",
        "bright" to "giving out or reflecting much light",
        "strong" to "having great power or force",
        "gentle" to "mild in temperament or behavior",
        "honest" to "free of deceit; truthful and sincere",
        "brave" to "showing courage in the face of danger",
        
        // Level 4-6: Intermediate vocabulary
        "abundant" to "existing in large quantities; plentiful",
        "curious" to "eager to know or learn something",
        "elegant" to "graceful and stylish in appearance",
        "generous" to "showing readiness to give more than necessary",
        "patient" to "able to accept delays without becoming annoyed",
        "sincere" to "free from pretense or deceit; genuine",
        "vibrant" to "full of energy and life",
        "wisdom" to "the quality of having experience and good judgment",
        
        // Level 7-10: Advanced vocabulary
        "articulate" to "having or showing the ability to speak fluently",
        "benevolent" to "well meaning and kindly",
        "diligent" to "having or showing care in one's work or duties",
        "eloquent" to "fluent or persuasive in speaking or writing",
        "innovative" to "featuring new methods; advanced and original",
        "meticulous" to "showing great attention to detail; very careful",
        "resilient" to "able to withstand or recover quickly from difficulties",
        "versatile" to "able to adapt or be adapted to many different functions",
        
        // Level 11-15: Academic vocabulary
        "ambiguous" to "open to more than one interpretation; unclear",
        "comprehensive" to "complete and including everything that is necessary",
        "empirical" to "based on observation or experience rather than theory",
        "hypothesis" to "a supposition made as a starting point for investigation",
        "methodology" to "a system of methods used in a particular area of study",
        "paradigm" to "a typical example or pattern of something",
        "synthesis" to "the combination of components to form a connected whole",
        "theoretical" to "concerned with or involving theory rather than practice",
        
        // Level 16+: Advanced academic vocabulary
        "anachronism" to "something belonging to a period other than that being portrayed",
        "dichotomy" to "a division into two mutually exclusive groups",
        "epistemology" to "the theory of knowledge and justified belief",
        "juxtaposition" to "the fact of two things being placed close together",
        "metamorphosis" to "a change of the form or nature of something",
        "nomenclature" to "the devising or choosing of names for things",
        "quintessential" to "representing the most perfect example of a quality",
        "ubiquitous" to "present, appearing, or found everywhere"
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityVocabularyBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.vocabulary)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Match the word with its definition!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup answer buttons
            setupAnswerButtons()
        }
    }

    private fun setupAnswerButtons() {
        binding.apply {
            btnOption1.setOnClickListener { selectAnswer(0) }
            btnOption2.setOnClickListener { selectAnswer(1) }
            btnOption3.setOnClickListener { selectAnswer(2) }
            btnOption4.setOnClickListener { selectAnswer(3) }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        currentTrial = 0
        reactionTimes.clear()
        trialsPerRound = getTrialsPerRound(currentLevel)
        
        binding.trialText.text = "Word 1/$trialsPerRound"
        
        generateNextVocabulary()
    }

    private fun getTrialsPerRound(level: Int): Int {
        return when (level) {
            in 1..5 -> 10      // 10 words per round
            in 6..10 -> 12     // 12 words per round
            in 11..15 -> 15    // 15 words per round
            in 16..20 -> 18    // 18 words per round
            else -> 20         // 20 words per round
        }
    }

    private fun generateNextVocabulary() {
        if (currentTrial >= trialsPerRound) {
            roundComplete()
            return
        }

        currentTrial++
        binding.trialText.text = "Word $currentTrial/$trialsPerRound"
        
        // Alternate between modes
        currentMode = if (Random.nextBoolean()) {
            VocabularyMode.WORD_TO_DEFINITION
        } else {
            VocabularyMode.DEFINITION_TO_WORD
        }
        
        val availableWords = getAvailableWords(currentLevel)
        val selectedEntry = availableWords.entries.toList().random()
        
        generateVocabularyPuzzle(selectedEntry.key, selectedEntry.value)
        problemStartTime = System.currentTimeMillis()
    }

    private fun getAvailableWords(level: Int): Map<String, String> {
        val allWords = vocabularyDatabase.toList()
        return when (level) {
            in 1..3 -> allWords.take(8).toMap()
            in 4..6 -> allWords.drop(8).take(8).toMap()
            in 7..10 -> allWords.drop(16).take(8).toMap()
            in 11..15 -> allWords.drop(24).take(8).toMap()
            else -> allWords.drop(32).toMap()
        }
    }

    private fun generateVocabularyPuzzle(word: String, definition: String) {
        currentWord = word
        correctDefinition = definition
        
        when (currentMode) {
            VocabularyMode.WORD_TO_DEFINITION -> generateWordToDefinition()
            VocabularyMode.DEFINITION_TO_WORD -> generateDefinitionToWord()
        }
    }

    private fun generateWordToDefinition() {
        // Show word, choose definition
        binding.apply {
            modeText.text = "WORD → DEFINITION"
            promptDisplay.text = currentWord.uppercase()
            questionText.text = "What does this word mean?"
        }
        
        // Generate wrong definitions
        val wrongDefinitions = mutableListOf<String>()
        val allDefinitions = vocabularyDatabase.values.toList()
        
        while (wrongDefinitions.size < 3) {
            val wrongDef = allDefinitions.random()
            if (wrongDef != correctDefinition && wrongDef !in wrongDefinitions) {
                wrongDefinitions.add(wrongDef)
            }
        }
        
        // Create answer options
        answerOptions = mutableListOf(correctDefinition)
        answerOptions.addAll(wrongDefinitions)
        answerOptions.shuffle()
        
        displayAnswerOptions()
    }

    private fun generateDefinitionToWord() {
        // Show definition, choose word
        binding.apply {
            modeText.text = "DEFINITION → WORD"
            promptDisplay.text = correctDefinition
            questionText.text = "Which word matches this definition?"
        }
        
        // Generate wrong words
        val wrongWords = mutableListOf<String>()
        val allWords = vocabularyDatabase.keys.toList()
        
        while (wrongWords.size < 3) {
            val wrongWord = allWords.random()
            if (wrongWord != currentWord && wrongWord !in wrongWords) {
                wrongWords.add(wrongWord)
            }
        }
        
        // Create answer options
        answerOptions = mutableListOf(currentWord)
        answerOptions.addAll(wrongWords)
        answerOptions.shuffle()
        
        displayAnswerOptions()
    }

    private fun displayAnswerOptions() {
        binding.apply {
            btnOption1.text = answerOptions[0]
            btnOption2.text = answerOptions[1]
            btnOption3.text = answerOptions[2]
            btnOption4.text = answerOptions[3]
        }
    }

    private fun selectAnswer(optionIndex: Int) {
        val selectedAnswer = answerOptions[optionIndex]
        val reactionTime = System.currentTimeMillis() - problemStartTime
        
        totalAttempts++
        val isCorrect = when (currentMode) {
            VocabularyMode.WORD_TO_DEFINITION -> selectedAnswer == correctDefinition
            VocabularyMode.DEFINITION_TO_WORD -> selectedAnswer == currentWord
        }
        
        if (isCorrect) {
            totalCorrect++
            reactionTimes.add(reactionTime)
        }
        
        showFeedback(selectedAnswer, isCorrect, reactionTime)
        
        // Continue to next vocabulary after delay
        Handler(Looper.getMainLooper()).postDelayed({
            generateNextVocabulary()
        }, 3000)
    }

    private fun showFeedback(selectedAnswer: String, isCorrect: Boolean, reactionTime: Long) {
        val feedback = if (isCorrect) {
            "Correct! You selected: \"$selectedAnswer\" (${reactionTime}ms)\n'$currentWord' = $correctDefinition"
        } else {
            when (currentMode) {
                VocabularyMode.WORD_TO_DEFINITION ->
                    "Wrong! You selected: \"$selectedAnswer\"\n'$currentWord' means: $correctDefinition"
                VocabularyMode.DEFINITION_TO_WORD ->
                    "Wrong! You selected: \"$selectedAnswer\"\nThe correct word is '$currentWord': $correctDefinition"
            }
        }
        
        binding.instructionText.text = feedback
        binding.instructionText.setTextColor(
            ContextCompat.getColor(this, if (isCorrect) R.color.stroop_green else R.color.stroop_red)
        )
        
        // Reset color after delay
        Handler(Looper.getMainLooper()).postDelayed({
            binding.instructionText.text = "Match the word with its definition!"
            binding.instructionText.setTextColor(
                ContextCompat.getColor(this, R.color.text_secondary)
            )
        }, 3000)
    }

    private fun roundComplete() {
        val avgReactionTime = if (reactionTimes.isNotEmpty()) {
            reactionTimes.average().toLong()
        } else 0L
        
        val roundAccuracy = if (trialsPerRound > 0) {
            (totalCorrect.toDouble() / trialsPerRound)
        } else 1.0
        
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Accuracy: ${(roundAccuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            // Reset for next round
            currentTrial = 0
            reactionTimes.clear()
            
            Handler(Looper.getMainLooper()).postDelayed({
                generateNextVocabulary()
            }, 2500)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0
        val avgReactionTime = if (reactionTimes.isNotEmpty()) reactionTimes.average().toLong() else 0L

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7,
                timeSpentMs = totalTime / maxRounds,
                attempts = trialsPerRound
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Avg Time: ${avgReactionTime}ms
                Words: $totalAttempts

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Total Words: $totalAttempts

                Vocabulary Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Match words with their correct definitions

                📋 GAME MODES:
                • Word → Definition: See a word, choose its meaning
                • Definition → Word: See a definition, choose the word

                💡 TIPS:
                • Read all options carefully before choosing
                • Think about word roots and prefixes
                • Consider context and usage
                • Learn from incorrect answers

                🏆 SCORING:
                • Accuracy = correct matches / total words
                • Speed = average response time
                • Higher levels have more advanced vocabulary
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
