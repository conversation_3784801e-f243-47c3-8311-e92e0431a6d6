// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ItemTubeBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final View ballSlot1;

  @NonNull
  public final View ballSlot2;

  @NonNull
  public final View ballSlot3;

  @NonNull
  public final View ballSlot4;

  @NonNull
  public final LinearLayout tubeContainer;

  private ItemTubeBinding(@NonNull LinearLayout rootView, @NonNull View ballSlot1,
      @NonNull View ballSlot2, @NonNull View ballSlot3, @NonNull View ballSlot4,
      @NonNull LinearLayout tubeContainer) {
    this.rootView = rootView;
    this.ballSlot1 = ballSlot1;
    this.ballSlot2 = ballSlot2;
    this.ballSlot3 = ballSlot3;
    this.ballSlot4 = ballSlot4;
    this.tubeContainer = tubeContainer;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ItemTubeBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ItemTubeBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.item_tube, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ItemTubeBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.ball_slot_1;
      View ballSlot1 = ViewBindings.findChildViewById(rootView, id);
      if (ballSlot1 == null) {
        break missingId;
      }

      id = R.id.ball_slot_2;
      View ballSlot2 = ViewBindings.findChildViewById(rootView, id);
      if (ballSlot2 == null) {
        break missingId;
      }

      id = R.id.ball_slot_3;
      View ballSlot3 = ViewBindings.findChildViewById(rootView, id);
      if (ballSlot3 == null) {
        break missingId;
      }

      id = R.id.ball_slot_4;
      View ballSlot4 = ViewBindings.findChildViewById(rootView, id);
      if (ballSlot4 == null) {
        break missingId;
      }

      LinearLayout tubeContainer = (LinearLayout) rootView;

      return new ItemTubeBinding((LinearLayout) rootView, ballSlot1, ballSlot2, ballSlot3,
          ballSlot4, tubeContainer);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
