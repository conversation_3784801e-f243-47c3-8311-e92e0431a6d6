// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class LayoutHeaderBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView headerPremium;

  @NonNull
  public final ImageView headerSettings;

  @NonNull
  public final LinearLayout headerStreak;

  @NonNull
  public final TextView headerTitle;

  @NonNull
  public final TextView streakCount;

  private LayoutHeaderBinding(@NonNull LinearLayout rootView, @NonNull TextView headerPremium,
      @NonNull ImageView headerSettings, @NonNull LinearLayout headerStreak,
      @NonNull TextView headerTitle, @NonNull TextView streakCount) {
    this.rootView = rootView;
    this.headerPremium = headerPremium;
    this.headerSettings = headerSettings;
    this.headerStreak = headerStreak;
    this.headerTitle = headerTitle;
    this.streakCount = streakCount;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static LayoutHeaderBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static LayoutHeaderBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.layout_header, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static LayoutHeaderBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.header_premium;
      TextView headerPremium = ViewBindings.findChildViewById(rootView, id);
      if (headerPremium == null) {
        break missingId;
      }

      id = R.id.header_settings;
      ImageView headerSettings = ViewBindings.findChildViewById(rootView, id);
      if (headerSettings == null) {
        break missingId;
      }

      id = R.id.header_streak;
      LinearLayout headerStreak = ViewBindings.findChildViewById(rootView, id);
      if (headerStreak == null) {
        break missingId;
      }

      id = R.id.header_title;
      TextView headerTitle = ViewBindings.findChildViewById(rootView, id);
      if (headerTitle == null) {
        break missingId;
      }

      id = R.id.streak_count;
      TextView streakCount = ViewBindings.findChildViewById(rootView, id);
      if (streakCount == null) {
        break missingId;
      }

      return new LayoutHeaderBinding((LinearLayout) rootView, headerPremium, headerSettings,
          headerStreak, headerTitle, streakCount);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
