<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.tests.TestsFragment">

    <!-- Header -->
    <include layout="@layout/layout_header" />

    <!-- My Results Button -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:background="@color/surface_white">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_my_results"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="📊 My Results"
            android:textColor="@color/surface_white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:backgroundTint="@color/primary_light_blue"
            app:cornerRadius="12dp"
            app:icon="@drawable/ic_analytics"
            app:iconGravity="start"
            app:iconPadding="8dp"
            app:iconTint="@color/surface_white" />

    </LinearLayout>

    <!-- Category Tabs -->
    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:elevation="2dp"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/tab_all_tests"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:text="@string/all_tests"
                android:textColor="@color/primary_light_blue"
                android:textSize="14sp"
                app:cornerRadius="20dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/tab_cognitive"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginEnd="8dp"
                android:text="@string/cognitive_tests"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                app:cornerRadius="20dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/tab_personality"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="@string/personality_tests"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                app:cornerRadius="20dp" />

        </LinearLayout>

    </HorizontalScrollView>

    <!-- Tests Grid -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/tests_recycler"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="8dp"
        android:clipToPadding="false"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="2"
        tools:listitem="@layout/item_test_card" />

</LinearLayout>
