package com.leapiq.braintraining.ui.tests.results

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.leapiq.braintraining.databinding.ActivityAnalyticsBinding
import com.leapiq.braintraining.data.TestResultsRepository
import com.leapiq.braintraining.analysis.TestAnalysisEngine

/**
 * Activity to display advanced analytics and insights
 */
class AnalyticsActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityAnalyticsBinding
    private lateinit var repository: TestResultsRepository
    private lateinit var analysisEngine: TestAnalysisEngine
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityAnalyticsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        repository = TestResultsRepository.getInstance(this)
        analysisEngine = TestAnalysisEngine(repository)
        
        setupUI()
        loadAnalytics()
    }
    
    private fun setupUI() {
        binding.apply {
            // Setup toolbar
            toolbar.setNavigationOnClickListener { finish() }
        }
    }
    
    private fun loadAnalytics() {
        val dashboardData = repository.getDashboardData()
        val comparativeAnalysis = repository.getComparativeAnalysis()
        val insights = repository.getPersonalizedInsights()
        
        binding.apply {
            // Overall Progress
            overallProgressBar.progress = dashboardData.overallProgress.completionPercentage.toInt()
            overallProgressText.text = "${dashboardData.overallProgress.completionPercentage.toInt()}% Complete"
            
            // Test Rankings
            val rankings = comparativeAnalysis.testRankings
            if (rankings.isNotEmpty()) {
                val topTest = rankings.first()
                topPerformingTestText.text = topTest.testName
                topPerformingScoreText.text = "${topTest.averageScore.toInt()}%"
            }
            
            // Cognitive vs Personality
            val comparison = comparativeAnalysis.cognitiveVsPersonality
            cognitiveAvgText.text = "${comparison.cognitiveAverage.toInt()}%"
            personalityAvgText.text = "${comparison.personalityAverage.toInt()}%"
            
            val preferredType = if (comparison.cognitiveAverage > comparison.personalityAverage) {
                "You tend to perform better on cognitive tests"
            } else {
                "You tend to perform better on personality assessments"
            }
            preferredTypeText.text = preferredType
            
            // Insights
            if (insights.isNotEmpty()) {
                val topInsights = insights.take(3)
                
                if (topInsights.isNotEmpty()) {
                    insight1Title.text = topInsights[0].title
                    insight1Description.text = topInsights[0].description
                }
                
                if (topInsights.size > 1) {
                    insight2Title.text = topInsights[1].title
                    insight2Description.text = topInsights[1].description
                    insight2Container.visibility = android.view.View.VISIBLE
                }
                
                if (topInsights.size > 2) {
                    insight3Title.text = topInsights[2].title
                    insight3Description.text = topInsights[2].description
                    insight3Container.visibility = android.view.View.VISIBLE
                }
            }
            
            // Improvement Areas
            val improvementAreas = dashboardData.improvementAreas
            if (improvementAreas.isNotEmpty()) {
                val topImprovement = improvementAreas.first()
                improvementAreaText.text = topImprovement.testName
                improvementScoreText.text = "${topImprovement.currentScore.toInt()}%"
                
                if (topImprovement.recommendedActions.isNotEmpty()) {
                    improvementActionText.text = topImprovement.recommendedActions.first()
                }
            }
            
            // Strengths
            val strengths = dashboardData.strongestAreas
            if (strengths.isNotEmpty()) {
                val topStrength = strengths.first()
                strengthAreaText.text = topStrength.testName
                strengthScoreText.text = "${topStrength.averageScore.toInt()}%"
                strengthConsistencyText.text = "Consistency: ${topStrength.consistency.toInt()}%"
            }
        }
    }
}
