package com.leapiq.braintraining.ui.today.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.databinding.ItemDailyChallengeBinding
import com.leapiq.braintraining.data.model.DailyChallenge

class DailyChallengeAdapter(
    private val onChallengeClick: (DailyChallenge) -> Unit
) : ListAdapter<DailyChallenge, DailyChallengeAdapter.ChallengeViewHolder>(DiffCallback) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ChallengeViewHolder {
        val binding = ItemDailyChallengeBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ChallengeViewHolder(binding)
    }

    override fun onBindViewHolder(holder: <PERSON>ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ChallengeViewHolder(
        private val binding: ItemDailyChallengeBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(challenge: DailyChallenge) {
            binding.challengeName.text = challenge.name
            binding.challengeCategory.text = challenge.category
            
            // Set icon based on category
            // TODO: Set appropriate icon based on challenge.iconResource
            
            binding.root.setOnClickListener {
                onChallengeClick(challenge)
            }
        }
    }

    companion object {
        private val DiffCallback = object : DiffUtil.ItemCallback<DailyChallenge>() {
            override fun areItemsTheSame(oldItem: DailyChallenge, newItem: DailyChallenge): Boolean {
                return oldItem.name == newItem.name
            }

            override fun areContentsTheSame(oldItem: DailyChallenge, newItem: DailyChallenge): Boolean {
                return oldItem == newItem
            }
        }
    }
}
