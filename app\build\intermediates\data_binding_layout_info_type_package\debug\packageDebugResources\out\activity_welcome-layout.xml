<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_welcome" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_welcome.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_welcome_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="81" endOffset="14"/></Target><Target id="@+id/viewPager" view="androidx.viewpager2.widget.ViewPager2"><Expressions/><location startLine="9" startOffset="4" endLine="13" endOffset="35"/></Target><Target id="@+id/tabLayout" view="com.google.android.material.tabs.TabLayout"><Expressions/><location startLine="16" startOffset="4" endLine="29" endOffset="33"/></Target><Target id="@+id/btnPrevious" view="Button"><Expressions/><location startLine="41" startOffset="8" endLine="51" endOffset="44"/></Target><Target id="@+id/btnSkip" view="Button"><Expressions/><location startLine="54" startOffset="8" endLine="65" endOffset="38"/></Target><Target id="@+id/btnNext" view="Button"><Expressions/><location startLine="68" startOffset="8" endLine="77" endOffset="37"/></Target></Targets></Layout>