// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class DialogGameMenuBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final Button btnContinue;

  @NonNull
  public final Button btnHowToPlay;

  @NonNull
  public final Button btnQuitMenu;

  @NonNull
  public final Button btnRestart;

  private DialogGameMenuBinding(@NonNull LinearLayout rootView, @NonNull Button btnContinue,
      @NonNull Button btnHowToPlay, @NonNull Button btnQuitMenu, @NonNull Button btnRestart) {
    this.rootView = rootView;
    this.btnContinue = btnContinue;
    this.btnHowToPlay = btnHowToPlay;
    this.btnQuitMenu = btnQuitMenu;
    this.btnRestart = btnRestart;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static DialogGameMenuBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static DialogGameMenuBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.dialog_game_menu, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static DialogGameMenuBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_continue;
      Button btnContinue = ViewBindings.findChildViewById(rootView, id);
      if (btnContinue == null) {
        break missingId;
      }

      id = R.id.btn_how_to_play;
      Button btnHowToPlay = ViewBindings.findChildViewById(rootView, id);
      if (btnHowToPlay == null) {
        break missingId;
      }

      id = R.id.btn_quit_menu;
      Button btnQuitMenu = ViewBindings.findChildViewById(rootView, id);
      if (btnQuitMenu == null) {
        break missingId;
      }

      id = R.id.btn_restart;
      Button btnRestart = ViewBindings.findChildViewById(rootView, id);
      if (btnRestart == null) {
        break missingId;
      }

      return new DialogGameMenuBinding((LinearLayout) rootView, btnContinue, btnHowToPlay,
          btnQuitMenu, btnRestart);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
