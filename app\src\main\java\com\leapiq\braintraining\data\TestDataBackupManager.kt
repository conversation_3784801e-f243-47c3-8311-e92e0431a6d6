package com.leapiq.braintraining.data

import android.content.Context
import android.os.Environment
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*
import com.leapiq.braintraining.data.model.TestProgress
import com.leapiq.braintraining.data.model.TestResult

/**
 * Manages backup and restore operations for test data
 */
class TestDataBackupManager private constructor(private val context: Context) {
    
    private val progressManager = TestProgressManager.getInstance(context)
    private val gson = GsonBuilder()
        .setDateFormat("yyyy-MM-dd HH:mm:ss")
        .setPrettyPrinting()
        .create()
    
    companion object {
        @Volatile
        private var INSTANCE: TestDataBackupManager? = null
        
        fun getInstance(context: Context): TestDataBackupManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: TestDataBackupManager(context.applicationContext).also { INSTANCE = it }
            }
        }
        
        private const val BACKUP_FOLDER = "LeapIQ_Backups"
        private const val BACKUP_FILE_PREFIX = "leapiq_backup_"
        private const val BACKUP_FILE_EXTENSION = ".json"
    }
    
    /**
     * Create a comprehensive backup of all test data
     */
    fun createBackup(): BackupResult {
        return try {
            val backupData = createBackupData()
            val backupFile = createBackupFile(backupData)
            
            BackupResult.Success(
                filePath = backupFile.absolutePath,
                fileSize = backupFile.length(),
                timestamp = Date(),
                testsIncluded = getTestsIncluded()
            )
        } catch (e: Exception) {
            BackupResult.Error(e.message ?: "Unknown error occurred during backup")
        }
    }
    
    /**
     * Restore test data from backup file
     */
    fun restoreFromBackup(filePath: String): RestoreResult {
        return try {
            val backupFile = File(filePath)
            if (!backupFile.exists()) {
                return RestoreResult.Error("Backup file not found")
            }
            
            val backupData = gson.fromJson(backupFile.readText(), BackupData::class.java)
            
            // Validate backup data
            if (!isValidBackupData(backupData)) {
                return RestoreResult.Error("Invalid backup data format")
            }
            
            // Restore data
            restoreTestData(backupData)
            
            RestoreResult.Success(
                testsRestored = backupData.testResults.size,
                timestamp = backupData.metadata.createdAt
            )
        } catch (e: Exception) {
            RestoreResult.Error(e.message ?: "Unknown error occurred during restore")
        }
    }
    
    /**
     * Get list of available backup files
     */
    fun getAvailableBackups(): List<BackupInfo> {
        val backupDir = getBackupDirectory()
        if (!backupDir.exists()) return emptyList()
        
        return backupDir.listFiles { file ->
            file.name.startsWith(BACKUP_FILE_PREFIX) && file.name.endsWith(BACKUP_FILE_EXTENSION)
        }?.mapNotNull { file ->
            try {
                val backupData = gson.fromJson(file.readText(), BackupData::class.java)
                BackupInfo(
                    filePath = file.absolutePath,
                    fileName = file.name,
                    createdAt = backupData.metadata.createdAt,
                    fileSize = file.length(),
                    testsCount = backupData.testResults.size,
                    version = backupData.metadata.version
                )
            } catch (e: Exception) {
                null // Skip invalid backup files
            }
        }?.sortedByDescending { it.createdAt } ?: emptyList()
    }
    
    /**
     * Delete a backup file
     */
    fun deleteBackup(filePath: String): Boolean {
        return try {
            val file = File(filePath)
            file.delete()
        } catch (e: Exception) {
            false
        }
    }
    
    /**
     * Export test data as CSV for external analysis
     */
    fun exportToCsv(): ExportResult {
        return try {
            val csvData = createCsvData()
            val csvFile = createCsvFile(csvData)
            
            ExportResult.Success(
                filePath = csvFile.absolutePath,
                fileSize = csvFile.length(),
                recordsExported = csvData.split("\n").size - 1 // Subtract header
            )
        } catch (e: Exception) {
            ExportResult.Error(e.message ?: "Unknown error occurred during CSV export")
        }
    }
    
    private fun createBackupData(): BackupData {
        val allResults = progressManager.getAllTestResults()
        val allProgresses = progressManager.getAllTestProgresses()
        
        return BackupData(
            metadata = BackupMetadata(
                version = "1.0",
                createdAt = Date(),
                appVersion = getAppVersion(),
                deviceInfo = getDeviceInfo()
            ),
            testResults = allResults,
            testProgresses = allProgresses
        )
    }
    
    private fun createBackupFile(backupData: BackupData): File {
        val backupDir = getBackupDirectory()
        if (!backupDir.exists()) {
            backupDir.mkdirs()
        }
        
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "$BACKUP_FILE_PREFIX$timestamp$BACKUP_FILE_EXTENSION"
        val backupFile = File(backupDir, fileName)
        
        FileWriter(backupFile).use { writer ->
            gson.toJson(backupData, writer)
        }
        
        return backupFile
    }
    
    private fun createCsvData(): String {
        val allResults = progressManager.getAllTestResults()
        val csvBuilder = StringBuilder()
        
        // CSV Header
        csvBuilder.appendLine("TestID,TestName,Score,AccuracyPercentage,TotalTimeMs,AverageResponseTime,CompletedAt,QuestionCount")
        
        // CSV Data
        allResults.forEach { (testId, results) ->
            val testName = getTestName(testId)
            results.forEach { result ->
                csvBuilder.appendLine(
                    "$testId,$testName,${result.score},${result.accuracyPercentage}," +
                    "${result.totalTimeMs},${result.averageResponseTime}," +
                    "${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(result.completedAt)}," +
                    "${result.questions.size}"
                )
            }
        }
        
        return csvBuilder.toString()
    }
    
    private fun createCsvFile(csvData: String): File {
        val backupDir = getBackupDirectory()
        if (!backupDir.exists()) {
            backupDir.mkdirs()
        }
        
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "leapiq_export_$timestamp.csv"
        val csvFile = File(backupDir, fileName)
        
        csvFile.writeText(csvData)
        return csvFile
    }
    
    private fun getBackupDirectory(): File {
        return File(context.getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS), BACKUP_FOLDER)
    }
    
    private fun isValidBackupData(backupData: BackupData?): Boolean {
        return backupData != null &&
               backupData.metadata.version.isNotEmpty() &&
               backupData.metadata.appVersion.isNotEmpty() &&
               (backupData.testResults.isNotEmpty() || backupData.testProgresses.isNotEmpty())
    }
    
    private fun restoreTestData(backupData: BackupData) {
        // Clear existing data (optional - could be made configurable)
        progressManager.clearAllTestData()
        
        // Restore test results
        backupData.testResults.forEach { (_, results) ->
            results.forEach { result ->
                progressManager.saveTestResult(result)
            }
        }
    }
    
    private fun getTestsIncluded(): List<String> {
        return progressManager.getAllTestResults().keys.toList()
    }
    
    private fun getTestName(testId: String): String {
        return when (testId) {
            "memory_assessment" -> "Memory Assessment"
            "attention_test" -> "Attention Test"
            "processing_speed" -> "Processing Speed"
            "learning_style" -> "Learning Style"
            "stress_response" -> "Stress Response"
            "problem_solving" -> "Problem Solving Style"
            else -> "Unknown Test"
        }
    }
    
    private fun getAppVersion(): String {
        return try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            packageInfo.versionName ?: "Unknown"
        } catch (e: Exception) {
            "Unknown"
        }
    }
    
    private fun getDeviceInfo(): String {
        return "${android.os.Build.MANUFACTURER} ${android.os.Build.MODEL} (Android ${android.os.Build.VERSION.RELEASE})"
    }
}

/**
 * Data classes for backup operations
 */
data class BackupData(
    val metadata: BackupMetadata,
    val testResults: Map<String, List<com.leapiq.braintraining.data.model.TestResult>>,
    val testProgresses: Map<String, TestProgress>
)

data class BackupMetadata(
    val version: String,
    val createdAt: Date,
    val appVersion: String,
    val deviceInfo: String
)

data class BackupInfo(
    val filePath: String,
    val fileName: String,
    val createdAt: Date,
    val fileSize: Long,
    val testsCount: Int,
    val version: String
)

sealed class BackupResult {
    data class Success(
        val filePath: String,
        val fileSize: Long,
        val timestamp: Date,
        val testsIncluded: List<String>
    ) : BackupResult()
    
    data class Error(val message: String) : BackupResult()
}

sealed class RestoreResult {
    data class Success(
        val testsRestored: Int,
        val timestamp: Date
    ) : RestoreResult()
    
    data class Error(val message: String) : RestoreResult()
}

sealed class ExportResult {
    data class Success(
        val filePath: String,
        val fileSize: Long,
        val recordsExported: Int
    ) : ExportResult()
    
    data class Error(val message: String) : ExportResult()
}
