// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentTestsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnMyResults;

  @NonNull
  public final MaterialButton tabAllTests;

  @NonNull
  public final MaterialButton tabCognitive;

  @NonNull
  public final MaterialButton tabPersonality;

  @NonNull
  public final RecyclerView testsRecycler;

  private FragmentTestsBinding(@NonNull LinearLayout rootView, @NonNull MaterialButton btnMyResults,
      @NonNull MaterialButton tabAllTests, @NonNull MaterialButton tabCognitive,
      @NonNull MaterialButton tabPersonality, @NonNull RecyclerView testsRecycler) {
    this.rootView = rootView;
    this.btnMyResults = btnMyResults;
    this.tabAllTests = tabAllTests;
    this.tabCognitive = tabCognitive;
    this.tabPersonality = tabPersonality;
    this.testsRecycler = testsRecycler;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentTestsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentTestsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_tests, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentTestsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_my_results;
      MaterialButton btnMyResults = ViewBindings.findChildViewById(rootView, id);
      if (btnMyResults == null) {
        break missingId;
      }

      id = R.id.tab_all_tests;
      MaterialButton tabAllTests = ViewBindings.findChildViewById(rootView, id);
      if (tabAllTests == null) {
        break missingId;
      }

      id = R.id.tab_cognitive;
      MaterialButton tabCognitive = ViewBindings.findChildViewById(rootView, id);
      if (tabCognitive == null) {
        break missingId;
      }

      id = R.id.tab_personality;
      MaterialButton tabPersonality = ViewBindings.findChildViewById(rootView, id);
      if (tabPersonality == null) {
        break missingId;
      }

      id = R.id.tests_recycler;
      RecyclerView testsRecycler = ViewBindings.findChildViewById(rootView, id);
      if (testsRecycler == null) {
        break missingId;
      }

      return new FragmentTestsBinding((LinearLayout) rootView, btnMyResults, tabAllTests,
          tabCognitive, tabPersonality, testsRecycler);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
