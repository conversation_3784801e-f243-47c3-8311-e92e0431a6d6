// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.HorizontalScrollView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class FragmentGamesBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final HorizontalScrollView categoryScrollView;

  @NonNull
  public final RecyclerView gamesRecycler;

  @NonNull
  public final MaterialButton tabAll;

  @NonNull
  public final MaterialButton tabAttention;

  @NonNull
  public final MaterialButton tabLanguage;

  @NonNull
  public final MaterialButton tabLogic;

  @NonNull
  public final MaterialButton tabMath;

  @NonNull
  public final MaterialButton tabMemory;

  private FragmentGamesBinding(@NonNull LinearLayout rootView,
      @NonNull HorizontalScrollView categoryScrollView, @NonNull RecyclerView gamesRecycler,
      @NonNull MaterialButton tabAll, @NonNull MaterialButton tabAttention,
      @NonNull MaterialButton tabLanguage, @NonNull MaterialButton tabLogic,
      @NonNull MaterialButton tabMath, @NonNull MaterialButton tabMemory) {
    this.rootView = rootView;
    this.categoryScrollView = categoryScrollView;
    this.gamesRecycler = gamesRecycler;
    this.tabAll = tabAll;
    this.tabAttention = tabAttention;
    this.tabLanguage = tabLanguage;
    this.tabLogic = tabLogic;
    this.tabMath = tabMath;
    this.tabMemory = tabMemory;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static FragmentGamesBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static FragmentGamesBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.fragment_games, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static FragmentGamesBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.category_scroll_view;
      HorizontalScrollView categoryScrollView = ViewBindings.findChildViewById(rootView, id);
      if (categoryScrollView == null) {
        break missingId;
      }

      id = R.id.games_recycler;
      RecyclerView gamesRecycler = ViewBindings.findChildViewById(rootView, id);
      if (gamesRecycler == null) {
        break missingId;
      }

      id = R.id.tab_all;
      MaterialButton tabAll = ViewBindings.findChildViewById(rootView, id);
      if (tabAll == null) {
        break missingId;
      }

      id = R.id.tab_attention;
      MaterialButton tabAttention = ViewBindings.findChildViewById(rootView, id);
      if (tabAttention == null) {
        break missingId;
      }

      id = R.id.tab_language;
      MaterialButton tabLanguage = ViewBindings.findChildViewById(rootView, id);
      if (tabLanguage == null) {
        break missingId;
      }

      id = R.id.tab_logic;
      MaterialButton tabLogic = ViewBindings.findChildViewById(rootView, id);
      if (tabLogic == null) {
        break missingId;
      }

      id = R.id.tab_math;
      MaterialButton tabMath = ViewBindings.findChildViewById(rootView, id);
      if (tabMath == null) {
        break missingId;
      }

      id = R.id.tab_memory;
      MaterialButton tabMemory = ViewBindings.findChildViewById(rootView, id);
      if (tabMemory == null) {
        break missingId;
      }

      return new FragmentGamesBinding((LinearLayout) rootView, categoryScrollView, gamesRecycler,
          tabAll, tabAttention, tabLanguage, tabLogic, tabMath, tabMemory);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
