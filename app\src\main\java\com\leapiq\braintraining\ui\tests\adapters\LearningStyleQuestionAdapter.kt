package com.leapiq.braintraining.ui.tests.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.databinding.ItemLearningStyleOptionBinding
import com.leapiq.braintraining.ui.tests.LearningStyleActivity
import com.leapiq.braintraining.ui.tests.LearningPreference
import com.leapiq.braintraining.ui.tests.LearningStyleOption
import com.leapiq.braintraining.ui.tests.LearningStyleQuestion

/**
 * Adapter for learning style question options
 */
class LearningStyleQuestionAdapter(
    private val onOptionSelected: (Int, Boolean) -> Unit
) : RecyclerView.Adapter<LearningStyleQuestionAdapter.OptionViewHolder>() {
    
    private var currentQuestion: LearningStyleQuestion? = null

    fun updateQuestion(question: LearningStyleQuestion) {
        currentQuestion = question
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OptionViewHolder {
        val binding = ItemLearningStyleOptionBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return OptionViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: OptionViewHolder, position: Int) {
        currentQuestion?.let { question ->
            if (position < question.options.size) {
                holder.bind(question.options[position], position)
            }
        }
    }
    
    override fun getItemCount(): Int {
        return currentQuestion?.options?.size ?: 0
    }
    
    inner class OptionViewHolder(
        private val binding: ItemLearningStyleOptionBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(option: LearningStyleOption, position: Int) {
            binding.apply {
                optionText.text = option.text
                optionCheckbox.isChecked = option.isSelected
                
                // Set click listeners
                root.setOnClickListener {
                    val newState = !optionCheckbox.isChecked
                    optionCheckbox.isChecked = newState
                    onOptionSelected(position, newState)
                }
                
                optionCheckbox.setOnCheckedChangeListener { _, isChecked ->
                    onOptionSelected(position, isChecked)
                }
                
                // Add preference indicator
                val preferenceIndicator = when (option.preference) {
                    LearningPreference.VISUAL -> "👁️"
                    LearningPreference.AUDITORY -> "👂"
                    LearningPreference.READ_WRITE -> "📝"
                    LearningPreference.KINESTHETIC -> "✋"
                }
                preferenceIcon.text = preferenceIndicator
                
                // Don't show preference indicators during test (only for debugging)
                // preferenceIcon.text = preferenceIndicator
                // preferenceIcon.visibility = View.VISIBLE
            }
        }
    }
}
