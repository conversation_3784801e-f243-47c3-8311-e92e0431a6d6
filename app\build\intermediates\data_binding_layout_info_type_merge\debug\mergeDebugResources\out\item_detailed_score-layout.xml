<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_detailed_score" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\item_detailed_score.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/item_detailed_score_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="41" endOffset="14"/></Target><Target id="@+id/categoryText" view="TextView"><Expressions/><location startLine="13" startOffset="8" endLine="20" endOffset="38"/></Target><Target id="@+id/scoreText" view="TextView"><Expressions/><location startLine="22" startOffset="8" endLine="28" endOffset="38"/></Target><Target id="@+id/scoreProgress" view="ProgressBar"><Expressions/><location startLine="32" startOffset="4" endLine="39" endOffset="31"/></Target></Targets></Layout>