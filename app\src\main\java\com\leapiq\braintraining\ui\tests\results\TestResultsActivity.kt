package com.leapiq.braintraining.ui.tests.results

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityTestResultsBinding
import com.leapiq.braintraining.data.model.TestResult
import com.leapiq.braintraining.data.model.TestType
import com.leapiq.braintraining.MainActivity
import com.google.gson.Gson

/**
 * Activity to display detailed test results with insights and recommendations
 */
class TestResultsActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityTestResultsBinding
    private lateinit var testResult: TestResult
    
    companion object {
        const val EXTRA_TEST_RESULT = "test_result"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTestResultsBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Get test result from intent
        val testResultJson = intent.getStringExtra(EXTRA_TEST_RESULT)
        testResult = if (testResultJson != null) {
            try {
                Gson().fromJson(testResultJson, TestResult::class.java)
            } catch (e: Exception) {
                finish()
                return
            }
        } else {
            finish()
            return
        }
        
        setupUI()
        displayResults()
    }
    
    private fun setupUI() {
        binding.apply {
            // Setup toolbar
            toolbar.setNavigationOnClickListener {
                navigateToTests()
            }
            
            // Setup buttons
            btnBackToTests.setOnClickListener {
                navigateToTests()
            }
            
            btnRetakeTest.setOnClickListener {
                retakeTest()
            }
            
            btnViewProgress.setOnClickListener {
                viewProgress()
            }
        }
    }
    
    private fun displayResults() {
        binding.apply {
            // Test name and type
            testName.text = getTestName(testResult.testId)
            testType.text = testResult.testType.name.lowercase().replaceFirstChar { it.uppercase() }
            
            // Main score
            scoreValue.text = "${testResult.score}"
            scoreLabel.text = if (testResult.testType == TestType.COGNITIVE) "Accuracy" else "Score"
            
            // Performance metrics
            displayPerformanceMetrics()
            
            // Detailed scores (for multi-dimensional tests)
            displayDetailedScores()
            
            // Insights
            displayInsights()
            
            // Recommendations
            displayRecommendations()
            
            // Test statistics
            displayTestStatistics()
        }
    }
    
    private fun displayPerformanceMetrics() {
        binding.apply {
            // Time taken
            val minutes = (testResult.totalTimeMs / 60000).toInt()
            val seconds = ((testResult.totalTimeMs % 60000) / 1000).toInt()
            timeTaken.text = "${minutes}m ${seconds}s"
            
            // Questions answered
            questionsAnswered.text = "${testResult.questions.size}"
            
            // Average response time
            val avgResponseSeconds = (testResult.averageResponseTime / 1000.0)
            avgResponseTime.text = String.format("%.1fs", avgResponseSeconds)
            
            // Accuracy (for cognitive tests)
            if (testResult.testType == TestType.COGNITIVE) {
                accuracyContainer.visibility = android.view.View.VISIBLE
                accuracyValue.text = "${testResult.accuracyPercentage}%"
            } else {
                accuracyContainer.visibility = android.view.View.GONE
            }
        }
    }
    
    private fun displayDetailedScores() {
        if (testResult.detailedScores.isNotEmpty()) {
            binding.detailedScoresContainer.visibility = android.view.View.VISIBLE
            
            // Setup RecyclerView for detailed scores
            // TODO: Implement DetailedScoresAdapter or use alternative display
            binding.detailedScoresRecycler.visibility = android.view.View.GONE
        } else {
            binding.detailedScoresContainer.visibility = android.view.View.GONE
        }
    }
    
    private fun displayInsights() {
        if (testResult.insights.isNotEmpty()) {
            binding.insightsContainer.visibility = android.view.View.VISIBLE
            
            val insightsText = testResult.insights.joinToString("\n\n") { "• $it" }
            binding.insightsText.text = insightsText
        } else {
            binding.insightsContainer.visibility = android.view.View.GONE
        }
    }
    
    private fun displayRecommendations() {
        if (testResult.recommendations.isNotEmpty()) {
            binding.recommendationsContainer.visibility = android.view.View.VISIBLE
            
            val recommendationsText = testResult.recommendations.joinToString("\n\n") { "• $it" }
            binding.recommendationsText.text = recommendationsText
        } else {
            binding.recommendationsContainer.visibility = android.view.View.GONE
        }
    }
    
    private fun displayTestStatistics() {
        binding.apply {
            // Completion date
            val dateFormat = java.text.SimpleDateFormat("MMM dd, yyyy 'at' HH:mm", java.util.Locale.getDefault())
            completionDate.text = dateFormat.format(testResult.completedAt)
            
            // Performance level
            val performanceLevel = when (testResult.score) {
                in 90..100 -> "Excellent"
                in 80..89 -> "Very Good"
                in 70..79 -> "Good"
                in 60..69 -> "Average"
                in 50..59 -> "Below Average"
                else -> "Needs Improvement"
            }
            performanceLevelText.text = performanceLevel
            
            // Set performance level color
            val colorRes = when (testResult.score) {
                in 90..100 -> R.color.success_green
                in 80..89 -> R.color.primary_light_blue
                in 70..79 -> R.color.warning_orange
                else -> R.color.error_red
            }
            performanceLevelText.setTextColor(getColor(colorRes))
        }
    }
    
    private fun getTestName(testId: String): String {
        return when (testId) {
            "cognitive_1" -> "Memory Assessment"
            "cognitive_2" -> "Attention Test"
            "cognitive_3" -> "Processing Speed"
            "personality_1" -> "Learning Style"
            "personality_2" -> "Stress Response"
            "personality_3" -> "Problem Solving Style"
            else -> "Unknown Test"
        }
    }
    
    private fun navigateToTests() {
        val intent = Intent(this, MainActivity::class.java).apply {
            putExtra("navigate_to", "tests")
            flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_SINGLE_TOP
        }
        startActivity(intent)
        finish()
    }
    
    private fun retakeTest() {
        // Navigate back to the test
        finish()
        // The test activity will be started by the TestsFragment
    }
    
    private fun viewProgress() {
        // Navigate to test progress view
        val intent = Intent(this, TestProgressActivity::class.java).apply {
            putExtra(TestProgressActivity.EXTRA_TEST_ID, testResult.testId)
        }
        startActivity(intent)
    }
}
