// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityTestQuestionBaseBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final MaterialButton btnNext;

  @NonNull
  public final MaterialButton btnPrevious;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final TextView progressText;

  @NonNull
  public final LinearLayout questionContent;

  @NonNull
  public final TextView questionText;

  @NonNull
  public final Toolbar toolbar;

  private ActivityTestQuestionBaseBinding(@NonNull LinearLayout rootView,
      @NonNull MaterialButton btnNext, @NonNull MaterialButton btnPrevious,
      @NonNull ProgressBar progressBar, @NonNull TextView progressText,
      @NonNull LinearLayout questionContent, @NonNull TextView questionText,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.btnNext = btnNext;
    this.btnPrevious = btnPrevious;
    this.progressBar = progressBar;
    this.progressText = progressText;
    this.questionContent = questionContent;
    this.questionText = questionText;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityTestQuestionBaseBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityTestQuestionBaseBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_test_question_base, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityTestQuestionBaseBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.btn_next;
      MaterialButton btnNext = ViewBindings.findChildViewById(rootView, id);
      if (btnNext == null) {
        break missingId;
      }

      id = R.id.btn_previous;
      MaterialButton btnPrevious = ViewBindings.findChildViewById(rootView, id);
      if (btnPrevious == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.progress_text;
      TextView progressText = ViewBindings.findChildViewById(rootView, id);
      if (progressText == null) {
        break missingId;
      }

      id = R.id.question_content;
      LinearLayout questionContent = ViewBindings.findChildViewById(rootView, id);
      if (questionContent == null) {
        break missingId;
      }

      id = R.id.question_text;
      TextView questionText = ViewBindings.findChildViewById(rootView, id);
      if (questionText == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityTestQuestionBaseBinding((LinearLayout) rootView, btnNext, btnPrevious,
          progressBar, progressText, questionContent, questionText, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
