<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical">

    <CheckBox
        android:id="@+id/option_checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:buttonTint="@color/primary_light_blue" />

    <TextView
        android:id="@+id/option_text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="Podcasts and videos where I can listen to experts"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:lineSpacingExtra="2dp" />

    <TextView
        android:id="@+id/preference_icon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="👂"
        android:textSize="20sp"
        android:visibility="gone" />

</LinearLayout>
