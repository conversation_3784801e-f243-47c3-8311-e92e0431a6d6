<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="item_test_card" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\item_test_card.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.cardview.widget.CardView"><Targets><Target tag="layout/item_test_card_0" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="1" startOffset="0" endLine="115" endOffset="35"/></Target><Target id="@+id/test_image" view="ImageView"><Expressions/><location startLine="23" startOffset="12" endLine="29" endOffset="48"/></Target><Target id="@+id/duration_badge" view="TextView"><Expressions/><location startLine="32" startOffset="12" endLine="47" endOffset="36"/></Target><Target id="@+id/completion_icon" view="ImageView"><Expressions/><location startLine="50" startOffset="12" endLine="61" endOffset="44"/></Target><Target id="@+id/test_name" view="TextView"><Expressions/><location startLine="73" startOffset="12" endLine="82" endOffset="48"/></Target><Target id="@+id/test_category" view="TextView"><Expressions/><location startLine="85" startOffset="12" endLine="94" endOffset="40"/></Target><Target id="@+id/last_score" view="TextView"><Expressions/><location startLine="97" startOffset="12" endLine="109" endOffset="44"/></Target></Targets></Layout>