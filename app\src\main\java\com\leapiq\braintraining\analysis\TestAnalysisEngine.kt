package com.leapiq.braintraining.analysis

import com.leapiq.braintraining.data.TestResultsRepository
import com.leapiq.braintraining.data.model.TestResult
import com.leapiq.braintraining.data.model.TestType
import kotlin.math.abs
import kotlin.math.roundToInt

/**
 * Advanced analysis engine for test results
 * Provides detailed score explanations, insights, and recommendations
 */
class TestAnalysisEngine(private val repository: TestResultsRepository) {
    
    /**
     * Generate comprehensive analysis for a test result
     */
    fun analyzeTestResult(testResult: TestResult): DetailedAnalysis {
        return DetailedAnalysis(
            testId = testResult.testId,
            overallPerformance = analyzeOverallPerformance(testResult),
            cognitiveProfile = if (testResult.testType == TestType.COGNITIVE) 
                analyzeCognitiveProfile(testResult) else null,
            personalityProfile = if (testResult.testType == TestType.PERSONALITY) 
                analyzePersonalityProfile(testResult) else null,
            strengthsAndWeaknesses = analyzeStrengthsAndWeaknesses(testResult),
            improvementRecommendations = generateImprovementRecommendations(testResult),
            comparativeAnalysis = generateComparativeAnalysis(testResult),
            nextSteps = generateNextSteps(testResult)
        )
    }
    
    /**
     * Analyze overall performance metrics
     */
    private fun analyzeOverallPerformance(testResult: TestResult): OverallPerformance {
        val scoreCategory = when (testResult.score) {
            in 90..100 -> PerformanceCategory.EXCELLENT
            in 80..89 -> PerformanceCategory.VERY_GOOD
            in 70..79 -> PerformanceCategory.GOOD
            in 60..69 -> PerformanceCategory.AVERAGE
            in 50..59 -> PerformanceCategory.BELOW_AVERAGE
            else -> PerformanceCategory.NEEDS_IMPROVEMENT
        }
        
        val timeEfficiency = analyzeTimeEfficiency(testResult)
        val consistencyScore = analyzeConsistency(testResult)
        
        return OverallPerformance(
            score = testResult.score,
            scoreCategory = scoreCategory,
            percentileRank = calculatePercentileRank(testResult),
            timeEfficiency = timeEfficiency,
            consistencyScore = consistencyScore,
            keyInsights = generateKeyInsights(testResult, scoreCategory, timeEfficiency)
        )
    }
    
    /**
     * Analyze cognitive-specific metrics
     */
    private fun analyzeCognitiveProfile(testResult: TestResult): CognitiveProfile {
        return when (testResult.testId) {
            "cognitive_1" -> analyzeMemoryProfile(testResult)
            "cognitive_2" -> analyzeAttentionProfile(testResult)
            "cognitive_3" -> analyzeProcessingSpeedProfile(testResult)
            else -> CognitiveProfile(
                primaryAbility = "General Cognitive",
                abilityScore = testResult.score.toDouble(),
                subAbilities = emptyMap(),
                cognitiveStrengths = emptyList(),
                cognitiveWeaknesses = emptyList()
            )
        }
    }
    
    /**
     * Analyze personality-specific metrics
     */
    private fun analyzePersonalityProfile(testResult: TestResult): PersonalityProfile {
        return when (testResult.testId) {
            "personality_1" -> analyzeLearningStyleProfile(testResult)
            "personality_2" -> analyzeStressResponseProfile(testResult)
            "personality_3" -> analyzeProblemSolvingProfile(testResult)
            else -> PersonalityProfile(
                primaryTrait = "General Personality",
                traitScore = testResult.score.toDouble(),
                subTraits = emptyMap(),
                personalityStrengths = emptyList(),
                developmentAreas = emptyList()
            )
        }
    }
    
    /**
     * Analyze memory-specific performance
     */
    private fun analyzeMemoryProfile(testResult: TestResult): CognitiveProfile {
        val subAbilities = testResult.detailedScores.ifEmpty {
            mapOf(
                "Working Memory" to (testResult.score * 0.9),
                "Short-term Memory" to (testResult.score * 1.1),
                "Long-term Memory" to (testResult.score * 1.0),
                "Spatial Memory" to (testResult.score * 0.95)
            )
        }
        
        val strengths = subAbilities.filter { it.value >= 80 }.keys.toList()
        val weaknesses = subAbilities.filter { it.value < 60 }.keys.toList()
        
        return CognitiveProfile(
            primaryAbility = "Memory",
            abilityScore = testResult.score.toDouble(),
            subAbilities = subAbilities,
            cognitiveStrengths = strengths,
            cognitiveWeaknesses = weaknesses
        )
    }
    
    /**
     * Analyze attention-specific performance
     */
    private fun analyzeAttentionProfile(testResult: TestResult): CognitiveProfile {
        val subAbilities = testResult.detailedScores.ifEmpty {
            mapOf(
                "Sustained Attention" to (testResult.score * 1.0),
                "Selective Attention" to (testResult.score * 0.95),
                "Vigilance" to (testResult.score * 1.05),
                "Distractor Resistance" to (testResult.score * 0.9)
            )
        }
        
        val strengths = subAbilities.filter { it.value >= 80 }.keys.toList()
        val weaknesses = subAbilities.filter { it.value < 60 }.keys.toList()
        
        return CognitiveProfile(
            primaryAbility = "Attention",
            abilityScore = testResult.score.toDouble(),
            subAbilities = subAbilities,
            cognitiveStrengths = strengths,
            cognitiveWeaknesses = weaknesses
        )
    }
    
    /**
     * Analyze processing speed-specific performance
     */
    private fun analyzeProcessingSpeedProfile(testResult: TestResult): CognitiveProfile {
        val subAbilities = testResult.detailedScores.ifEmpty {
            mapOf(
                "Simple Reaction Time" to (testResult.score * 1.1),
                "Choice Reaction Time" to (testResult.score * 0.9),
                "Symbol Coding Speed" to (testResult.score * 1.0),
                "Decision Speed" to (testResult.score * 0.95)
            )
        }
        
        val strengths = subAbilities.filter { it.value >= 80 }.keys.toList()
        val weaknesses = subAbilities.filter { it.value < 60 }.keys.toList()
        
        return CognitiveProfile(
            primaryAbility = "Processing Speed",
            abilityScore = testResult.score.toDouble(),
            subAbilities = subAbilities,
            cognitiveStrengths = strengths,
            cognitiveWeaknesses = weaknesses
        )
    }
    
    /**
     * Analyze learning style profile
     */
    private fun analyzeLearningStyleProfile(testResult: TestResult): PersonalityProfile {
        val subTraits = testResult.detailedScores.ifEmpty {
            mapOf(
                "Visual Learning" to 25.0,
                "Auditory Learning" to 25.0,
                "Read/Write Learning" to 25.0,
                "Kinesthetic Learning" to 25.0
            )
        }
        
        val dominantStyle = subTraits.maxByOrNull { it.value }?.key ?: "Balanced"
        val strengths = subTraits.filter { it.value >= 30 }.keys.toList()
        val developmentAreas = subTraits.filter { it.value < 20 }.keys.toList()
        
        return PersonalityProfile(
            primaryTrait = "Learning Style: $dominantStyle",
            traitScore = subTraits.values.maxOrNull() ?: 0.0,
            subTraits = subTraits,
            personalityStrengths = strengths,
            developmentAreas = developmentAreas
        )
    }
    
    /**
     * Analyze stress response profile
     */
    private fun analyzeStressResponseProfile(testResult: TestResult): PersonalityProfile {
        val subTraits = testResult.detailedScores.ifEmpty {
            mapOf(
                "Stress Resilience" to (testResult.score * 1.0),
                "Coping Strategies" to (testResult.score * 0.95),
                "Pressure Handling" to (testResult.score * 1.05),
                "Emotional Regulation" to (testResult.score * 0.9)
            )
        }
        
        val strengths = subTraits.filter { it.value >= 70 }.keys.toList()
        val developmentAreas = subTraits.filter { it.value < 50 }.keys.toList()
        
        return PersonalityProfile(
            primaryTrait = "Stress Response",
            traitScore = testResult.score.toDouble(),
            subTraits = subTraits,
            personalityStrengths = strengths,
            developmentAreas = developmentAreas
        )
    }
    
    /**
     * Analyze problem solving profile
     */
    private fun analyzeProblemSolvingProfile(testResult: TestResult): PersonalityProfile {
        val subTraits = testResult.detailedScores.ifEmpty {
            mapOf(
                "Analytical Thinking" to (testResult.score * 0.9),
                "Intuitive Thinking" to (testResult.score * 1.1),
                "Systematic Approach" to (testResult.score * 1.0),
                "Creative Thinking" to (testResult.score * 0.95)
            )
        }
        
        val dominantStyle = subTraits.maxByOrNull { it.value }?.key ?: "Balanced"
        val strengths = subTraits.filter { it.value >= 70 }.keys.toList()
        val developmentAreas = subTraits.filter { it.value < 50 }.keys.toList()
        
        return PersonalityProfile(
            primaryTrait = "Problem Solving: $dominantStyle",
            traitScore = subTraits.values.maxOrNull() ?: 0.0,
            subTraits = subTraits,
            personalityStrengths = strengths,
            developmentAreas = developmentAreas
        )
    }
    
    /**
     * Analyze strengths and weaknesses
     */
    private fun analyzeStrengthsAndWeaknesses(testResult: TestResult): StrengthsWeaknessesAnalysis {
        val strengths = mutableListOf<Strength>()
        val weaknesses = mutableListOf<Weakness>()
        
        // Analyze based on detailed scores
        testResult.detailedScores.forEach { (area, score) ->
            when {
                score >= 80 -> strengths.add(
                    Strength(
                        area = area,
                        score = score,
                        description = "Excellent performance in $area",
                        impact = StrengthImpact.HIGH
                    )
                )
                score < 60 -> weaknesses.add(
                    Weakness(
                        area = area,
                        score = score,
                        description = "Room for improvement in $area",
                        priority = WeaknessPriority.HIGH,
                        improvementPotential = calculateImprovementPotential(score)
                    )
                )
            }
        }
        
        // Analyze response patterns
        val responsePatterns = analyzeResponsePatterns(testResult)
        
        return StrengthsWeaknessesAnalysis(
            strengths = strengths,
            weaknesses = weaknesses,
            responsePatterns = responsePatterns,
            overallBalance = calculateOverallBalance(strengths, weaknesses)
        )
    }
    
    /**
     * Generate improvement recommendations
     */
    private fun generateImprovementRecommendations(testResult: TestResult): List<ImprovementRecommendation> {
        val recommendations = mutableListOf<ImprovementRecommendation>()
        
        // Add test-specific recommendations
        recommendations.addAll(getTestSpecificRecommendations(testResult))
        
        // Add general recommendations based on performance
        recommendations.addAll(getGeneralRecommendations(testResult))
        
        return recommendations.sortedByDescending { it.priority.ordinal }
    }
    
    /**
     * Generate comparative analysis
     */
    private fun generateComparativeAnalysis(testResult: TestResult): ComparativeAnalysisResult {
        // Compare with user's previous results
        val personalComparison = generatePersonalComparison(testResult)
        
        // Compare with population norms (simulated)
        val populationComparison = generatePopulationComparison(testResult)
        
        return ComparativeAnalysisResult(
            personalComparison = personalComparison,
            populationComparison = populationComparison,
            improvementTrend = calculateImprovementTrend(testResult)
        )
    }
    
    /**
     * Generate next steps
     */
    private fun generateNextSteps(testResult: TestResult): List<NextStep> {
        val nextSteps = mutableListOf<NextStep>()
        
        // Immediate actions
        nextSteps.add(NextStep(
            category = NextStepCategory.IMMEDIATE,
            action = "Review your detailed results and insights",
            timeframe = "Today",
            priority = NextStepPriority.HIGH
        ))
        
        // Short-term goals
        if (testResult.score < 70) {
            nextSteps.add(NextStep(
                category = NextStepCategory.SHORT_TERM,
                action = "Practice specific weak areas identified in the analysis",
                timeframe = "This week",
                priority = NextStepPriority.HIGH
            ))
        }
        
        // Long-term development
        nextSteps.add(NextStep(
            category = NextStepCategory.LONG_TERM,
            action = "Retake this test in 2-4 weeks to track improvement",
            timeframe = "2-4 weeks",
            priority = NextStepPriority.MEDIUM
        ))
        
        return nextSteps
    }
    
    // Helper methods
    private fun analyzeTimeEfficiency(testResult: TestResult): TimeEfficiency {
        val avgResponseTime = testResult.averageResponseTime
        val efficiency = when {
            avgResponseTime < 1000 -> TimeEfficiencyLevel.EXCELLENT
            avgResponseTime < 2000 -> TimeEfficiencyLevel.GOOD
            avgResponseTime < 3000 -> TimeEfficiencyLevel.AVERAGE
            else -> TimeEfficiencyLevel.SLOW
        }
        
        return TimeEfficiency(
            averageResponseTime = avgResponseTime,
            level = efficiency,
            description = getTimeEfficiencyDescription(efficiency)
        )
    }
    
    private fun analyzeConsistency(testResult: TestResult): ConsistencyScore {
        val responseTimes = testResult.questions.map { it.responseTime }
        if (responseTimes.size < 2) {
            return ConsistencyScore(score = 100.0, level = ConsistencyLevel.HIGH, description = "Insufficient data")
        }
        
        val mean = responseTimes.average()
        val variance = responseTimes.map { (it - mean) * (it - mean) }.average()
        val standardDeviation = kotlin.math.sqrt(variance)
        val coefficientOfVariation = (standardDeviation / mean) * 100
        
        val level = when {
            coefficientOfVariation < 20 -> ConsistencyLevel.HIGH
            coefficientOfVariation < 40 -> ConsistencyLevel.MEDIUM
            else -> ConsistencyLevel.LOW
        }
        
        val score = maxOf(0.0, 100.0 - coefficientOfVariation)
        
        return ConsistencyScore(
            score = score,
            level = level,
            description = getConsistencyDescription(level)
        )
    }
    
    private fun calculatePercentileRank(testResult: TestResult): Int {
        // Simplified percentile calculation
        // In a real implementation, this would compare against a database of results
        return when (testResult.score) {
            in 95..100 -> 95
            in 90..94 -> 85
            in 80..89 -> 70
            in 70..79 -> 50
            in 60..69 -> 30
            in 50..59 -> 15
            else -> 5
        }
    }
    
    private fun generateKeyInsights(testResult: TestResult, category: PerformanceCategory, timeEfficiency: TimeEfficiency): List<String> {
        val insights = mutableListOf<String>()
        
        insights.add("Your performance falls in the ${category.name.lowercase().replace('_', ' ')} range")
        
        when (timeEfficiency.level) {
            TimeEfficiencyLevel.EXCELLENT -> insights.add("You demonstrate excellent response speed")
            TimeEfficiencyLevel.GOOD -> insights.add("Your response time is good")
            TimeEfficiencyLevel.AVERAGE -> insights.add("Your response time is average")
            TimeEfficiencyLevel.SLOW -> insights.add("Consider working on response speed")
        }
        
        if (testResult.accuracyPercentage > 90) {
            insights.add("You show high accuracy in your responses")
        } else if (testResult.accuracyPercentage < 70) {
            insights.add("Focus on improving accuracy over speed")
        }
        
        return insights
    }
    
    private fun analyzeResponsePatterns(testResult: TestResult): List<ResponsePattern> {
        val patterns = mutableListOf<ResponsePattern>()
        
        // Analyze response time patterns
        val responseTimes = testResult.questions.map { it.responseTime }
        if (responseTimes.isNotEmpty()) {
            val trend = calculateResponseTimeTrend(responseTimes)
            patterns.add(ResponsePattern(
                type = ResponsePatternType.RESPONSE_TIME_TREND,
                description = trend,
                significance = ResponsePatternSignificance.MEDIUM
            ))
        }
        
        return patterns
    }
    
    private fun calculateResponseTimeTrend(responseTimes: List<Long>): String {
        if (responseTimes.size < 3) return "Insufficient data for trend analysis"
        
        val firstThird = responseTimes.take(responseTimes.size / 3).average()
        val lastThird = responseTimes.takeLast(responseTimes.size / 3).average()
        
        return when {
            lastThird < firstThird * 0.9 -> "Response times improved throughout the test"
            lastThird > firstThird * 1.1 -> "Response times slowed down during the test"
            else -> "Response times remained consistent throughout the test"
        }
    }
    
    private fun calculateOverallBalance(strengths: List<Strength>, weaknesses: List<Weakness>): OverallBalance {
        val strengthScore = strengths.sumOf { it.score } / maxOf(1, strengths.size)
        val weaknessScore = weaknesses.sumOf { it.score } / maxOf(1, weaknesses.size)

        return when {
            strengthScore > weaknessScore + 20 -> OverallBalance.STRENGTH_DOMINANT
            weaknessScore > strengthScore + 20 -> OverallBalance.WEAKNESS_DOMINANT
            strengths.size > weaknesses.size * 2 -> OverallBalance.STRENGTH_DOMINANT
            weaknesses.size > strengths.size * 2 -> OverallBalance.WEAKNESS_DOMINANT
            else -> OverallBalance.BALANCED
        }
    }
    
    private fun calculateImprovementPotential(currentScore: Double): ImprovementPotential {
        return when {
            currentScore < 40 -> ImprovementPotential.HIGH
            currentScore < 60 -> ImprovementPotential.MEDIUM
            else -> ImprovementPotential.LOW
        }
    }
    
    private fun getTestSpecificRecommendations(testResult: TestResult): List<ImprovementRecommendation> {
        // Return test-specific recommendations from the test result
        return testResult.recommendations.map { recommendation ->
            ImprovementRecommendation(
                category = RecommendationCategory.SKILL_SPECIFIC,
                title = "Test-Specific Improvement",
                description = recommendation,
                actionSteps = listOf(recommendation),
                timeframe = "1-2 weeks",
                priority = RecommendationPriority.MEDIUM,
                expectedImpact = ImprovementImpact.MEDIUM
            )
        }
    }
    
    private fun getGeneralRecommendations(testResult: TestResult): List<ImprovementRecommendation> {
        val recommendations = mutableListOf<ImprovementRecommendation>()
        
        if (testResult.score < 60) {
            recommendations.add(ImprovementRecommendation(
                category = RecommendationCategory.GENERAL,
                title = "Focus on Fundamentals",
                description = "Build a strong foundation in the core skills tested",
                actionSteps = listOf(
                    "Review test instructions carefully",
                    "Practice regularly with similar exercises",
                    "Focus on accuracy before speed"
                ),
                timeframe = "2-4 weeks",
                priority = RecommendationPriority.HIGH,
                expectedImpact = ImprovementImpact.HIGH
            ))
        }
        
        return recommendations
    }
    
    private fun generatePersonalComparison(testResult: TestResult): PersonalComparison {
        // Get user's previous results for this test
        val previousResults = repository.getTestResults(testResult.testId)
            .filter { it.completedAt.before(testResult.completedAt) }
            .sortedByDescending { it.completedAt }

        return if (previousResults.isNotEmpty()) {
            val previousScore = previousResults.first().score
            val improvement = testResult.score - previousScore.toInt()
            val trend = when {
                improvement > 5 -> "Improving"
                improvement < -5 -> "Declining"
                else -> "Stable"
            }

            PersonalComparison(
                previousScore = previousScore,
                improvement = improvement.toDouble(),
                trend = trend
            )
        } else {
            PersonalComparison(
                previousScore = null,
                improvement = 0.0,
                trend = "First attempt"
            )
        }
    }
    
    private fun generatePopulationComparison(testResult: TestResult): PopulationComparison {
        val percentile = calculatePercentileRank(testResult)
        val comparisonGroup = when (testResult.testType) {
            TestType.COGNITIVE -> "Cognitive Test Takers"
            TestType.PERSONALITY -> "Personality Assessment Participants"
        }

        val description = when {
            percentile >= 90 -> "Exceptional performance - top 10%"
            percentile >= 75 -> "Above average performance"
            percentile >= 50 -> "Average performance"
            percentile >= 25 -> "Below average performance"
            else -> "Significant room for improvement"
        }

        return PopulationComparison(
            percentileRank = percentile,
            comparisonGroup = comparisonGroup,
            description = description
        )
    }
    
    private fun calculateImprovementTrend(testResult: TestResult): ImprovementTrend {
        val previousResults = repository.getTestResults(testResult.testId)
            .filter { it.completedAt.before(testResult.completedAt) }
            .sortedBy { it.completedAt }

        return if (previousResults.size >= 2) {
            val recentScores = previousResults.takeLast(3).map { it.score.toDouble() }
            val earlierScores = previousResults.dropLast(3).takeLast(3).map { it.score.toDouble() }

            val recentAvg = recentScores.average()
            val earlierAvg = if (earlierScores.isNotEmpty()) earlierScores.average() else recentAvg

            val improvement = recentAvg - earlierAvg
            val magnitude = kotlin.math.abs(improvement.toDouble())

            val direction = when {
                improvement > 5 -> TrendDirection.IMPROVING
                improvement < -5 -> TrendDirection.DECLINING
                else -> TrendDirection.STABLE
            }

            val description = when (direction) {
                TrendDirection.IMPROVING -> "Your scores are improving over time (+${improvement.toInt()}%)"
                TrendDirection.DECLINING -> "Your scores have declined recently (${improvement.toInt()}%)"
                TrendDirection.STABLE -> "Your performance is consistent"
            }

            ImprovementTrend(
                direction = direction,
                magnitude = magnitude,
                description = description
            )
        } else {
            ImprovementTrend(
                direction = TrendDirection.STABLE,
                magnitude = 0.0,
                description = "Not enough data for trend analysis"
            )
        }
    }
    
    private fun getTimeEfficiencyDescription(level: TimeEfficiencyLevel): String {
        return when (level) {
            TimeEfficiencyLevel.EXCELLENT -> "You respond very quickly while maintaining accuracy"
            TimeEfficiencyLevel.GOOD -> "You have good response speed"
            TimeEfficiencyLevel.AVERAGE -> "Your response speed is typical"
            TimeEfficiencyLevel.SLOW -> "Consider practicing to improve response speed"
        }
    }
    
    private fun getConsistencyDescription(level: ConsistencyLevel): String {
        return when (level) {
            ConsistencyLevel.HIGH -> "You maintain consistent performance throughout"
            ConsistencyLevel.MEDIUM -> "Your performance shows moderate consistency"
            ConsistencyLevel.LOW -> "Your performance varies significantly across questions"
        }
    }
}

/**
 * Data classes for detailed test analysis
 */
data class DetailedAnalysis(
    val testId: String,
    val overallPerformance: OverallPerformance,
    val cognitiveProfile: CognitiveProfile?,
    val personalityProfile: PersonalityProfile?,
    val strengthsAndWeaknesses: StrengthsWeaknessesAnalysis,
    val improvementRecommendations: List<ImprovementRecommendation>,
    val comparativeAnalysis: ComparativeAnalysisResult,
    val nextSteps: List<NextStep>
)

data class OverallPerformance(
    val score: Int,
    val scoreCategory: PerformanceCategory,
    val percentileRank: Int,
    val timeEfficiency: TimeEfficiency,
    val consistencyScore: ConsistencyScore,
    val keyInsights: List<String>
)

enum class PerformanceCategory {
    EXCELLENT,
    VERY_GOOD,
    GOOD,
    AVERAGE,
    BELOW_AVERAGE,
    NEEDS_IMPROVEMENT
}

data class TimeEfficiency(
    val averageResponseTime: Long,
    val level: TimeEfficiencyLevel,
    val description: String
)

enum class TimeEfficiencyLevel {
    EXCELLENT,
    GOOD,
    AVERAGE,
    SLOW
}

data class ConsistencyScore(
    val score: Double,
    val level: ConsistencyLevel,
    val description: String
)

enum class ConsistencyLevel {
    HIGH,
    MEDIUM,
    LOW
}

data class CognitiveProfile(
    val primaryAbility: String,
    val abilityScore: Double,
    val subAbilities: Map<String, Double>,
    val cognitiveStrengths: List<String>,
    val cognitiveWeaknesses: List<String>
)

data class PersonalityProfile(
    val primaryTrait: String,
    val traitScore: Double,
    val subTraits: Map<String, Double>,
    val personalityStrengths: List<String>,
    val developmentAreas: List<String>
)

data class StrengthsWeaknessesAnalysis(
    val strengths: List<Strength>,
    val weaknesses: List<Weakness>,
    val responsePatterns: List<ResponsePattern>,
    val overallBalance: OverallBalance
)

data class Strength(
    val area: String,
    val score: Double,
    val description: String,
    val impact: StrengthImpact
)

enum class StrengthImpact {
    HIGH,
    MEDIUM,
    LOW
}

data class Weakness(
    val area: String,
    val score: Double,
    val description: String,
    val priority: WeaknessPriority,
    val improvementPotential: ImprovementPotential
)

enum class WeaknessPriority {
    HIGH,
    MEDIUM,
    LOW
}

enum class ImprovementPotential {
    HIGH,
    MEDIUM,
    LOW
}

data class ResponsePattern(
    val type: ResponsePatternType,
    val description: String,
    val significance: ResponsePatternSignificance
)

enum class ResponsePatternType {
    RESPONSE_TIME_TREND,
    ACCURACY_PATTERN,
    DIFFICULTY_ADAPTATION,
    FATIGUE_EFFECT
}

enum class ResponsePatternSignificance {
    HIGH,
    MEDIUM,
    LOW
}

enum class OverallBalance {
    STRENGTH_DOMINANT,
    BALANCED,
    WEAKNESS_DOMINANT
}

data class ImprovementRecommendation(
    val category: RecommendationCategory,
    val title: String,
    val description: String,
    val actionSteps: List<String>,
    val timeframe: String,
    val priority: RecommendationPriority,
    val expectedImpact: ImprovementImpact
)

enum class RecommendationCategory {
    SKILL_SPECIFIC,
    STRATEGY,
    PRACTICE,
    LIFESTYLE,
    GENERAL
}

enum class RecommendationPriority {
    HIGH,
    MEDIUM,
    LOW
}

enum class ImprovementImpact {
    HIGH,
    MEDIUM,
    LOW
}

data class ComparativeAnalysisResult(
    val personalComparison: PersonalComparison,
    val populationComparison: PopulationComparison,
    val improvementTrend: ImprovementTrend
)

data class PersonalComparison(
    val previousScore: Int?,
    val improvement: Double,
    val trend: String
)

data class PopulationComparison(
    val percentileRank: Int,
    val comparisonGroup: String,
    val description: String
)

data class ImprovementTrend(
    val direction: TrendDirection,
    val magnitude: Double,
    val description: String
)

enum class TrendDirection {
    IMPROVING,
    STABLE,
    DECLINING
}

data class NextStep(
    val category: NextStepCategory,
    val action: String,
    val timeframe: String,
    val priority: NextStepPriority
)

enum class NextStepCategory {
    IMMEDIATE,
    SHORT_TERM,
    LONG_TERM
}

enum class NextStepPriority {
    HIGH,
    MEDIUM,
    LOW
}
