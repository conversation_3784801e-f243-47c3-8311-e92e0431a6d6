<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Selected state - larger, filled circle with subtle shadow -->
    <item android:state_selected="true">
        <layer-list>
            <!-- Shadow -->
            <item android:top="1dp" android:left="1dp">
                <shape android:shape="oval">
                    <solid android:color="#20000000" />
                    <size android:width="16dp" android:height="16dp" />
                </shape>
            </item>
            <!-- Main circle -->
            <item android:bottom="1dp" android:right="1dp">
                <shape android:shape="oval">
                    <solid android:color="@color/primary_light_blue" />
                    <size android:width="16dp" android:height="16dp" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- Unselected state - smaller, semi-transparent circle -->
    <item>
        <shape android:shape="oval">
            <solid android:color="#40888888" />
            <size android:width="10dp" android:height="10dp" />
        </shape>
    </item>
</selector>
