package com.leapiq.braintraining.ui.tests.adapters

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ItemStressResponseOptionBinding
import com.leapiq.braintraining.ui.tests.StressResponseActivity
import com.leapiq.braintraining.ui.tests.StressResponseQuestion
import com.leapiq.braintraining.ui.tests.StressResponseOption
import com.leapiq.braintraining.ui.tests.StressResponseType

/**
 * Adapter for stress response question options
 */
class StressResponseQuestionAdapter(
    private val onOptionSelected: (Int) -> Unit
) : RecyclerView.Adapter<StressResponseQuestionAdapter.OptionViewHolder>() {
    
    private var currentQuestion: StressResponseQuestion? = null

    fun updateQuestion(question: StressResponseQuestion) {
        currentQuestion = question
        notifyDataSetChanged()
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): OptionViewHolder {
        val binding = ItemStressResponseOptionBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return OptionViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: OptionViewHolder, position: Int) {
        currentQuestion?.let { question ->
            if (position < question.options.size) {
                holder.bind(question.options[position], position)
            }
        }
    }
    
    override fun getItemCount(): Int {
        return currentQuestion?.options?.size ?: 0
    }
    
    inner class OptionViewHolder(
        private val binding: ItemStressResponseOptionBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(option: StressResponseOption, position: Int) {
            binding.apply {
                optionText.text = option.text
                optionRadio.isChecked = option.isSelected
                
                // Set click listeners
                root.setOnClickListener {
                    onOptionSelected(position)
                }
                
                optionRadio.setOnClickListener {
                    onOptionSelected(position)
                }
                
                // Visual feedback for selection
                if (option.isSelected) {
                    root.setBackgroundColor(ContextCompat.getColor(root.context, R.color.primary_light_blue_light))
                    optionText.setTextColor(ContextCompat.getColor(root.context, R.color.text_primary))
                } else {
                    root.setBackgroundColor(ContextCompat.getColor(root.context, R.color.surface_white))
                    optionText.setTextColor(ContextCompat.getColor(root.context, R.color.text_primary))
                }
                
                // Add stress level indicator (subtle visual cue)
                val stressLevelColor = when (option.score) {
                    5 -> R.color.success_green      // Excellent stress response
                    4 -> R.color.primary_light_blue // Good stress response
                    3 -> R.color.warning_orange     // Moderate stress response
                    2 -> R.color.error_red          // Poor stress response
                    1 -> R.color.error_red          // Very poor stress response
                    else -> R.color.text_secondary
                }
                
                // Set stress level indicator (hidden during test, can be shown for debugging)
                stressLevelIndicator.setBackgroundColor(ContextCompat.getColor(root.context, stressLevelColor))
                // stressLevelIndicator.visibility = View.VISIBLE // Uncomment for debugging
            }
        }
    }
}
