<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.games.WordAssociationActivity">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/primary_light_blue"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Quit Button -->
        <ImageButton
            android:id="@+id/btn_quit"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="Quit game"
            app:tint="@color/text_white" />

        <!-- Game Title -->
        <TextView
            android:id="@+id/game_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Word Association"
            android:textColor="@color/text_white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Menu Button -->
        <ImageButton
            android:id="@+id/btn_menu"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_menu"
            android:contentDescription="Game menu"
            app:tint="@color/text_white" />

    </LinearLayout>

    <!-- Game Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:elevation="2dp"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Level -->
        <TextView
            android:id="@+id/level_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Level 1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Round -->
        <TextView
            android:id="@+id/round_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Round 1/3"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Trial -->
        <TextView
            android:id="@+id/trial_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Word 1/10"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Instructions -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:padding="16dp"
        android:text="Choose the word most related to the target word!"
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />

    <!-- Target Word Display -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:background="@color/surface_white"
        android:padding="16dp">

        <TextView
            android:id="@+id/target_word_display"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/word_target_background"
            android:gravity="center"
            android:text="CAT"
            android:textColor="@color/text_primary"
            android:textSize="36sp"
            android:textStyle="bold"
            tools:text="FREEDOM" />

    </FrameLayout>

    <!-- Association Prompt -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:padding="12dp"
        android:text="Which word is most related?"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold" />

    <!-- Answer Options -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/surface_white"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- First Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/btn_option1"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="dog"
                android:textSize="18sp"
                android:textStyle="bold"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="12dp"
                android:elevation="4dp"
                tools:text="liberty" />

            <Button
                android:id="@+id/btn_option2"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="car"
                android:textSize="18sp"
                android:textStyle="bold"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="12dp"
                android:elevation="4dp"
                tools:text="democracy" />

        </LinearLayout>

        <!-- Second Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_option3"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="book"
                android:textSize="18sp"
                android:textStyle="bold"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="12dp"
                android:elevation="4dp"
                tools:text="independence" />

            <Button
                android:id="@+id/btn_option4"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="tree"
                android:textSize="18sp"
                android:textStyle="bold"
                android:backgroundTint="@color/surface_white"
                android:textColor="@color/text_primary"
                app:cornerRadius="12dp"
                android:elevation="4dp"
                tools:text="choice" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
