<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_word_search" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_word_search.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_word_search_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="209" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/puzzle_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/found_text" view="TextView"><Expressions/><location startLine="101" startOffset="8" endLine="110" endOffset="38"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="115" startOffset="4" endLine="124" endOffset="33"/></Target><Target id="@+id/grid_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="142" startOffset="12" endLine="148" endOffset="64"/></Target><Target id="@+id/word_list_recycler_view" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="174" startOffset="16" endLine="181" endOffset="68"/></Target><Target id="@+id/btn_hint" view="Button"><Expressions/><location startLine="197" startOffset="8" endLine="205" endOffset="36"/></Target></Targets></Layout>