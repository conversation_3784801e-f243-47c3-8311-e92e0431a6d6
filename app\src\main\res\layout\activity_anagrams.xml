<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.games.AnagramsActivity">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/primary_light_blue"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="16dp"
        android:paddingEnd="16dp">

        <!-- Quit Button -->
        <ImageButton
            android:id="@+id/btn_quit"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_close"
            android:contentDescription="Quit game"
            app:tint="@color/text_white" />

        <!-- Game Title -->
        <TextView
            android:id="@+id/game_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Anagrams"
            android:textColor="@color/text_white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <!-- Menu Button -->
        <ImageButton
            android:id="@+id/btn_menu"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_menu"
            android:contentDescription="Game menu"
            app:tint="@color/text_white" />

    </LinearLayout>

    <!-- Game Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:elevation="2dp"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- Level -->
        <TextView
            android:id="@+id/level_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Level 1"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Round -->
        <TextView
            android:id="@+id/round_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Round 1/3"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- Trial -->
        <TextView
            android:id="@+id/trial_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="Word 1/8"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Instructions -->
    <TextView
        android:id="@+id/instruction_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:gravity="center"
        android:padding="16dp"
        android:text="Unscramble the letters to form a word!"
        android:textColor="@color/text_secondary"
        android:textSize="14sp" />

    <!-- Scrambled Word Display -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="@color/surface_white"
        android:padding="16dp">

        <TextView
            android:id="@+id/scrambled_word_display"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/anagram_scrambled_background"
            android:gravity="center"
            android:text="TAC"
            android:textColor="@color/text_primary"
            android:textSize="32sp"
            android:textStyle="bold"
            android:fontFamily="monospace"
            tools:text="EEFRDOM" />

    </FrameLayout>

    <!-- User Input Display -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:background="@color/surface_white"
        android:padding="16dp">

        <TextView
            android:id="@+id/user_input_display"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/anagram_input_background"
            android:gravity="center"
            android:text="___"
            android:textColor="@color/text_primary"
            android:textSize="28sp"
            android:textStyle="bold"
            android:fontFamily="monospace"
            tools:text="FREE___" />

    </FrameLayout>

    <!-- Letter Buttons Container -->
    <LinearLayout
        android:id="@+id/letters_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center">

        <!-- Letter buttons will be added dynamically -->

    </LinearLayout>

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/surface_white"
        android:orientation="vertical"
        android:padding="16dp"
        android:gravity="center">

        <!-- First Row -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="12dp">

            <Button
                android:id="@+id/btn_clear"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Clear"
                android:textSize="16sp"
                android:backgroundTint="@color/primary_light_blue_light"
                android:textColor="@color/text_primary"
                app:cornerRadius="12dp" />

            <Button
                android:id="@+id/btn_hint"
                android:layout_width="0dp"
                android:layout_height="60dp"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="Hint"
                android:textSize="16sp"
                android:backgroundTint="@color/stroop_yellow"
                android:textColor="@color/text_primary"
                app:cornerRadius="12dp" />

        </LinearLayout>

        <!-- Submit Button -->
        <Button
            android:id="@+id/btn_submit"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:text="Submit Answer"
            android:textSize="18sp"
            android:textStyle="bold"
            android:backgroundTint="@color/stroop_green"
            android:textColor="@color/text_white"
            app:cornerRadius="12dp"
            android:elevation="4dp" />

    </LinearLayout>

</LinearLayout>
