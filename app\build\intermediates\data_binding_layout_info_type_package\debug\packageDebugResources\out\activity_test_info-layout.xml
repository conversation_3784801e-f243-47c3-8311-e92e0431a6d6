<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_test_info" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_test_info.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_test_info_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="155" endOffset="14"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="10" startOffset="8" endLine="18" endOffset="54"/></Target><Target id="@+id/hero_section" view="LinearLayout"><Expressions/><location startLine="21" startOffset="4" endLine="57" endOffset="18"/></Target><Target id="@+id/test_icon" view="ImageView"><Expressions/><location startLine="29" startOffset="8" endLine="35" endOffset="49"/></Target><Target id="@+id/test_title" view="TextView"><Expressions/><location startLine="37" startOffset="8" endLine="45" endOffset="38"/></Target><Target id="@+id/test_subtitle" view="TextView"><Expressions/><location startLine="47" startOffset="8" endLine="55" endOffset="33"/></Target><Target id="@+id/test_description" view="TextView"><Expressions/><location startLine="83" startOffset="16" endLine="91" endOffset="56"/></Target><Target id="@+id/key_points_list" view="LinearLayout"><Expressions/><location startLine="94" startOffset="16" endLine="98" endOffset="52"/></Target><Target id="@+id/estimated_time" view="TextView"><Expressions/><location startLine="129" startOffset="12" endLine="136" endOffset="63"/></Target><Target id="@+id/btn_start_test" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="143" startOffset="4" endLine="153" endOffset="50"/></Target></Targets></Layout>