package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivitySpeedMathBinding
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date
import kotlin.random.Random

/**
 * Speed Math Game
 * Rapid-fire math problems with time pressure
 * Tests calculation speed and accuracy under time constraints
 */
class SpeedMathActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySpeedMathBinding
    private lateinit var progressManager: GameProgressManager

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "math_3"

    // Speed math specific
    private var currentProblem = ""
    private var correctAnswer = 0
    private var multipleChoiceOptions = mutableListOf<Int>()
    private var isMultipleChoice = false
    private var roundTimer: CountDownTimer? = null
    private var timeRemaining = 0L
    private var problemsPerMinute = 0
    private var roundStartTime = 0L

    // Math operations
    private enum class Operation(val symbol: String) {
        ADD("+"), SUBTRACT("-"), MULTIPLY("×"), DIVIDE("÷")
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySpeedMathBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.speed_math)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Solve as many problems as you can!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish()
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }

            // Setup multiple choice buttons
            setupMultipleChoiceButtons()

            // Setup number pad (for direct input mode)
            setupNumberPad()

            // Setup control buttons
            btnClear.setOnClickListener {
                clearInput()
            }

            btnSubmit.setOnClickListener {
                submitAnswer()
            }
        }
    }

    private fun setupMultipleChoiceButtons() {
        binding.apply {
            btnOption1.setOnClickListener { selectMultipleChoice(0) }
            btnOption2.setOnClickListener { selectMultipleChoice(1) }
            btnOption3.setOnClickListener { selectMultipleChoice(2) }
            btnOption4.setOnClickListener { selectMultipleChoice(3) }
        }
    }

    private fun setupNumberPad() {
        binding.apply {
            btn0.setOnClickListener { addDigit("0") }
            btn1.setOnClickListener { addDigit("1") }
            btn2.setOnClickListener { addDigit("2") }
            btn3.setOnClickListener { addDigit("3") }
            btn4.setOnClickListener { addDigit("4") }
            btn5.setOnClickListener { addDigit("5") }
            btn6.setOnClickListener { addDigit("6") }
            btn7.setOnClickListener { addDigit("7") }
            btn8.setOnClickListener { addDigit("8") }
            btn9.setOnClickListener { addDigit("9") }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        roundStartTime = System.currentTimeMillis()
        totalCorrect = 0
        totalAttempts = 0
        problemsPerMinute = 0
        
        isMultipleChoice = shouldUseMultipleChoice(currentLevel)
        val roundDuration = getRoundDuration(currentLevel)
        
        binding.apply {
            if (isMultipleChoice) {
                multipleChoiceContainer.visibility = android.view.View.VISIBLE
                numberPadContainer.visibility = android.view.View.GONE
                instructionText.text = "Choose the correct answer quickly!"
            } else {
                multipleChoiceContainer.visibility = android.view.View.GONE
                numberPadContainer.visibility = android.view.View.VISIBLE
                instructionText.text = "Type your answer and submit!"
            }
        }
        
        startRoundTimer(roundDuration)
        generateNextProblem()
    }

    private fun shouldUseMultipleChoice(level: Int): Boolean {
        return level <= 15 // Multiple choice for easier levels, direct input for harder
    }

    private fun getRoundDuration(level: Int): Long {
        return when (level) {
            in 1..5 -> 90000L     // 90 seconds
            in 6..10 -> 75000L    // 75 seconds
            in 11..15 -> 60000L   // 60 seconds
            in 16..20 -> 45000L   // 45 seconds
            else -> 30000L        // 30 seconds
        }
    }

    private fun startRoundTimer(duration: Long) {
        timeRemaining = duration
        roundTimer = object : CountDownTimer(duration, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                timeRemaining = millisUntilFinished
                val seconds = (millisUntilFinished / 1000).toInt()
                binding.timerText.text = "Time: ${seconds}s"
                
                // Change color when time is running low
                if (seconds <= 10) {
                    binding.timerText.setTextColor(ContextCompat.getColor(this@SpeedMathActivity, R.color.stroop_red))
                } else if (seconds <= 20) {
                    binding.timerText.setTextColor(ContextCompat.getColor(this@SpeedMathActivity, R.color.stroop_yellow))
                } else {
                    binding.timerText.setTextColor(ContextCompat.getColor(this@SpeedMathActivity, R.color.text_primary))
                }
            }

            override fun onFinish() {
                binding.timerText.text = "Time: 0s"
                roundComplete()
            }
        }.start()
    }

    private fun generateNextProblem() {
        val difficulty = getDifficulty(currentLevel)
        val operations = getAvailableOperations(currentLevel)
        val operation = operations.random()
        
        generateProblem(operation, difficulty)
        
        if (isMultipleChoice) {
            generateMultipleChoiceOptions()
            displayMultipleChoiceProblem()
        } else {
            displayDirectInputProblem()
        }
        
        clearInput()
    }

    private fun getDifficulty(level: Int): Int {
        return when (level) {
            in 1..3 -> 1       // Single digit
            in 4..6 -> 2       // Two digit
            in 7..10 -> 3      // Larger numbers
            in 11..15 -> 4     // Mixed complexity
            in 16..20 -> 5     // High complexity
            else -> 6          // Expert level
        }
    }

    private fun getAvailableOperations(level: Int): List<Operation> {
        return when (level) {
            in 1..2 -> listOf(Operation.ADD)
            in 3..4 -> listOf(Operation.ADD, Operation.SUBTRACT)
            in 5..8 -> listOf(Operation.ADD, Operation.SUBTRACT, Operation.MULTIPLY)
            else -> listOf(Operation.ADD, Operation.SUBTRACT, Operation.MULTIPLY, Operation.DIVIDE)
        }
    }

    private fun generateProblem(operation: Operation, difficulty: Int) {
        when (operation) {
            Operation.ADD -> generateAddition(difficulty)
            Operation.SUBTRACT -> generateSubtraction(difficulty)
            Operation.MULTIPLY -> generateMultiplication(difficulty)
            Operation.DIVIDE -> generateDivision(difficulty)
        }
    }

    private fun generateAddition(difficulty: Int) {
        val (min, max) = getNumberRange(difficulty)
        val a = Random.nextInt(min, max + 1)
        val b = Random.nextInt(min, max + 1)
        
        currentProblem = "$a + $b"
        correctAnswer = a + b
    }

    private fun generateSubtraction(difficulty: Int) {
        val (min, max) = getNumberRange(difficulty)
        val a = Random.nextInt(min, max + 1)
        val b = Random.nextInt(min, a + 1) // Ensure positive result
        
        currentProblem = "$a - $b"
        correctAnswer = a - b
    }

    private fun generateMultiplication(difficulty: Int) {
        val (min, max) = when (difficulty) {
            1 -> Pair(2, 9)
            2 -> Pair(2, 12)
            3 -> Pair(10, 25)
            4 -> Pair(10, 50)
            5 -> Pair(20, 99)
            else -> Pair(10, 99)
        }
        
        val a = Random.nextInt(min, max + 1)
        val b = Random.nextInt(2, if (difficulty <= 2) 10 else 15)
        
        currentProblem = "$a × $b"
        correctAnswer = a * b
    }

    private fun generateDivision(difficulty: Int) {
        val (min, max) = getNumberRange(difficulty)
        val divisor = Random.nextInt(2, if (difficulty <= 2) 10 else 15)
        val quotient = Random.nextInt(min / divisor + 1, max / divisor + 1)
        val dividend = divisor * quotient
        
        currentProblem = "$dividend ÷ $divisor"
        correctAnswer = quotient
    }

    private fun getNumberRange(difficulty: Int): Pair<Int, Int> {
        return when (difficulty) {
            1 -> Pair(1, 9)
            2 -> Pair(10, 99)
            3 -> Pair(50, 200)
            4 -> Pair(100, 500)
            5 -> Pair(200, 999)
            else -> Pair(500, 9999)
        }
    }

    private fun generateMultipleChoiceOptions() {
        multipleChoiceOptions.clear()
        multipleChoiceOptions.add(correctAnswer)
        
        // Generate 3 plausible wrong answers
        val range = maxOf(10, correctAnswer / 4)
        while (multipleChoiceOptions.size < 4) {
            val wrongAnswer = correctAnswer + Random.nextInt(-range, range + 1)
            if (wrongAnswer != correctAnswer && wrongAnswer !in multipleChoiceOptions && wrongAnswer >= 0) {
                multipleChoiceOptions.add(wrongAnswer)
            }
        }
        
        multipleChoiceOptions.shuffle()
    }

    private fun displayMultipleChoiceProblem() {
        binding.apply {
            problemDisplay.text = "$currentProblem = ?"
            btnOption1.text = multipleChoiceOptions[0].toString()
            btnOption2.text = multipleChoiceOptions[1].toString()
            btnOption3.text = multipleChoiceOptions[2].toString()
            btnOption4.text = multipleChoiceOptions[3].toString()
        }
    }

    private fun displayDirectInputProblem() {
        binding.problemDisplay.text = "$currentProblem = ?"
    }

    private fun selectMultipleChoice(optionIndex: Int) {
        val selectedAnswer = multipleChoiceOptions[optionIndex]
        checkAnswer(selectedAnswer)
    }

    private fun addDigit(digit: String) {
        if (!isMultipleChoice) {
            val currentInput = binding.answerInput.text.toString()
            if (currentInput.length < 6) {
                binding.answerInput.text = currentInput + digit
            }
        }
    }

    private fun clearInput() {
        if (!isMultipleChoice) {
            binding.answerInput.text = ""
        }
    }

    private fun submitAnswer() {
        if (!isMultipleChoice) {
            val userInput = binding.answerInput.text.toString()
            if (userInput.isNotEmpty()) {
                val userAnswer = userInput.toIntOrNull() ?: return
                checkAnswer(userAnswer)
            }
        }
    }

    private fun checkAnswer(userAnswer: Int) {
        totalAttempts++
        val isCorrect = userAnswer == correctAnswer
        
        if (isCorrect) {
            totalCorrect++
            showQuickFeedback(true)
        } else {
            showQuickFeedback(false)
        }
        
        // Update score display
        binding.scoreText.text = "Score: $totalCorrect/$totalAttempts"
        
        // Generate next problem immediately
        Handler(Looper.getMainLooper()).postDelayed({
            generateNextProblem()
        }, 300) // Very short delay for speed
    }

    private fun showQuickFeedback(isCorrect: Boolean) {
        val color = if (isCorrect) R.color.stroop_green else R.color.stroop_red
        binding.problemDisplay.setTextColor(ContextCompat.getColor(this, color))
        
        Handler(Looper.getMainLooper()).postDelayed({
            binding.problemDisplay.setTextColor(ContextCompat.getColor(this, R.color.text_primary))
        }, 300)
    }

    private fun roundComplete() {
        roundTimer?.cancel()
        
        val roundTime = System.currentTimeMillis() - roundStartTime
        val roundAccuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0
        problemsPerMinute = if (roundTime > 0) ((totalAttempts * 60000) / roundTime).toInt() else 0
        
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete
            showResults()
        } else {
            // Next round
            binding.instructionText.text = """
                Round complete!
                Accuracy: ${(roundAccuracy * 100).toInt()}%
                Speed: $problemsPerMinute problems/min
            """.trimIndent()
            binding.roundText.text = "Round $currentRound/$maxRounds"
            
            Handler(Looper.getMainLooper()).postDelayed({
                setupGame()
            }, 3000)
        }
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = accuracy > 0.7,
                timeSpentMs = totalTime / maxRounds,
                attempts = totalAttempts / maxRounds
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * problemsPerMinute).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Speed: $problemsPerMinute problems/min
                Total Problems: $totalAttempts

                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 3000)
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Best Speed: $problemsPerMinute problems/min

                Speed Math Master!
            """.trimIndent()

            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 4000)
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        setupGame()
    }

    private fun showGameMenu() {
        roundTimer?.cancel()
        
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
            // Resume timer with remaining time
            startRoundTimer(timeRemaining)
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        roundTimer?.cancel()
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0
        binding.roundText.text = "Round $currentRound/$maxRounds"
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Solve as many math problems as possible in the time limit

                📋 RULES:
                • Answer math problems quickly and accurately
                • Multiple choice mode: tap the correct answer
                • Direct input mode: type answer and submit
                • Beat the clock to maximize your score

                💡 TIPS:
                • Speed AND accuracy both matter
                • Use mental math shortcuts
                • Don't spend too long on hard problems
                • Practice makes you faster

                🏆 SCORING:
                • Score = accuracy × problems per minute
                • Higher levels have shorter time limits
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
                startRoundTimer(timeRemaining)
            }
            .show()
    }

    override fun onDestroy() {
        super.onDestroy()
        roundTimer?.cancel()
    }
}
