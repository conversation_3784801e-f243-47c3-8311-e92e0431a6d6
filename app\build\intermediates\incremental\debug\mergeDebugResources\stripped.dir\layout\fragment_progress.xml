<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.progress.ProgressFragment">

    <!-- Header -->
    <include layout="@layout/layout_header" />

    <!-- Main Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- User Profile Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <!-- Profile Picture -->
                    <ImageView
                        android:id="@+id/profile_picture"
                        android:layout_width="64dp"
                        android:layout_height="64dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/header_icon_background"
                        android:padding="8dp"
                        android:src="@drawable/ic_person"
                        android:tint="@color/primary_light_blue_dark" />

                    <!-- User Info -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <!-- Username -->
                        <TextView
                            android:id="@+id/username"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text=""
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <!-- Level -->
                        <TextView
                            android:id="@+id/user_level"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/current_level"
                            android:textColor="@color/primary_light_blue"
                            android:textSize="14sp"
                            android:textStyle="bold"
                            tools:text="Level 12" />

                        <!-- Streaks -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:orientation="horizontal">

                            <TextView
                                android:id="@+id/max_streak"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/max_streak"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                tools:text="Max: 15 days" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:text="|"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/current_streak"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="8dp"
                                android:text="@string/current_streak"
                                android:textColor="@color/text_secondary"
                                android:textSize="12sp"
                                tools:text="Current: 7 days" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Total Score -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/total_score"
                            android:textColor="@color/text_secondary"
                            android:textSize="12sp" />

                        <TextView
                            android:id="@+id/total_score"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="2,450"
                            android:textColor="@color/primary_light_blue"
                            android:textSize="20sp"
                            android:textStyle="bold" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- All-Time Accuracy Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="All-Time Accuracy"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <!-- Overall Accuracy -->
                    <TextView
                        android:id="@+id/overall_accuracy"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Overall: 76%"
                        android:textColor="@color/primary_light_blue"
                        android:textSize="18sp"
                        android:textStyle="bold"
                        android:gravity="center"
                        android:layout_marginBottom="8dp" />

                    <!-- Category Accuracies -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/category_accuracy_recycler"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:listitem="@layout/item_category_accuracy" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Weekly Stats Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="This Week"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <TextView
                        android:id="@+id/games_played_week"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/games_played_week"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="Games Played: 15 this week" />

                    <!-- Weekly Accuracy Chart Placeholder -->
                    <View
                        android:layout_width="match_parent"
                        android:layout_height="120dp"
                        android:layout_marginTop="12dp"
                        android:background="@color/background_light_gray"
                        android:layout_marginBottom="8dp" />

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Weekly accuracy chart will be implemented here"
                        android:textColor="@color/text_secondary"
                        android:textSize="12sp"
                        android:gravity="center"
                        android:fontFamily="monospace" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Achievements Section -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="12dp"
                app:cardElevation="3dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/achievements"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:layout_marginBottom="12dp" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/achievements_recycler"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:spanCount="3"
                        tools:listitem="@layout/item_achievement" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
