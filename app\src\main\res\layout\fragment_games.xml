<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_light_gray"
    tools:context=".ui.games.GamesFragment">

    <!-- Header -->
    <include layout="@layout/layout_header" />

    <!-- Category Tabs -->
    <HorizontalScrollView
        android:id="@+id/category_scroll_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface_white"
        android:elevation="4dp"
        android:scrollbars="none"
        android:clipToPadding="false"
        android:paddingStart="8dp"
        android:paddingEnd="8dp">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/tab_all"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginEnd="12dp"
                android:text="@string/all_games"
                android:textColor="@color/text_white"
                android:textSize="14sp"
                android:backgroundTint="@color/primary_light_blue"
                app:cornerRadius="20dp"
                android:elevation="2dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/tab_memory"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginEnd="12dp"
                android:text="@string/memory_games"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:backgroundTint="@color/surface_white"
                app:cornerRadius="20dp"
                app:strokeColor="@color/primary_light_blue_light"
                app:strokeWidth="1dp"
                android:elevation="1dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/tab_attention"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginEnd="12dp"
                android:text="@string/attention_games"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:backgroundTint="@color/surface_white"
                app:cornerRadius="20dp"
                app:strokeColor="@color/primary_light_blue_light"
                app:strokeWidth="1dp"
                android:elevation="1dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/tab_math"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginEnd="12dp"
                android:text="@string/math_games"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:backgroundTint="@color/surface_white"
                app:cornerRadius="20dp"
                app:strokeColor="@color/primary_light_blue_light"
                app:strokeWidth="1dp"
                android:elevation="1dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/tab_logic"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_marginEnd="12dp"
                android:text="@string/logic_games"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:backgroundTint="@color/surface_white"
                app:cornerRadius="20dp"
                app:strokeColor="@color/primary_light_blue_light"
                app:strokeWidth="1dp"
                android:elevation="1dp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/tab_language"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:text="@string/language_games"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:backgroundTint="@color/surface_white"
                app:cornerRadius="20dp"
                app:strokeColor="@color/primary_light_blue_light"
                app:strokeWidth="1dp"
                android:elevation="1dp" />

        </LinearLayout>

    </HorizontalScrollView>

    <!-- Games Grid -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/games_recycler"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="8dp"
        android:clipToPadding="false"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        app:spanCount="2"
        tools:listitem="@layout/item_game_card" />

</LinearLayout>
