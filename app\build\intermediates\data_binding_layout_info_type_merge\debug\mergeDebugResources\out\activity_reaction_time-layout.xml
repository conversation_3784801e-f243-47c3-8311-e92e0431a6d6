<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_reaction_time" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_reaction_time.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_reaction_time_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="173" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/trial_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/mode_text" view="TextView"><Expressions/><location startLine="103" startOffset="4" endLine="113" endOffset="34"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="116" startOffset="4" endLine="125" endOffset="33"/></Target><Target id="@+id/reaction_area" view="FrameLayout"><Expressions/><location startLine="128" startOffset="4" endLine="171" endOffset="17"/></Target><Target id="@+id/stimulus_circle" view="View"><Expressions/><location startLine="139" startOffset="8" endLine="148" endOffset="40"/></Target><Target id="@+id/pulse_effect" view="View"><Expressions/><location startLine="151" startOffset="8" endLine="158" endOffset="44"/></Target></Targets></Layout>