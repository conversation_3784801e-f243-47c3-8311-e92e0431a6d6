package com.leapiq.braintraining.ui.games

import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.widget.Button
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityCardMatchingBinding
import com.leapiq.braintraining.ui.games.memory.MemoryCard
import com.leapiq.braintraining.ui.games.memory.MemoryCardAdapter
import com.leapiq.braintraining.data.GameProgressManager
import com.leapiq.braintraining.data.model.LevelResult
import com.leapiq.braintraining.data.model.RoundResult
import java.util.Date

/**
 * Simple Card Matching Memory Game
 * Find matching pairs by flipping cards
 */
class CardMatchingActivity : AppCompatActivity() {

    private lateinit var binding: ActivityCardMatchingBinding
    private lateinit var adapter: MemoryCardAdapter
    private lateinit var cards: MutableList<MemoryCard>
    private lateinit var progressManager: GameProgressManager

    private var flippedCards = mutableListOf<Int>()
    private var matchedPairs = 0
    private var isProcessing = false

    // Game state
    private var currentLevel = 1
    private var currentRound = 1
    private val maxRounds = 3  // Changed from 5 to 3
    private var startTime = 0L
    private var totalCorrect = 0
    private var totalAttempts = 0
    private val gameId = "memory_1"

    // Professional Google Material Design icons
    private val cardSymbols = listOf(
        R.drawable.ic_apple, R.drawable.ic_car, R.drawable.ic_home, R.drawable.ic_music,
        R.drawable.ic_favorite, R.drawable.ic_pets, R.drawable.ic_sports, R.drawable.ic_school
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityCardMatchingBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize progress manager and load current level
        progressManager = GameProgressManager.getInstance(this)
        currentLevel = progressManager.getNextLevel(gameId)

        setupUI()
        setupGame()
    }

    private fun setupUI() {
        binding.apply {
            gameTitle.text = getString(R.string.card_matching)
            levelText.text = "Level $currentLevel"
            roundText.text = "Round $currentRound/$maxRounds"
            instructionText.text = "Find matching pairs by tapping cards!"

            // Setup quit button
            btnQuit.setOnClickListener {
                finish() // Go back to games page
            }

            // Setup menu button
            btnMenu.setOnClickListener {
                showGameMenu()
            }
        }
    }

    private fun setupGame() {
        startTime = System.currentTimeMillis()
        createCards()
        setupRecyclerView()

        // Show cards briefly then hide them
        Handler(Looper.getMainLooper()).postDelayed({
            hideAllCards()
        }, 1500) // Reduced from 2000ms to 1500ms
    }
    
    private fun createCards() {
        cards = mutableListOf()

        val levelConfig = getLevelConfiguration(currentLevel)
        val symbols = cardSymbols.take(levelConfig.pairs)
        var id = 0

        // Add each symbol twice (to create pairs)
        symbols.forEach { symbol ->
            cards.add(MemoryCard(id++, symbol))
            cards.add(MemoryCard(id++, symbol))
        }

        // Shuffle the cards
        cards.shuffle()
    }

    private fun getLevelConfiguration(level: Int): LevelConfig {
        return when (level) {
            in 1..5 -> LevelConfig(pairs = 3, columns = 2, rows = 3) // 2x3 grid (2 columns, 3 rows), 6 cards
            in 6..10 -> LevelConfig(pairs = 4, columns = 2, rows = 4) // 2x4 grid, 8 cards
            in 11..15 -> LevelConfig(pairs = 6, columns = 3, rows = 4) // 3x4 grid, 12 cards
            in 16..20 -> LevelConfig(pairs = 8, columns = 4, rows = 4) // 4x4 grid, 16 cards
            in 21..25 -> LevelConfig(pairs = 10, columns = 4, rows = 5) // 4x5 grid, 20 cards
            else -> LevelConfig(pairs = 10, columns = 4, rows = 5) // Default to hardest
        }
    }

    data class LevelConfig(
        val pairs: Int,
        val columns: Int,
        val rows: Int
    )

    private fun setupRecyclerView() {
        adapter = MemoryCardAdapter(cards) { position ->
            onCardClicked(position)
        }

        val levelConfig = getLevelConfiguration(currentLevel)

        binding.cardGrid.apply {
            layoutManager = GridLayoutManager(this@CardMatchingActivity, levelConfig.columns)
            adapter = <EMAIL>
        }
    }

    private fun onCardClicked(position: Int) {
        // Check if card is already flipped, matched, or if we're processing
        if (cards[position].isFlipped || cards[position].isMatched || isProcessing) {
            return
        }

        // If we already have 2 flipped cards, ignore new clicks
        if (flippedCards.size >= 2) {
            return
        }

        // Flip the card immediately (no animation delay)
        cards[position].isFlipped = true
        adapter.notifyItemChanged(position)
        flippedCards.add(position)

        // Check if we have 2 flipped cards
        if (flippedCards.size == 2) {
            isProcessing = true
            checkForMatch()
        }
    }

    private fun checkForMatch() {
        val firstPos = flippedCards[0]
        val secondPos = flippedCards[1]
        val firstCard = cards[firstPos]
        val secondCard = cards[secondPos]

        // Very short delay to show both cards, then check match
        Handler(Looper.getMainLooper()).postDelayed({
            if (firstCard.identifier == secondCard.identifier) {
                // Match found!
                firstCard.isMatched = true
                secondCard.isMatched = true
                matchedPairs++
                totalCorrect++

                adapter.notifyItemChanged(firstPos)
                adapter.notifyItemChanged(secondPos)

                // Immediately allow new clicks
                flippedCards.clear()
                isProcessing = false

                // Check if round is complete
                val levelConfig = getLevelConfiguration(currentLevel)
                if (matchedPairs == levelConfig.pairs) {
                    roundComplete()
                }
            } else {
                // No match, flip cards back but allow immediate new clicks
                firstCard.isFlipped = false
                secondCard.isFlipped = false

                adapter.notifyItemChanged(firstPos)
                adapter.notifyItemChanged(secondPos)

                // Immediately allow new clicks (same as matches)
                flippedCards.clear()
                isProcessing = false
            }

            totalAttempts++
        }, 300) // Very short delay to see both cards
    }
    
    private fun hideAllCards() {
        cards.forEachIndexed { index, card ->
            if (!card.isMatched) {
                card.isFlipped = false
                adapter.notifyItemChanged(index)
            }
        }
    }

    private fun roundComplete() {
        currentRound++

        if (currentRound > maxRounds) {
            // Level complete - show results
            showResults()
        } else {
            // Start next round
            binding.instructionText.text = "Round $currentRound starting..."
            binding.roundText.text = "Round $currentRound/$maxRounds"

            Handler(Looper.getMainLooper()).postDelayed({
                resetForNextRound()
            }, 1000) // Reduced from 1500ms to 1000ms
        }
    }

    private fun resetForNextRound() {
        matchedPairs = 0
        flippedCards.clear()
        isProcessing = false

        // Reset all cards
        cards.forEach { card ->
            card.isFlipped = false
            card.isMatched = false
        }

        // Shuffle cards for next round
        cards.shuffle()
        cards.forEachIndexed { index, _ ->
            adapter.notifyItemChanged(index)
        }

        // Show cards briefly then hide
        Handler(Looper.getMainLooper()).postDelayed({
            hideAllCards()
            binding.instructionText.text = "Find matching pairs by tapping cards!"
        }, 1500) // Reduced from 2000ms to 1500ms
    }

    private fun showResults() {
        val endTime = System.currentTimeMillis()
        val totalTime = endTime - startTime
        val accuracy = if (totalAttempts > 0) (totalCorrect.toDouble() / totalAttempts) else 1.0

        // Save level result
        val roundResults = mutableListOf<RoundResult>()
        for (i in 1..maxRounds) {
            roundResults.add(RoundResult(
                roundNumber = i,
                isCorrect = true, // Simplified - assume all rounds completed successfully
                timeSpentMs = totalTime / maxRounds,
                attempts = 1
            ))
        }

        val levelResult = LevelResult(
            gameId = gameId,
            level = currentLevel,
            rounds = roundResults,
            totalTimeMs = totalTime,
            accuracy = accuracy,
            score = (accuracy * 100).toInt(),
            completedAt = Date()
        )

        progressManager.saveLevelResult(levelResult)

        if (currentLevel < 25) {
            binding.instructionText.text = """
                Level $currentLevel Complete!

                Accuracy: ${(accuracy * 100).toInt()}%
                Time: ${totalTime / 1000}s

                Congratulations!
                Starting Level ${currentLevel + 1}...
            """.trimIndent()

            // Start next level
            Handler(Looper.getMainLooper()).postDelayed({
                startNextLevel()
            }, 2000) // Reduced from 3000ms to 2000ms
        } else {
            binding.instructionText.text = """
                🎉 ALL LEVELS COMPLETE! 🎉

                Final Accuracy: ${(accuracy * 100).toInt()}%
                Total Time: ${totalTime / 1000}s

                You're a Memory Master!
            """.trimIndent()

            // Finish game
            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 3000) // Reduced from 4000ms to 3000ms
        }
    }

    private fun startNextLevel() {
        currentLevel++
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        // Update UI
        binding.levelText.text = "Level $currentLevel"
        binding.roundText.text = "Round $currentRound/$maxRounds"

        // Setup new level
        setupGame()
    }

    private fun showGameMenu() {
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_game_menu, null)
        val dialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(true)
            .create()

        // Setup menu buttons
        dialogView.findViewById<Button>(R.id.btn_continue).setOnClickListener {
            dialog.dismiss()
        }

        dialogView.findViewById<Button>(R.id.btn_restart).setOnClickListener {
            dialog.dismiss()
            restartLevel()
        }

        dialogView.findViewById<Button>(R.id.btn_how_to_play).setOnClickListener {
            dialog.dismiss()
            showHowToPlay()
        }

        dialogView.findViewById<Button>(R.id.btn_quit_menu).setOnClickListener {
            dialog.dismiss()
            finish()
        }

        dialog.show()
    }

    private fun restartLevel() {
        currentRound = 1
        totalCorrect = 0
        totalAttempts = 0

        // Update UI
        binding.roundText.text = "Round $currentRound/$maxRounds"

        // Restart the level
        setupGame()
    }

    private fun showHowToPlay() {
        AlertDialog.Builder(this)
            .setTitle("How to Play")
            .setMessage("""
                🎯 GOAL: Find all matching pairs of cards

                📋 RULES:
                • Tap cards to flip them over
                • Find 2 cards with the same symbol
                • Complete 3 rounds to advance to next level
                • Each level has more cards and difficulty

                💡 TIPS:
                • Remember card positions
                • Work systematically
                • Practice makes perfect!

                🏆 SCORING:
                • Accuracy = correct matches / total attempts
                • Faster completion = better score
            """.trimIndent())
            .setPositiveButton("Got it!") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
}
