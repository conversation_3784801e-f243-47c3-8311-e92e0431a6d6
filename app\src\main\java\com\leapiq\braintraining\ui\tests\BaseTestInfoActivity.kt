package com.leapiq.braintraining.ui.tests

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.leapiq.braintraining.R
import com.leapiq.braintraining.databinding.ActivityTestInfoBinding

/**
 * Base class for test information/instruction pages
 * Shows test details, instructions, and start button before beginning the actual test
 */
abstract class BaseTestInfoActivity : AppCompatActivity() {
    
    protected lateinit var binding: ActivityTestInfoBinding
    protected lateinit var testId: String
    
    companion object {
        const val EXTRA_TEST_ID = "test_id"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityTestInfoBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        // Get test ID from intent
        testId = intent.getStringExtra(EXTRA_TEST_ID) ?: ""
        
        if (testId.isEmpty()) {
            finish()
            return
        }
        
        setupUI()
        loadTestInfo()
    }
    
    private fun setupUI() {
        binding.apply {
            // Setup toolbar
            toolbar.setNavigationOnClickListener { finish() }
            
            // Setup start button
            btnStartTest.setOnClickListener { startTest() }
            
            // Setup modern styling
            setupModernStyling()
        }
    }
    
    private fun setupModernStyling() {
        binding.apply {
            // Set gradient background for hero section
            heroSection.background = ContextCompat.getDrawable(
                this@BaseTestInfoActivity, 
                getHeroGradient()
            )
            
            // Style the start button
            btnStartTest.apply {
                background = ContextCompat.getDrawable(
                    this@BaseTestInfoActivity,
                    R.drawable.button_primary_rounded
                )
                setTextColor(ContextCompat.getColor(this@BaseTestInfoActivity, R.color.surface_white))
            }
        }
    }
    
    private fun startTest() {
        val intent = Intent(this, getTestActivityClass())
        intent.putExtra(BaseTestActivity.EXTRA_TEST_ID, testId)
        startActivity(intent)
        finish() // Close info activity
    }
    
    /**
     * Load test-specific information into the UI
     */
    protected abstract fun loadTestInfo()
    
    /**
     * Get the test activity class to launch
     */
    protected abstract fun getTestActivityClass(): Class<*>
    
    /**
     * Get the hero section gradient drawable
     */
    protected abstract fun getHeroGradient(): Int
    
    /**
     * Helper method to set test info
     */
    protected fun setTestInfo(
        title: String,
        subtitle: String,
        description: String,
        keyPoints: List<String>,
        estimatedMinutes: Int,
        iconResource: Int
    ) {
        binding.apply {
            testTitle.text = title
            testSubtitle.text = subtitle
            testDescription.text = description
            estimatedTime.text = "$estimatedMinutes minutes"
            testIcon.setImageResource(iconResource)

            // Setup key points
            keyPointsList.removeAllViews()
            keyPoints.forEach { point ->
                val pointView = layoutInflater.inflate(
                    R.layout.item_key_point,
                    keyPointsList,
                    false
                )
                val pointText = pointView.findViewById<android.widget.TextView>(R.id.key_point_text)
                pointText.text = point
                keyPointsList.addView(pointView)
            }
        }
    }
}
