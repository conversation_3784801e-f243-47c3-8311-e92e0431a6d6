<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="3dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Test Image (Rectangle) -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="120dp">

            <ImageView
                android:id="@+id/test_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:background="@color/secondary_blue"
                tools:src="@drawable/ic_tests" />

            <!-- Test Duration Badge -->
            <TextView
                android:id="@+id/duration_badge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="top|end"
                android:layout_margin="8dp"
                android:background="@drawable/header_icon_background"
                android:paddingStart="8dp"
                android:paddingTop="4dp"
                android:paddingEnd="8dp"
                android:paddingBottom="4dp"
                android:text="5 min"
                android:textColor="@color/primary_light_blue_dark"
                android:textSize="10sp"
                android:textStyle="bold"
                tools:text="5 min" />

            <!-- Completion Status -->
            <ImageView
                android:id="@+id/completion_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_gravity="top|start"
                android:layout_margin="8dp"
                android:background="@drawable/header_icon_background"
                android:padding="4dp"
                android:src="@drawable/ic_check"
                android:tint="@color/success_green"
                android:visibility="gone"
                tools:visibility="visible" />

        </FrameLayout>

        <!-- Test Info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- Test Name -->
            <TextView
                android:id="@+id/test_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Memory Assessment"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="Memory Assessment" />

            <!-- Category -->
            <TextView
                android:id="@+id/test_category"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:gravity="center"
                android:text="Cognitive"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="Cognitive" />

            <!-- Last Score (if completed) -->
            <TextView
                android:id="@+id/last_score"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="center"
                android:text="Last Score: 85%"
                android:textColor="@color/primary_light_blue"
                android:textSize="12sp"
                android:textStyle="bold"
                android:visibility="gone"
                tools:text="Last Score: 85%"
                tools:visibility="visible" />

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
