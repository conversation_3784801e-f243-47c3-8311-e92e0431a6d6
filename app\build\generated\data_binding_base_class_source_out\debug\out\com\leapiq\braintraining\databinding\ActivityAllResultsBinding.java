// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityAllResultsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final LinearLayout emptyStateContainer;

  @NonNull
  public final RecyclerView resultsRecycler;

  @NonNull
  public final Toolbar toolbar;

  private ActivityAllResultsBinding(@NonNull LinearLayout rootView,
      @NonNull LinearLayout emptyStateContainer, @NonNull RecyclerView resultsRecycler,
      @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.emptyStateContainer = emptyStateContainer;
    this.resultsRecycler = resultsRecycler;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityAllResultsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityAllResultsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_all_results, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityAllResultsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.empty_state_container;
      LinearLayout emptyStateContainer = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateContainer == null) {
        break missingId;
      }

      id = R.id.results_recycler;
      RecyclerView resultsRecycler = ViewBindings.findChildViewById(rootView, id);
      if (resultsRecycler == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityAllResultsBinding((LinearLayout) rootView, emptyStateContainer,
          resultsRecycler, toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
