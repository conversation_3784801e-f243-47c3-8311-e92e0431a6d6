<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_progress" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\fragment_progress.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_progress_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="288" endOffset="14"/></Target><Target tag="layout/fragment_progress_0" include="layout_header"><Expressions/><location startLine="11" startOffset="4" endLine="11" endOffset="45"/></Target><Target id="@+id/profile_picture" view="ImageView"><Expressions/><location startLine="41" startOffset="20" endLine="49" endOffset="71"/></Target><Target id="@+id/username" view="TextView"><Expressions/><location startLine="59" startOffset="24" endLine="66" endOffset="54"/></Target><Target id="@+id/user_level" view="TextView"><Expressions/><location startLine="69" startOffset="24" endLine="78" endOffset="51"/></Target><Target id="@+id/max_streak" view="TextView"><Expressions/><location startLine="87" startOffset="28" endLine="94" endOffset="59"/></Target><Target id="@+id/current_streak" view="TextView"><Expressions/><location startLine="104" startOffset="28" endLine="112" endOffset="62"/></Target><Target id="@+id/total_score" view="TextView"><Expressions/><location startLine="132" startOffset="24" endLine="140" endOffset="54"/></Target><Target id="@+id/overall_accuracy" view="TextView"><Expressions/><location startLine="172" startOffset="20" endLine="181" endOffset="59"/></Target><Target id="@+id/category_accuracy_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="184" startOffset="20" endLine="190" endOffset="73"/></Target><Target id="@+id/games_played_week" view="TextView"><Expressions/><location startLine="219" startOffset="20" endLine="226" endOffset="65"/></Target><Target id="@+id/achievements_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="271" startOffset="20" endLine="278" endOffset="67"/></Target></Targets></Layout>