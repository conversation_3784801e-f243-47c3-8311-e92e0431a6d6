<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_tests" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\fragment_tests.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/fragment_tests_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="100" endOffset="14"/></Target><Target tag="layout/fragment_tests_0" include="layout_header"><Expressions/><location startLine="11" startOffset="4" endLine="11" endOffset="45"/></Target><Target id="@+id/btn_my_results" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="21" startOffset="8" endLine="35" endOffset="49"/></Target><Target id="@+id/tab_all_tests" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="53" startOffset="12" endLine="62" endOffset="41"/></Target><Target id="@+id/tab_cognitive" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="64" startOffset="12" endLine="73" endOffset="41"/></Target><Target id="@+id/tab_personality" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="75" startOffset="12" endLine="83" endOffset="41"/></Target><Target id="@+id/tests_recycler" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="90" startOffset="4" endLine="98" endOffset="49"/></Target></Targets></Layout>