// Generated by view binder compiler. Do not edit!
package com.leapiq.braintraining.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.leapiq.braintraining.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMyResultsBinding implements ViewBinding {
  @NonNull
  private final LinearLayout rootView;

  @NonNull
  public final TextView avgCognitiveScoreText;

  @NonNull
  public final TextView avgPersonalityScoreText;

  @NonNull
  public final MaterialButton btnAnalytics;

  @NonNull
  public final MaterialButton btnExportData;

  @NonNull
  public final MaterialButton btnViewAllResults;

  @NonNull
  public final LinearLayout cognitiveScoreContainer;

  @NonNull
  public final TextView cognitiveTestsText;

  @NonNull
  public final LinearLayout contentContainer;

  @NonNull
  public final TextView decliningTestsText;

  @NonNull
  public final LinearLayout emptyStateContainer;

  @NonNull
  public final TextView improvingTestsText;

  @NonNull
  public final TextView insightAction;

  @NonNull
  public final TextView insightDescription;

  @NonNull
  public final View insightPriorityIndicator;

  @NonNull
  public final TextView insightTitle;

  @NonNull
  public final MaterialCardView insightsContainer;

  @NonNull
  public final ProgressBar overallProgressBar;

  @NonNull
  public final TextView overallProgressText;

  @NonNull
  public final LinearLayout personalityScoreContainer;

  @NonNull
  public final TextView personalityTestsText;

  @NonNull
  public final RecyclerView recentActivityRecycler;

  @NonNull
  public final TextView stableTestsText;

  @NonNull
  public final RecyclerView testProgressRecycler;

  @NonNull
  public final Toolbar toolbar;

  @NonNull
  public final TextView totalTestsText;

  private ActivityMyResultsBinding(@NonNull LinearLayout rootView,
      @NonNull TextView avgCognitiveScoreText, @NonNull TextView avgPersonalityScoreText,
      @NonNull MaterialButton btnAnalytics, @NonNull MaterialButton btnExportData,
      @NonNull MaterialButton btnViewAllResults, @NonNull LinearLayout cognitiveScoreContainer,
      @NonNull TextView cognitiveTestsText, @NonNull LinearLayout contentContainer,
      @NonNull TextView decliningTestsText, @NonNull LinearLayout emptyStateContainer,
      @NonNull TextView improvingTestsText, @NonNull TextView insightAction,
      @NonNull TextView insightDescription, @NonNull View insightPriorityIndicator,
      @NonNull TextView insightTitle, @NonNull MaterialCardView insightsContainer,
      @NonNull ProgressBar overallProgressBar, @NonNull TextView overallProgressText,
      @NonNull LinearLayout personalityScoreContainer, @NonNull TextView personalityTestsText,
      @NonNull RecyclerView recentActivityRecycler, @NonNull TextView stableTestsText,
      @NonNull RecyclerView testProgressRecycler, @NonNull Toolbar toolbar,
      @NonNull TextView totalTestsText) {
    this.rootView = rootView;
    this.avgCognitiveScoreText = avgCognitiveScoreText;
    this.avgPersonalityScoreText = avgPersonalityScoreText;
    this.btnAnalytics = btnAnalytics;
    this.btnExportData = btnExportData;
    this.btnViewAllResults = btnViewAllResults;
    this.cognitiveScoreContainer = cognitiveScoreContainer;
    this.cognitiveTestsText = cognitiveTestsText;
    this.contentContainer = contentContainer;
    this.decliningTestsText = decliningTestsText;
    this.emptyStateContainer = emptyStateContainer;
    this.improvingTestsText = improvingTestsText;
    this.insightAction = insightAction;
    this.insightDescription = insightDescription;
    this.insightPriorityIndicator = insightPriorityIndicator;
    this.insightTitle = insightTitle;
    this.insightsContainer = insightsContainer;
    this.overallProgressBar = overallProgressBar;
    this.overallProgressText = overallProgressText;
    this.personalityScoreContainer = personalityScoreContainer;
    this.personalityTestsText = personalityTestsText;
    this.recentActivityRecycler = recentActivityRecycler;
    this.stableTestsText = stableTestsText;
    this.testProgressRecycler = testProgressRecycler;
    this.toolbar = toolbar;
    this.totalTestsText = totalTestsText;
  }

  @Override
  @NonNull
  public LinearLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMyResultsBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMyResultsBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_my_results, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMyResultsBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.avg_cognitive_score_text;
      TextView avgCognitiveScoreText = ViewBindings.findChildViewById(rootView, id);
      if (avgCognitiveScoreText == null) {
        break missingId;
      }

      id = R.id.avg_personality_score_text;
      TextView avgPersonalityScoreText = ViewBindings.findChildViewById(rootView, id);
      if (avgPersonalityScoreText == null) {
        break missingId;
      }

      id = R.id.btn_analytics;
      MaterialButton btnAnalytics = ViewBindings.findChildViewById(rootView, id);
      if (btnAnalytics == null) {
        break missingId;
      }

      id = R.id.btn_export_data;
      MaterialButton btnExportData = ViewBindings.findChildViewById(rootView, id);
      if (btnExportData == null) {
        break missingId;
      }

      id = R.id.btn_view_all_results;
      MaterialButton btnViewAllResults = ViewBindings.findChildViewById(rootView, id);
      if (btnViewAllResults == null) {
        break missingId;
      }

      id = R.id.cognitive_score_container;
      LinearLayout cognitiveScoreContainer = ViewBindings.findChildViewById(rootView, id);
      if (cognitiveScoreContainer == null) {
        break missingId;
      }

      id = R.id.cognitive_tests_text;
      TextView cognitiveTestsText = ViewBindings.findChildViewById(rootView, id);
      if (cognitiveTestsText == null) {
        break missingId;
      }

      id = R.id.content_container;
      LinearLayout contentContainer = ViewBindings.findChildViewById(rootView, id);
      if (contentContainer == null) {
        break missingId;
      }

      id = R.id.declining_tests_text;
      TextView decliningTestsText = ViewBindings.findChildViewById(rootView, id);
      if (decliningTestsText == null) {
        break missingId;
      }

      id = R.id.empty_state_container;
      LinearLayout emptyStateContainer = ViewBindings.findChildViewById(rootView, id);
      if (emptyStateContainer == null) {
        break missingId;
      }

      id = R.id.improving_tests_text;
      TextView improvingTestsText = ViewBindings.findChildViewById(rootView, id);
      if (improvingTestsText == null) {
        break missingId;
      }

      id = R.id.insight_action;
      TextView insightAction = ViewBindings.findChildViewById(rootView, id);
      if (insightAction == null) {
        break missingId;
      }

      id = R.id.insight_description;
      TextView insightDescription = ViewBindings.findChildViewById(rootView, id);
      if (insightDescription == null) {
        break missingId;
      }

      id = R.id.insight_priority_indicator;
      View insightPriorityIndicator = ViewBindings.findChildViewById(rootView, id);
      if (insightPriorityIndicator == null) {
        break missingId;
      }

      id = R.id.insight_title;
      TextView insightTitle = ViewBindings.findChildViewById(rootView, id);
      if (insightTitle == null) {
        break missingId;
      }

      id = R.id.insights_container;
      MaterialCardView insightsContainer = ViewBindings.findChildViewById(rootView, id);
      if (insightsContainer == null) {
        break missingId;
      }

      id = R.id.overall_progress_bar;
      ProgressBar overallProgressBar = ViewBindings.findChildViewById(rootView, id);
      if (overallProgressBar == null) {
        break missingId;
      }

      id = R.id.overall_progress_text;
      TextView overallProgressText = ViewBindings.findChildViewById(rootView, id);
      if (overallProgressText == null) {
        break missingId;
      }

      id = R.id.personality_score_container;
      LinearLayout personalityScoreContainer = ViewBindings.findChildViewById(rootView, id);
      if (personalityScoreContainer == null) {
        break missingId;
      }

      id = R.id.personality_tests_text;
      TextView personalityTestsText = ViewBindings.findChildViewById(rootView, id);
      if (personalityTestsText == null) {
        break missingId;
      }

      id = R.id.recent_activity_recycler;
      RecyclerView recentActivityRecycler = ViewBindings.findChildViewById(rootView, id);
      if (recentActivityRecycler == null) {
        break missingId;
      }

      id = R.id.stable_tests_text;
      TextView stableTestsText = ViewBindings.findChildViewById(rootView, id);
      if (stableTestsText == null) {
        break missingId;
      }

      id = R.id.test_progress_recycler;
      RecyclerView testProgressRecycler = ViewBindings.findChildViewById(rootView, id);
      if (testProgressRecycler == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      id = R.id.total_tests_text;
      TextView totalTestsText = ViewBindings.findChildViewById(rootView, id);
      if (totalTestsText == null) {
        break missingId;
      }

      return new ActivityMyResultsBinding((LinearLayout) rootView, avgCognitiveScoreText,
          avgPersonalityScoreText, btnAnalytics, btnExportData, btnViewAllResults,
          cognitiveScoreContainer, cognitiveTestsText, contentContainer, decliningTestsText,
          emptyStateContainer, improvingTestsText, insightAction, insightDescription,
          insightPriorityIndicator, insightTitle, insightsContainer, overallProgressBar,
          overallProgressText, personalityScoreContainer, personalityTestsText,
          recentActivityRecycler, stableTestsText, testProgressRecycler, toolbar, totalTestsText);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
