<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_tube_sort" modulePackage="com.leapiq.braintraining" filePath="app\src\main\res\layout\activity_tube_sort.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.LinearLayout"><Targets><Target tag="layout/activity_tube_sort_0" view="LinearLayout"><Expressions/><location startLine="1" startOffset="0" endLine="176" endOffset="14"/></Target><Target id="@+id/btn_quit" view="ImageButton"><Expressions/><location startLine="22" startOffset="8" endLine="29" endOffset="42"/></Target><Target id="@+id/game_title" view="TextView"><Expressions/><location startLine="32" startOffset="8" endLine="41" endOffset="38"/></Target><Target id="@+id/btn_menu" view="ImageButton"><Expressions/><location startLine="44" startOffset="8" endLine="51" endOffset="42"/></Target><Target id="@+id/level_text" view="TextView"><Expressions/><location startLine="65" startOffset="8" endLine="74" endOffset="38"/></Target><Target id="@+id/round_text" view="TextView"><Expressions/><location startLine="77" startOffset="8" endLine="86" endOffset="38"/></Target><Target id="@+id/puzzle_text" view="TextView"><Expressions/><location startLine="89" startOffset="8" endLine="98" endOffset="38"/></Target><Target id="@+id/moves_text" view="TextView"><Expressions/><location startLine="101" startOffset="8" endLine="110" endOffset="38"/></Target><Target id="@+id/instruction_text" view="TextView"><Expressions/><location startLine="115" startOffset="4" endLine="124" endOffset="33"/></Target><Target id="@+id/tubes_grid" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="133" startOffset="8" endLine="138" endOffset="48"/></Target><Target id="@+id/btn_undo" view="Button"><Expressions/><location startLine="150" startOffset="8" endLine="160" endOffset="36"/></Target><Target id="@+id/btn_reset" view="Button"><Expressions/><location startLine="162" startOffset="8" endLine="172" endOffset="36"/></Target></Targets></Layout>